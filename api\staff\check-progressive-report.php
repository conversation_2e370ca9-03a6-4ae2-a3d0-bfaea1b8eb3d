<?php
/**
 * Check Progressive Report API
 * Checks if a customer has an existing progressive report
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit;
    }
    
    // Validate required fields
    if (empty($input['customer_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Customer ID is required']);
        exit;
    }
    
    if (empty($input['appointment_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Appointment ID is required']);
        exit;
    }
    
    $customerId = $input['customer_id'];
    $appointmentId = $input['appointment_id'];
    $staffId = $_SESSION['user_id'];
    
    // Verify the appointment belongs to this staff member
    global $database;
    $appointment = $database->fetch("
        SELECT id, user_id, staff_id 
        FROM bookings 
        WHERE id = ? AND staff_id = ?
    ", [$appointmentId, $staffId]);
    
    if (!$appointment) {
        http_response_code(403);
        echo json_encode(['error' => 'Appointment not found or access denied']);
        exit;
    }
    
    // Verify the customer ID matches the appointment
    if ($appointment['user_id'] !== $customerId) {
        http_response_code(400);
        echo json_encode(['error' => 'Customer ID does not match appointment']);
        exit;
    }
    
    // Initialize ProgressiveReport handler
    $progressiveReport = new ProgressiveReport();
    
    // Check if customer has existing report
    $existingReport = $database->fetch("
        SELECT id, title, status, created_at
        FROM progressive_reports 
        WHERE client_id = ? 
        ORDER BY created_at DESC 
        LIMIT 1
    ", [$customerId]);
    
    if ($existingReport) {
        // Customer has existing report
        echo json_encode([
            'success' => true,
            'has_report' => true,
            'report_id' => $existingReport['id'],
            'report_title' => $existingReport['title'],
            'report_status' => $existingReport['status'],
            'message' => 'Existing medical progress report found for this patient'
        ]);
    } else {
        // No existing report
        echo json_encode([
            'success' => true,
            'has_report' => false,
            'message' => 'No existing medical progress report found for this patient'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Check Progressive Report Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error occurred while checking medical reports'
    ]);
}
?>
