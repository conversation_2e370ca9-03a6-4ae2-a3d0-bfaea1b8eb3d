-- Create Rooms Management System
-- Execute this script to add rooms table and update bookings table

USE flix_salonce;

-- Create rooms table
CREATE TABLE IF NOT EXISTS rooms (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type ENUM('TREATMENT', 'CONSULTATION', 'PROCEDURE', 'RECOVERY', 'VIP') NOT NULL DEFAULT 'TREATMENT',
    capacity INT NOT NULL DEFAULT 1,
    description TEXT,
    status ENUM('AVAILABLE', 'UNAVAILABLE', 'MAINTENANCE') NOT NULL DEFAULT 'AVAILABLE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add room_id column to bookings table if it doesn't exist
ALTER TABLE bookings 
ADD COLUMN IF NOT EXISTS room_id VARCHAR(36) NULL,
ADD CONSTRAINT fk_bookings_room 
FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_rooms_status ON rooms(status);
CREATE INDEX IF NOT EXISTS idx_rooms_type ON rooms(type);
CREATE INDEX IF NOT EXISTS idx_bookings_room_date ON bookings(room_id, date, start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_bookings_room_status ON bookings(room_id, status);

-- Insert sample rooms data
INSERT INTO rooms (id, name, type, capacity, description, status) VALUES
(UUID(), 'Treatment Room 1', 'TREATMENT', 1, 'Standard treatment room with advanced medical equipment', 'AVAILABLE'),
(UUID(), 'Treatment Room 2', 'TREATMENT', 1, 'Standard treatment room with advanced medical equipment', 'AVAILABLE'),
(UUID(), 'VIP Suite', 'VIP', 2, 'Luxury VIP treatment suite with premium amenities', 'AVAILABLE'),
(UUID(), 'Consultation Room A', 'CONSULTATION', 1, 'Private consultation room for patient meetings', 'AVAILABLE'),
(UUID(), 'Consultation Room B', 'CONSULTATION', 1, 'Private consultation room for patient meetings', 'AVAILABLE'),
(UUID(), 'Procedure Room', 'PROCEDURE', 1, 'Specialized room for advanced medical procedures', 'AVAILABLE'),
(UUID(), 'Recovery Room', 'RECOVERY', 3, 'Post-treatment recovery area with monitoring equipment', 'AVAILABLE');

-- Create a function to check room availability
DELIMITER //
CREATE OR REPLACE FUNCTION CheckRoomAvailability(
    p_room_id VARCHAR(36),
    p_date DATE,
    p_start_time TIME,
    p_end_time TIME,
    p_exclude_booking_id VARCHAR(36)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE conflict_count INT DEFAULT 0;
    
    -- Check for overlapping confirmed bookings
    SELECT COUNT(*) INTO conflict_count
    FROM bookings 
    WHERE room_id = p_room_id 
    AND date = p_date
    AND status IN ('CONFIRMED', 'IN_PROGRESS')
    AND (
        (start_time < p_end_time AND end_time > p_start_time)
    )
    AND (p_exclude_booking_id IS NULL OR id != p_exclude_booking_id);
    
    RETURN conflict_count = 0;
END //
DELIMITER ;

-- Create procedure to get available rooms for a time slot
DELIMITER //
CREATE OR REPLACE PROCEDURE GetAvailableRooms(
    IN p_date DATE,
    IN p_start_time TIME,
    IN p_end_time TIME,
    IN p_room_type VARCHAR(50),
    IN p_exclude_booking_id VARCHAR(36)
)
BEGIN
    SELECT r.id, r.name, r.type, r.capacity, r.description, r.status
    FROM rooms r
    WHERE r.status = 'AVAILABLE'
    AND (p_room_type IS NULL OR r.type = p_room_type)
    AND CheckRoomAvailability(r.id, p_date, p_start_time, p_end_time, p_exclude_booking_id) = 1
    ORDER BY r.type, r.name;
END //
DELIMITER ;

-- Create procedure to validate room assignment
DELIMITER //
CREATE OR REPLACE PROCEDURE ValidateRoomAssignment(
    IN p_room_id VARCHAR(36),
    IN p_date DATE,
    IN p_start_time TIME,
    IN p_end_time TIME,
    IN p_exclude_booking_id VARCHAR(36),
    OUT p_is_valid BOOLEAN,
    OUT p_error_message TEXT
)
BEGIN
    DECLARE room_status VARCHAR(20);
    DECLARE conflict_count INT DEFAULT 0;
    
    -- Check if room exists and is available
    SELECT status INTO room_status FROM rooms WHERE id = p_room_id;
    
    IF room_status IS NULL THEN
        SET p_is_valid = FALSE;
        SET p_error_message = 'Room not found';
    ELSEIF room_status != 'AVAILABLE' THEN
        SET p_is_valid = FALSE;
        SET p_error_message = CONCAT('Room is currently ', room_status);
    ELSE
        -- Check for conflicts
        SELECT COUNT(*) INTO conflict_count
        FROM bookings 
        WHERE room_id = p_room_id 
        AND date = p_date
        AND status IN ('CONFIRMED', 'IN_PROGRESS')
        AND (start_time < p_end_time AND end_time > p_start_time)
        AND (p_exclude_booking_id IS NULL OR id != p_exclude_booking_id);
        
        IF conflict_count > 0 THEN
            SET p_is_valid = FALSE;
            SET p_error_message = 'Selected room is already booked for this time. Please assign a different room.';
        ELSE
            SET p_is_valid = TRUE;
            SET p_error_message = NULL;
        END IF;
    END IF;
END //
DELIMITER ;

-- Add system setting to track room management features
INSERT INTO system_settings (setting_key, setting_value) VALUES 
('rooms_management_enabled', '1'),
('require_room_assignment', '1')
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = CURRENT_TIMESTAMP;

COMMIT;
