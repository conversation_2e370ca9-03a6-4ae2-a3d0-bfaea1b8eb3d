<?php
/**
 * Authentication Debug Script
 * Use this to debug authentication issues
 */

require_once __DIR__ . '/config/app.php';

// Set content type
header('Content-Type: application/json');

try {
    $debug = [
        'timestamp' => date('Y-m-d H:i:s'),
        'session_status' => session_status(),
        'session_id' => session_id(),
        'session_name' => session_name(),
        'cookie_params' => session_get_cookie_params(),
        'session_data' => $_SESSION,
        'cookies' => $_COOKIE,
        'server_info' => [
            'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'not set',
            'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'not set',
            'HTTPS' => $_SERVER['HTTPS'] ?? 'not set',
            'HTTP_X_FORWARDED_PROTO' => $_SERVER['HTTP_X_FORWARDED_PROTO'] ?? 'not set',
            'SERVER_PORT' => $_SERVER['SERVER_PORT'] ?? 'not set',
        ],
        'auth_check' => [
            'has_auth_object' => isset($auth),
            'is_logged_in' => isLoggedIn(),
            'current_user' => getCurrentUser(),
        ]
    ];

    // Check database sessions if user is logged in
    if (isset($_SESSION['user_id']) && isset($_SESSION['session_token'])) {
        try {
            $session = $database->fetch(
                "SELECT * FROM sessions WHERE session_token = ?",
                [$_SESSION['session_token']]
            );
            
            $debug['database_session'] = $session;
            
            if ($session) {
                $debug['session_expired'] = strtotime($session['expires']) < time();
                $debug['expires_in_seconds'] = strtotime($session['expires']) - time();
            }
        } catch (Exception $e) {
            $debug['database_error'] = $e->getMessage();
        }
    }

    echo json_encode($debug, JSON_PRETTY_PRINT);

} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], JSON_PRETTY_PRINT);
}
?>
