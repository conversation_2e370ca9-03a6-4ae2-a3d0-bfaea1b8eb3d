<?php
require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/customer_panel_functions.php';
require_once __DIR__ . '/../includes/wishlist_functions.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'remove_item':
                    $itemType = $_POST['item_type'];
                    $itemId = $_POST['item_id'];
                    removeFromWishlist($_SESSION['user_id'], $itemType, $itemId);
                    $message = 'Item removed from wishlist successfully!';
                    $messageType = 'success';
                    break;

                case 'clear_wishlist':
                    clearUserWishlist($_SESSION['user_id']);
                    $message = 'Wishlist cleared successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

$customerId = $_SESSION['user_id'];

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// Get wishlist items
$wishlistItems = getUserWishlist($customerId, $limit, $offset);
$totalItems = getWishlistCount($customerId);
$totalPages = ceil($totalItems / $limit);

$pageTitle = "My Wishlist";

// Include customer header
include __DIR__ . '/../includes/customer_header.php';
?>

<style>
/* Professional Wishlist Page Styles */
.wishlist-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
}

.wishlist-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.wishlist-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.wishlist-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.wishlist-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.wishlist-item:hover::before {
    transform: scaleY(1);
}

.wishlist-item:hover {
    transform: translateX(5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(73, 167, 92, 0.3);
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}

.info-card {
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.info-card:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(73, 167, 92, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #49a75c, #2563eb);
    color: white;
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(73, 167, 92, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.4);
}

.btn-secondary {
    background: rgba(248, 250, 252, 0.9);
    color: #374151;
    border: 1px solid rgba(226, 232, 240, 0.8);
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(73, 167, 92, 0.3);
    color: #49a75c;
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.notification {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.notification.success {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #065f46;
}

.notification.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #991b1b;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    border-radius: 2px;
}

.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    animation: float 6s ease-in-out infinite;
}

.floating-circle:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-circle:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 10%;
    animation-delay: 2s;
}

.floating-circle:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.floating-circle:nth-child(4) {
    width: 100px;
    height: 100px;
    bottom: 30%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.empty-state {
    text-align: center;
    padding: 3rem 2rem;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wishlist-container {
        padding: 1rem;
    }

    .wishlist-card {
        border-radius: 16px;
        margin-bottom: 1rem;
    }

    .wishlist-item {
        padding: 1.5rem;
        border-radius: 16px;
    }

    .service-icon {
        width: 60px;
        height: 60px;
    }

    .info-card {
        padding: 0.75rem;
    }

    .btn-primary, .btn-secondary, .btn-danger {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}
</style>

<!-- Floating Background Elements -->
<div class="floating-elements">
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
</div>

<div class="wishlist-container">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Page Header -->
        <div class="wishlist-card p-8 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-6 lg:mb-0">
                    <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-blue-500/10 text-redolence-green px-6 py-3 rounded-full text-sm font-bold mb-4 backdrop-blur-sm border border-redolence-green/20">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/>
                        </svg>
                        My Wishlist
                    </div>
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                        Saved
                        <span class="bg-gradient-to-r from-redolence-green to-blue-600 bg-clip-text text-transparent">
                            Favorites
                        </span>
                    </h1>
                    <p class="text-xl text-gray-600">
                        <?= $totalItems ?> saved item<?= $totalItems !== 1 ? 's' : '' ?> ready for booking
                    </p>
                </div>

                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="<?= getBasePath() ?>/services" class="btn-secondary">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        Browse Services
                    </a>
                    <?php if ($totalItems > 0): ?>
                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to clear your entire wishlist?')">
                        <input type="hidden" name="action" value="clear_wishlist">
                        <button type="submit" class="btn-danger">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                            Clear All
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="notification <?= $messageType ?>">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <?php if ($messageType === 'success'): ?>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    <?php else: ?>
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    <?php endif; ?>
                </svg>
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Wishlist Items -->
        <?php if (empty($wishlistItems)): ?>
            <div class="wishlist-card">
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <svg class="w-16 h-16 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">Your Wishlist is Empty</h3>
                    <p class="text-xl text-gray-600 mb-8 max-w-md mx-auto">
                        Start adding your favorite services and packages to your wishlist for easy booking later
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?= getBasePath() ?>/services" class="btn-primary">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                            Browse Services
                        </a>
                        <a href="<?= getBasePath() ?>/packages" class="btn-secondary">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                            View Packages
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="space-y-6">
                <?php foreach ($wishlistItems as $item): ?>
                    <div class="wishlist-item">
                        <div class="flex flex-col lg:flex-row gap-6">
                            <!-- Service Icon & Basic Info -->
                            <div class="flex items-start gap-6">
                                <div class="service-icon">
                                    <?php if ($item['item_image']): ?>
                                        <?php
                                        // Check if image is a URL or uploaded file
                                        $imageSrc = filter_var($item['item_image'], FILTER_VALIDATE_URL)
                                            ? $item['item_image']
                                            : getBasePath() . '/uploads/' . $item['item_image'];
                                        ?>
                                        <img src="<?= htmlspecialchars($imageSrc) ?>" alt="<?= htmlspecialchars($item['item_name']) ?>" class="w-full h-full object-cover rounded-full" />
                                    <?php else: ?>
                                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <?php if ($item['item_type'] === 'service'): ?>
                                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            <?php else: ?>
                                                <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"/>
                                            <?php endif; ?>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?= ucfirst($item['item_type']) ?>
                                        </span>
                                        <span class="text-sm text-gray-500">
                                            Added <?= date('M j, Y', strtotime($item['created_at'])) ?>
                                        </span>
                                    </div>
                                    <h3 class="text-xl font-bold text-gray-900 mb-2"><?= htmlspecialchars($item['item_name']) ?></h3>
                                    <?php if ($item['item_description']): ?>
                                        <p class="text-gray-600 mb-3"><?= htmlspecialchars($item['item_description']) ?></p>
                                    <?php endif; ?>
                                    <div class="flex items-center gap-4 text-sm text-gray-500">
                                        <div class="flex items-center gap-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <?= $item['item_duration'] ?> minutes
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                            </svg>
                                            <?= htmlspecialchars($item['item_category']) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing & Actions -->
                            <div class="flex flex-col lg:items-end gap-4">
                                <div class="info-card">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-redolence-green mb-1">
                                            <?= formatCurrency($item['item_price']) ?>
                                        </div>
                                        <div class="text-sm text-gray-500">Total Price</div>
                                    </div>
                                </div>

                                <div class="flex flex-col sm:flex-row lg:flex-col gap-3">
                                    <a href="<?= getBasePath() ?>/customer/book?<?= $item['item_type'] ?>=<?= $item['item_id'] ?>" class="btn-primary">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                        </svg>
                                        Book Now
                                    </a>
                                    <form method="POST" class="inline" onsubmit="return confirm('Remove this item from your wishlist?')">
                                        <input type="hidden" name="action" value="remove_item">
                                        <input type="hidden" name="item_type" value="<?= $item['item_type'] ?>">
                                        <input type="hidden" name="item_id" value="<?= $item['item_id'] ?>">
                                        <button type="submit" class="btn-secondary w-full">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                            Remove
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center space-x-2">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?= $page - 1 ?>"
                               class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                </svg>
                            </a>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <a href="?page=<?= $i ?>"
                               class="px-4 py-2 <?= $i === $page ? 'bg-redolence-green text-white' : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50' ?> rounded-lg transition-colors font-medium">
                                <?= $i ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <a href="?page=<?= $page + 1 ?>"
                               class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
// Enhanced wishlist page interactions
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll for notifications
    const notification = document.querySelector('.notification');
    if (notification) {
        notification.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Auto-hide notification after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    // Enhanced hover effects for wishlist items
    const wishlistItems = document.querySelectorAll('.wishlist-item');
    wishlistItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // Smooth animations on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all wishlist items for scroll animations
    wishlistItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });

    // Form submission loading states
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Processing...
                `;
            }
        });
    });
});

// Show toast notification
function showToast(message, type = 'success') {
    // Remove any existing toast first
    const existingToast = document.getElementById('toast');
    if (existingToast) {
        existingToast.remove();
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.id = 'toast';
    toast.className = `fixed top-4 right-4 z-50 flex items-center p-4 rounded-xl shadow-lg transform translate-x-full transition-all duration-300 ${
        type === 'success'
            ? 'bg-green-500 text-white'
            : 'bg-red-500 text-white'
    }`;

    toast.innerHTML = `
        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
            ${type === 'success'
                ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>'
                : '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>'
            }
        </svg>
        <span class="font-medium">${message}</span>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
        toast.style.transform = 'translateX(full)';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}
</script>

<?php include __DIR__ . '/../includes/customer_footer.php'; ?>
