<?php
/**
 * Get Service Details API
 * Returns detailed information about a specific service
 */

require_once __DIR__ . '/../../config/app.php';

header('Content-Type: application/json');

try {
    // Check if service ID is provided
    if (!isset($_GET['id']) || empty($_GET['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Service ID is required']);
        exit;
    }

    $serviceId = sanitize($_GET['id']);

    // Fetch service details
    $service = $database->fetch(
        "SELECT * FROM services WHERE id = ? AND is_active = 1",
        [$serviceId]
    );

    if (!$service) {
        http_response_code(404);
        echo json_encode(['error' => 'Service not found']);
        exit;
    }

    // Handle missing fields with defaults
    $service['featured'] = isset($service['featured']) ? (bool)$service['featured'] : false;
    $service['popular'] = isset($service['popular']) ? (bool)$service['popular'] : false;
    $service['new_treatment'] = isset($service['new_treatment']) ? (bool)$service['new_treatment'] : false;
    $service['is_active'] = (bool)$service['is_active'];
    $service['session_frequency'] = $service['session_frequency'] ?? '';
    $service['technology_used'] = $service['technology_used'] ?? '';

    // Convert numeric fields
    $service['price'] = $service['price'] ? (float)$service['price'] : null;
    $service['duration'] = $service['duration'] ? (int)$service['duration'] : null;
    $service['sort_order'] = isset($service['sort_order']) ? (int)$service['sort_order'] : 0;

    // Fix HTML entities in name and description (decode &amp; back to & and &lt; back to <)
    $service['name'] = html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8');
    $service['description'] = html_entity_decode($service['description'], ENT_QUOTES, 'UTF-8');

    // Return service data with JSON_UNESCAPED_UNICODE to preserve special characters
    echo json_encode($service, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

} catch (Exception $e) {
    error_log("Service API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
