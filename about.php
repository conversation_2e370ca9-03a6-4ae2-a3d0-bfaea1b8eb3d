<?php
/**
 * About Page - Radical Redesign
 * Redolence Medi Aesthetics - Advanced Medical Aesthetics Center
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "About Redolence Medi Aesthetics - Advanced Medical Beauty";
$pageDescription = "Discover Redolence Medi Aesthetics, where cutting-edge medical science meets aesthetic artistry. Our board-certified specialists deliver transformative results through advanced medical treatments.";

include __DIR__ . '/includes/header.php';
?>

<!-- Critical CSS for immediate rendering -->
<style>
/* Revolutionary Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
}

/* Advanced Animation Framework */
@keyframes morphingGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes floatingElement {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(-10px) rotate(-3deg); }
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(73, 167, 92, 0.3); }
    50% { box-shadow: 0 0 40px rgba(88, 148, 210, 0.5); }
}

@keyframes slideInFromLeft {
    0% { opacity: 0; transform: translateX(-100px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideInFromRight {
    0% { opacity: 0; transform: translateX(100px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

/* Revolutionary Card System */
.medical-card-revolutionary {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    backdrop-filter: blur(20px);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.medical-card-revolutionary::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--gradient-primary);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.medical-card-revolutionary:hover::before {
    opacity: 0;
}

.medical-card-revolutionary:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Morphing Background System */
.morphing-bg {
    background: linear-gradient(-45deg, #49a75c, #5894d2, #6ba3d6, #4db86d);
    background-size: 400% 400%;
    animation: morphingGradient 8s ease infinite;
}

/* Advanced Typography */
.text-revolutionary {
    background: var(--gradient-primary);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: morphingGradient 6s ease infinite;
}

/* Floating Elements System */
.floating-medical {
    animation: floatingElement 6s ease-in-out infinite;
}

.floating-medical:nth-child(2) { animation-delay: 2s; }
.floating-medical:nth-child(3) { animation-delay: 4s; }

/* Interactive Hover States */
.interactive-element {
    transition: all 0.3s ease;
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-3px);
}

/* Advanced Grid System */
.medical-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Scroll-triggered Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Medical Professional Styling */
.medical-professional {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(15px);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.5s ease;
}

.medical-professional:hover {
    transform: translateY(-10px) rotateY(5deg);
    box-shadow: 0 25px 50px rgba(73, 167, 92, 0.2);
}

/* Revolutionary Button System */
.btn-revolutionary {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.btn-revolutionary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn-revolutionary:hover::before {
    left: 100%;
}

.btn-revolutionary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(73, 167, 92, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .medical-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .medical-card-revolutionary {
        margin: 0;
        padding: 1rem;
    }
    
    /* Mobile Hero Section */
    .relative.min-h-screen h1 {
        font-size: 3rem !important;
        line-height: 1.1;
    }
    
    .relative.min-h-screen h1 span {
        font-size: 2.5rem !important;
    }
    
    .relative.min-h-screen p {
        font-size: 1.1rem !important;
    }
    
    .relative.min-h-screen p span {
        font-size: 1rem !important;
    }
    
    /* Mobile Section Headings */
    h2 {
        font-size: 2.5rem !important;
    }
    
    .text-6xl, .text-7xl {
        font-size: 2.5rem !important;
    }
    
    /* Mobile Cards */
    .medical-professional {
        padding: 1.5rem;
    }
    
    .medical-professional .w-40 {
        width: 6rem;
        height: 6rem;
    }
    
    .medical-professional .text-3xl {
        font-size: 1.5rem;
    }
    
    /* Mobile Values Cards */
    .medical-card-revolutionary.rounded-3xl {
        padding: 1.5rem;
    }
    
    .medical-card-revolutionary h3 {
        font-size: 1.5rem;
    }
    
    .medical-card-revolutionary .w-24 {
        width: 4rem;
        height: 4rem;
    }
    
    /* Mobile Sections Padding */
    .py-32 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .py-16 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .py-24 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
}
</style>

<!-- Revolutionary Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Dynamic Background -->
    <div class="absolute inset-0 morphing-bg"></div>
    
    <!-- Floating Medical Elements -->
    <div class="absolute inset-0 pointer-events-none">
        <div class="floating-medical absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute top-40 right-20 w-24 h-24 bg-white/15 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute bottom-32 left-1/4 w-40 h-40 bg-white/8 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute bottom-20 right-10 w-28 h-28 bg-white/12 rounded-full backdrop-blur-sm"></div>
    </div>
    
    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white px-6 max-w-6xl mx-auto">
        <div class="scroll-reveal">
            <div class="inline-flex items-center bg-white/20 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-white/30">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-semibold text-lg">CERTIFIED MEDICAL AESTHETICS CENTER</span>
            </div>
        </div>
        
        <h1 class="text-7xl md:text-9xl font-black mb-8 leading-none scroll-reveal" style="animation-delay: 0.2s;">
            REDOLENCE
            <span class="block text-6xl md:text-8xl font-light opacity-90">Medi Aesthetics</span>
        </h1>
        
        <p class="text-2xl md:text-4xl font-light mb-12 leading-relaxed scroll-reveal" style="animation-delay: 0.4s;">
            Where <strong>Medical Science</strong> meets <strong>Aesthetic Artistry</strong>
            <span class="block mt-4 text-xl md:text-2xl opacity-80">Transforming lives through advanced medical treatments</span>
        </p>
        
        <div class="flex flex-col md:flex-row gap-6 justify-center items-center scroll-reveal" style="animation-delay: 0.6s;">
            <button class="btn-revolutionary interactive-element">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Schedule Consultation
            </button>
            <button class="bg-white/20 backdrop-blur-sm border-2 border-white/50 text-white px-8 py-4 rounded-full font-semibold hover:bg-white/30 transition-all interactive-element">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Virtual Tour
            </button>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Revolutionary Story Section -->
<section class="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 75% 75%, var(--primary-blue) 2px, transparent 2px); background-size: 80px 80px;"></div>
    </div>
    
    <div class="max-w-7xl mx-auto px-6 relative">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
            <!-- Content -->
            <div class="scroll-reveal">
                <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 px-6 py-3 rounded-full mb-8 border border-redolence-green/20">
                    <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <span class="font-semibold text-redolence-green">OUR REVOLUTIONARY APPROACH</span>
                </div>
                
                <h2 class="text-6xl md:text-7xl font-black mb-8 leading-tight">
                    Pioneering
                    <span class="text-revolutionary block">Medical Excellence</span>
                </h2>
                
                <div class="space-y-8 text-xl leading-relaxed text-gray-700">
                    <p class="text-2xl font-light">
                        At Redolence Medi Aesthetics, we've revolutionized the beauty industry by combining 
                        <strong class="text-redolence-green">advanced medical science</strong> with 
                        <strong class="text-redolence-blue">artistic precision</strong>.
                    </p>
                    
                    <p>
                        Our state-of-the-art facility houses the latest FDA-approved technologies, operated by 
                        board-certified physicians and licensed medical professionals who understand that true 
                        beauty enhancement requires both medical expertise and aesthetic vision.
                    </p>
                    
                    <p>
                        Every treatment is personalized using advanced diagnostic tools and 3D imaging technology, 
                        ensuring results that enhance your natural features while maintaining the highest safety standards.
                    </p>
                </div>
                
                <!-- Stats Grid -->
                <div class="grid grid-cols-2 gap-8 mt-12">
                    <div class="medical-card-revolutionary rounded-2xl p-6 text-center">
                        <div class="text-4xl font-black text-redolence-green mb-2">15,000+</div>
                        <div class="text-gray-600 font-medium">Successful Procedures</div>
                    </div>
                    <div class="medical-card-revolutionary rounded-2xl p-6 text-center">
                        <div class="text-4xl font-black text-redolence-blue mb-2">98.7%</div>
                        <div class="text-gray-600 font-medium">Patient Satisfaction</div>
                    </div>
                </div>
            </div>
            
            <!-- Visual Element -->
            <div class="scroll-reveal" style="animation-delay: 0.3s;">
                <div class="relative">
                    <!-- Main Image Container -->
                    <div class="medical-card-revolutionary rounded-3xl p-8 aspect-square flex items-center justify-center relative overflow-hidden">
                        <!-- Background Image -->
                        <div class="absolute inset-0 opacity-20">
                            <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                                 alt="Medical Aesthetics" class="w-full h-full object-cover">
                        </div>
                        
                        <!-- Center Logo -->
                        <div class="relative z-10 text-center">
                            <div class="w-48 h-48 bg-white rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
                                <img src="<?= getBasePath() ?>/includes/redolence_logo.png" 
                                     alt="Redolence Logo" class="w-32 h-32 object-contain">
                            </div>
                            <h3 class="text-2xl font-bold text-gray-800 mb-2">Advanced Medical Facility</h3>
                            <p class="text-redolence-green font-semibold">State-of-the-Art Technology</p>
                        </div>
                        
                        <!-- Floating Elements -->
                        <div class="absolute top-8 right-8 w-12 h-12 bg-redolence-green/20 rounded-full floating-medical"></div>
                        <div class="absolute bottom-8 left-8 w-16 h-16 bg-redolence-blue/20 rounded-full floating-medical"></div>
                    </div>
                    
                    <!-- Decorative Elements -->
                    <div class="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full blur-xl"></div>
                    <div class="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-br from-redolence-blue/20 to-redolence-green/20 rounded-full blur-2xl"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Values Section -->
<section class="py-32 bg-gradient-to-br from-redolence-green/5 via-white to-redolence-blue/5 relative">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-20 scroll-reveal">
            <div class="inline-flex items-center bg-white/80 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-redolence-green/20">
                <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                <span class="font-bold text-lg">OUR CORE VALUES</span>
            </div>
            
            <h2 class="text-6xl md:text-7xl font-black mb-8 leading-tight">
                What Drives
                <span class="text-revolutionary block">Our Excellence</span>
            </h2>
            
            <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Every decision, every treatment, every interaction is guided by our unwavering commitment to these fundamental principles
            </p>
        </div>
        
        <!-- Values Grid -->
        <div class="medical-grid">
            <!-- Value 1 -->
            <div class="medical-card-revolutionary rounded-3xl p-10 text-center scroll-reveal interactive-element" style="animation-delay: 0.1s;">
                <div class="w-24 h-24 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                    <svg class="w-12 h-12 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-redolence-green/30 rounded-full animate-pulse"></div>
                </div>
                <h3 class="text-3xl font-bold text-gray-900 mb-6">Medical Safety First</h3>
                <p class="text-gray-600 leading-relaxed text-lg">
                    We prioritize patient safety above all else, using only FDA-approved treatments, 
                    medical-grade products, and following the strictest clinical protocols in the industry.
                </p>
            </div>
            
            <!-- Value 2 -->
            <div class="medical-card-revolutionary rounded-3xl p-10 text-center scroll-reveal interactive-element" style="animation-delay: 0.2s;">
                <div class="w-24 h-24 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                    <svg class="w-12 h-12 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-redolence-blue/30 rounded-full animate-pulse"></div>
                </div>
                <h3 class="text-3xl font-bold text-gray-900 mb-6">Scientific Innovation</h3>
                <p class="text-gray-600 leading-relaxed text-lg">
                    We stay at the forefront of medical aesthetics through continuous research, 
                    advanced training, and investment in cutting-edge technology and techniques.
                </p>
            </div>
            
            <!-- Value 3 -->
            <div class="medical-card-revolutionary rounded-3xl p-10 text-center scroll-reveal interactive-element" style="animation-delay: 0.3s;">
                <div class="w-24 h-24 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                    <svg class="w-12 h-12 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-purple-500/30 rounded-full animate-pulse"></div>
                </div>
                <h3 class="text-3xl font-bold text-gray-900 mb-6">Personalized Care</h3>
                <p class="text-gray-600 leading-relaxed text-lg">
                    Every patient receives a customized treatment plan based on their unique anatomy, 
                    aesthetic goals, and medical history for optimal, natural-looking results.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Team Section -->
<section class="py-32 bg-white relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-redolence-green/10 to-transparent rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-redolence-blue/10 to-transparent rounded-full blur-3xl"></div>
    
    <div class="max-w-7xl mx-auto px-6 relative">
        <!-- Section Header -->
        <div class="text-center mb-20 scroll-reveal">
            <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 px-8 py-4 rounded-full mb-8 border border-redolence-green/20">
                <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span class="font-bold text-lg">MEET OUR SPECIALISTS</span>
            </div>
            
            <h2 class="text-6xl md:text-7xl font-black mb-8 leading-tight">
                World-Class
                <span class="text-revolutionary block">Medical Team</span>
            </h2>
            
            <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Board-certified physicians and licensed medical professionals dedicated to your transformation
            </p>
        </div>
        
        <!-- Team Grid -->
        <div class="medical-grid">
            <?php
            // Include staff functions
            require_once __DIR__ . '/includes/staff_functions.php';
            
            // Get active staff members
            $staffMembers = getActiveStaff();
            
            foreach ($staffMembers as $index => $staff): 
                $delay = ($index * 0.1) + 0.1;
            ?>
            <div class="medical-professional scroll-reveal interactive-element" style="animation-delay: <?= $delay ?>s;">
                <!-- Profile Image -->
                <div class="relative mb-8">
                    <div class="w-40 h-40 bg-gradient-to-br from-redolence-green/30 via-redolence-blue/20 to-purple-500/20 rounded-full flex items-center justify-center mx-auto relative overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 opacity-30">
                            <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                                 alt="Medical Professional" class="w-full h-full object-cover">
                        </div>
                        
                        <!-- Initials -->
                        <span class="text-white font-black text-3xl relative z-10">
                            <?php 
                            $nameParts = explode(' ', $staff['name']);
                            echo strtoupper(substr($nameParts[0], 0, 1)) . (isset($nameParts[1]) ? strtoupper(substr($nameParts[1], 0, 1)) : '');
                            ?>
                        </span>
                        
                        <!-- Decorative Ring -->
                        <div class="absolute inset-0 border-4 border-white/50 rounded-full"></div>
                    </div>
                    
                    <!-- Medical Badge -->
                    <div class="absolute -top-2 -right-2 w-12 h-12 bg-redolence-green rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                
                <!-- Professional Info -->
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Dr. <?= $staff['name'] ?></h3>
                    <p class="text-redolence-blue font-semibold text-lg mb-4">Board-Certified Medical Specialist</p>
                    
                    <!-- Credentials -->
                    <div class="flex flex-wrap justify-center gap-2 mb-6">
                        <span class="bg-redolence-green/15 text-redolence-green px-3 py-1 rounded-full text-sm font-medium">MD</span>
                        <span class="bg-redolence-blue/15 text-redolence-blue px-3 py-1 rounded-full text-sm font-medium">Aesthetic Medicine</span>
                        <span class="bg-purple-500/15 text-purple-600 px-3 py-1 rounded-full text-sm font-medium">15+ Years</span>
                    </div>
                    
                    <!-- Bio -->
                    <p class="text-gray-600 leading-relaxed mb-6">
                        Specialized in advanced medical aesthetics with extensive training in facial anatomy, 
                        non-surgical procedures, and patient safety protocols.
                    </p>
                    
                    <!-- Action Button -->
                    <button class="btn-revolutionary w-full">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Book with Dr. <?= explode(' ', $staff['name'])[0] ?>
                    </button>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 md:py-24 bg-redolence-green">
    <div class="max-w-4xl mx-auto px-4 md:px-6 text-center text-white">
        <h2 class="text-3xl md:text-5xl font-bold mb-6 leading-tight">
            Ready to Transform Your Beauty Journey?
        </h2>
        
        <p class="text-lg md:text-xl mb-8 opacity-90">
            Experience the future of medical aesthetics with our revolutionary treatments
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-2xl mx-auto">
            <a href="contact.php" class="w-full sm:w-auto bg-white text-redolence-green px-6 py-3 rounded-lg font-semibold text-center hover:bg-gray-100 transition-colors">
                Schedule Your Consultation
            </a>
            
            <a href="tel:+255781985757" class="w-full sm:w-auto border-2 border-white text-white px-6 py-3 rounded-lg font-semibold text-center hover:bg-white hover:text-redolence-green transition-colors">
                Call: +255 781 985 757
            </a>
        </div>
    </div>
</section>

<!-- Revolutionary JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Advanced Scroll Reveal System
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                
                // Add staggered animation for grid items
                if (entry.target.classList.contains('medical-grid')) {
                    const children = entry.target.children;
                    Array.from(children).forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('revealed');
                        }, index * 100);
                    });
                }
            }
        });
    }, observerOptions);
    
    // Observe all scroll reveal elements
    document.querySelectorAll('.scroll-reveal').forEach(el => {
        observer.observe(el);
    });
    
    // Simplified Interactive Elements (except hero section)
    document.querySelectorAll('.interactive-element').forEach(element => {
        // Only apply complex effects to hero section elements
        const isInHero = element.closest('.relative.min-h-screen');
        
        if (isInHero) {
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.05)';
                this.style.filter = 'brightness(1.1)';
            });
            
            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.filter = 'brightness(1)';
            });
        } else {
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });
            
            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        }
    });
    
    // Simplified Card Hover Effects
    document.querySelectorAll('.medical-card-revolutionary').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Simplified Medical Professional Hover Effects
    document.querySelectorAll('.medical-professional').forEach(professional => {
        professional.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        professional.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Button Interaction Effects
    document.querySelectorAll('.btn-revolutionary').forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Smooth Scrolling for Internal Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Page Load Animation
    setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transform = 'translateY(0)';
    }, 100);
    
    // Parallax Effect for Hero Section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.morphing-bg');
        
        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    body {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.8s ease;
    }
`;
document.head.appendChild(style);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>