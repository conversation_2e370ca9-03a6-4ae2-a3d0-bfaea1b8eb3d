<?php
/**
 * Enhanced Mobile Menu Component
 * Modern slide-out navigation menu for mobile devices with medical aesthetics styling
 */

// Get current path for active state
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();
?>

<!-- Enhanced Mobile Menu Overlay -->
<div class="lg:hidden fixed inset-0 z-50 hidden" id="mobile-menu-overlay">
    <!-- Background overlay with blur effect -->
    <div class="fixed inset-0 bg-black/60 backdrop-blur-sm transition-all duration-300" id="mobile-menu-backdrop"></div>
    
    <!-- Enhanced Menu Panel -->
    <div class="mobile-menu fixed inset-y-0 left-0 z-50 w-full max-w-sm glass-effect shadow-2xl">
        <div class="flex h-full flex-col">
            <!-- Enhanced Header -->
            <div class="flex items-center justify-between px-6 py-6 border-b border-white/20">
                <div class="flex items-center space-x-4">
                    <div class="logo-icon w-12 h-12 rounded-2xl flex items-center justify-center shadow-xl">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-xl font-heading font-bold gradient-text">Redolence</h2>
                        <p class="text-sm text-gray-600 font-medical -mt-1 tracking-wide">Medi Aesthetics</p>
                    </div>
                </div>
                <button class="hamburger p-3 rounded-xl hover:bg-white/10 transition-all duration-300" id="mobile-menu-close">
                    <div class="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block transform rotate-45 translate-y-1.5"></span>
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block opacity-0"></span>
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block transform -rotate-45 -translate-y-1.5"></span>
                    </div>
                </button>
            </div>

            <!-- Enhanced Navigation -->
            <nav class="flex-1 px-6 py-6 space-y-3 overflow-y-auto">
                <!-- Home -->
                <?php $isActive = $currentPath === $basePath . '/'; ?>
                <a href="<?= $basePath ?>/" 
                   class="mobile-menu-item block px-6 py-4 text-base font-medium rounded-2xl transition-all duration-300 <?= $isActive ? 'bg-gradient-to-r from-redolence-green/20 to-redolence-blue/20 text-redolence-green border-l-4 border-redolence-green' : 'text-gray-700 hover:bg-white/10' ?>">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold">Home</div>
                            <div class="text-xs text-gray-500">Welcome page</div>
                        </div>
                    </div>
                </a>

                <!-- Services -->
                <?php $isServicesActive = $currentPath === $basePath . '/services' || strpos($currentPath, $basePath . '/services') === 0; ?>
                <a href="<?= $basePath ?>/services" 
                   class="mobile-menu-item block px-6 py-4 text-base font-medium rounded-2xl transition-all duration-300 <?= $isServicesActive ? 'bg-gradient-to-r from-redolence-green/20 to-redolence-blue/20 text-redolence-green border-l-4 border-redolence-green' : 'text-gray-700 hover:bg-white/10' ?>">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold">Services</div>
                            <div class="text-xs text-gray-500">Medical treatments</div>
                        </div>
                    </div>
                </a>

                <!-- Enhanced Navigation Items -->
                <?php
                $enhancedNavigation = [
                    [
                        'name' => 'Offers', 
                        'href' => $basePath . '/offers', 
                        'icon' => 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
                        'description' => 'Special deals'
                    ],
                    [
                        'name' => 'Gallery', 
                        'href' => $basePath . '/gallery', 
                        'icon' => 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
                        'description' => 'Before & after'
                    ],
                    [
                        'name' => 'About', 
                        'href' => $basePath . '/about', 
                        'icon' => 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
                        'description' => 'Our story'
                    ],
                    [
                        'name' => 'Contact', 
                        'href' => $basePath . '/contact', 
                        'icon' => 'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z',
                        'description' => 'Get in touch'
                    ],
                ];

                foreach ($enhancedNavigation as $item):
                    $isActive = $currentPath === $item['href'] || ($item['href'] !== $basePath . '/' && strpos($currentPath, $item['href']) === 0);
                ?>
                    <a href="<?= $item['href'] ?>" 
                       class="mobile-menu-item block px-6 py-4 text-base font-medium rounded-2xl transition-all duration-300 <?= $isActive ? 'bg-gradient-to-r from-redolence-green/20 to-redolence-blue/20 text-redolence-green border-l-4 border-redolence-green' : 'text-gray-700 hover:bg-white/10' ?>">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 flex items-center justify-center mr-4">
                                <svg class="w-5 h-5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?= $item['icon'] ?>"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold"><?= $item['name'] ?></div>
                                <div class="text-xs text-gray-500"><?= $item['description'] ?></div>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>

                <!-- Divider -->
                <div class="border-t border-white/20 my-6"></div>

                <!-- User Account Section -->
                <?php if (isLoggedIn()): ?>
                    <?php $user = getCurrentUser(); ?>
                    <div class="px-6 py-4 bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 rounded-2xl">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 rounded-full bg-gradient-to-br from-redolence-green to-redolence-blue flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm"><?= strtoupper(substr($user['name'], 0, 1)) ?></span>
                            </div>
                            <div>
                                <div class="font-semibold text-gray-900"><?= htmlspecialchars(explode(' ', $user['name'])[0]) ?></div>
                                <div class="text-xs text-gray-500"><?= ucfirst(strtolower($user['role'])) ?></div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <a href="<?= $basePath ?>/customer/dashboard" class="block text-sm text-gray-600 hover:text-redolence-green transition-colors">Dashboard</a>
                            <a href="<?= $basePath ?>/customer/appointments" class="block text-sm text-gray-600 hover:text-redolence-green transition-colors">My Appointments</a>
                            <a href="<?= $basePath ?>/auth/logout.php" class="block text-sm text-red-600 hover:text-red-700 transition-colors">Sign Out</a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl">
                        <div class="text-center">
                            <div class="text-sm text-gray-600 mb-3">Join Redolence today</div>
                            <div class="space-y-2">
                                <a href="<?= $basePath ?>/auth/login.php" class="block w-full bg-redolence-green hover:bg-green-600 text-white py-2 px-4 rounded-xl font-semibold transition-all duration-300 text-center">Sign In</a>
                                <a href="<?= $basePath ?>/auth/register.php" class="block w-full border-2 border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white py-2 px-4 rounded-xl font-semibold transition-all duration-300 text-center">Register</a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </nav>

            <!-- Enhanced Footer -->
            <div class="px-6 py-6 border-t border-white/20">
                <a href="<?= getBasePath() ?>/customer/book" 
                   class="cta-button w-full text-white py-4 px-6 rounded-2xl font-semibold transition-all duration-300 shadow-lg text-center inline-block relative overflow-hidden">
                    <svg class="w-5 h-5 mr-2 inline relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                    </svg>
                    <span class="relative z-10">Book Consultation</span>
                </a>
                
                <!-- Contact Info -->
                <div class="mt-4 text-center">
                    <div class="text-xs text-gray-500 mb-2">Need help? Call us</div>
                    <a href="tel:+255123456789" class="text-sm font-semibold text-redolence-green hover:text-redolence-blue transition-colors">
                        +255 123 456 789
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Mobile Menu Styles -->
<style>
/* Mobile Menu Enhancements */
.mobile-menu {
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.open {
    transform: translateX(0);
}

.mobile-menu-item {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.mobile-menu-item:hover {
    background: rgba(73, 167, 92, 0.05);
    border-left-color: #49a75c;
    transform: translateX(8px);
}

/* Hamburger Animation for Close Button */
.hamburger {
    transition: all 0.3s ease;
}

.hamburger-line {
    transition: all 0.3s ease;
    transform-origin: center;
}

/* Smooth Scrolling for Mobile Menu */
.mobile-menu nav {
    scroll-behavior: smooth;
}

/* Enhanced Glass Effect for Mobile */
@supports (backdrop-filter: blur(20px)) {
    .mobile-menu {
        backdrop-filter: blur(20px) saturate(180%);
        background: rgba(255, 255, 255, 0.9);
        border-right: 1px solid rgba(255, 255, 255, 0.3);
    }
}

/* Fallback for browsers without backdrop-filter */
@supports not (backdrop-filter: blur(20px)) {
    .mobile-menu {
        background: rgba(255, 255, 255, 0.95);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
    const mobileMenuClose = document.getElementById('mobile-menu-close');
    const mobileMenuBackdrop = document.getElementById('mobile-menu-backdrop');
    const mobileMenu = document.querySelector('.mobile-menu');
    const hamburgerButton = document.querySelector('.hamburger');

    function openMobileMenu() {
        mobileMenuOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
        if (hamburgerButton) {
            hamburgerButton.classList.add('active');
        }
        setTimeout(() => {
            mobileMenu.classList.add('open');
        }, 10);
    }

    function closeMobileMenu() {
        mobileMenu.classList.remove('open');
        document.body.style.overflow = ''; // Restore scrolling
        if (hamburgerButton) {
            hamburgerButton.classList.remove('active');
        }
        setTimeout(() => {
            mobileMenuOverlay.classList.add('hidden');
        }, 400);
    }

    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', openMobileMenu);
    }

    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', closeMobileMenu);
    }

    if (mobileMenuBackdrop) {
        mobileMenuBackdrop.addEventListener('click', closeMobileMenu);
    }

    // Close menu when clicking on navigation links
    const mobileNavLinks = mobileMenuOverlay.querySelectorAll('a');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });

    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !mobileMenuOverlay.classList.contains('hidden')) {
            closeMobileMenu();
        }
    });

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
});
</script>
