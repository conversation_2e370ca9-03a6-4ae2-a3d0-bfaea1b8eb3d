    <!-- Medical Admin Footer -->
    <footer class="medical-glass border-t border-redolence-green/20 mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6 flex flex-col sm:flex-row justify-between items-center">
                <div class="text-sm text-gray-600">
                    &copy; <?= date('Y') ?> <?= APP_NAME ?> Medical Admin Panel. All rights reserved.
                </div>
                <div class="flex items-center space-x-6 mt-2 sm:mt-0">
                    <span class="text-xs text-gray-500">Version <?= APP_VERSION ?></span>
                    <div class="flex items-center text-xs text-gray-500">
                        <div class="h-2 w-2 bg-redolence-green rounded-full mr-2 animate-pulse"></div>
                        Medical System Online
                    </div>
                    <div class="flex items-center text-xs text-gray-500">
                        <svg class="w-3 h-3 mr-1 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        HIPAA Compliant
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Medical Toast Notifications Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Medical Admin Scripts -->
    <script>
        // Medical toast notification system
        function showToast(message, type = 'success') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = type === 'success' ? 'bg-gradient-to-r from-redolence-green to-green-600' : 
                           type === 'error' ? 'bg-gradient-to-r from-red-500 to-red-600' : 
                           type === 'warning' ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' : 
                           'bg-gradient-to-r from-redolence-blue to-blue-600';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0 medical-glass border border-white/20`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            ${type === 'success' ? 
                                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />' :
                                type === 'error' ?
                                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />' :
                                type === 'warning' ?
                                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />' :
                                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
                            }
                        </svg>
                        <span class="font-medium">${message}</span>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200 transition-colors">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            `;
            
            container.appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
            }, 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }

        // Medical confirm dialog
        function confirmAction(message, callback) {
            if (confirm(`Medical System Confirmation: ${message}`)) {
                callback();
            }
        }

        // Format currency for medical billing (TSH)
        function formatCurrency(amount) {
            return 'TSH ' + parseInt(amount).toLocaleString();
        }

        // Format date for medical records
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Medical AJAX helper
        function makeRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Medical-System': 'Redolence-Admin'
                }
            };
            
            return fetch(url, { ...defaultOptions, ...options })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Medical System Error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('Medical system request failed:', error);

                    // Only show toast if function exists
                    if (typeof showToast === 'function') {
                        showToast('Medical system request failed. Please try again.', 'error');
                    }

                    throw error;
                });
        }

        // Auto-refresh medical data every 30 seconds for dashboard
        if (window.location.pathname === '/admin' || window.location.pathname === '/admin/') {
            setInterval(() => {
                // Refresh medical dashboard stats
                const basePath = '<?= getBasePath() ?>';
                makeRequest(`${basePath}/api/admin/dashboard-stats.php`)
                    .then(data => {
                        // Update medical stats if elements exist
                        const elements = {
                            'total-patients': data.customers,
                            'total-appointments': data.bookings,
                            'total-revenue': formatCurrency(data.revenue),
                            'pending-appointments': data.pending_bookings
                        };

                        Object.entries(elements).forEach(([id, value]) => {
                            const element = document.getElementById(id);
                            if (element) element.textContent = value;
                        });
                    })
                    .catch(() => {
                        // Silently fail for auto-refresh
                    });
            }, 30000);
        }

        // Medical Notification System
        let notificationDropdownOpen = false;
        let currentNotificationFilter = 'all';
        let notifications = [];
        let lastNotificationCount = 0;
        let lastNotificationCheck = Date.now();

        // Initialize medical notifications on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();

            // Auto-refresh medical notifications every 60 seconds
            setInterval(loadNotificationsWithToast, 60000);

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('#notificationButton') && !e.target.closest('#notificationDropdown')) {
                    closeNotifications();
                }
            });
        });

        function toggleNotifications() {
            const dropdown = document.getElementById('notificationDropdown');

            if (notificationDropdownOpen) {
                closeNotifications();
            } else {
                dropdown.classList.remove('hidden');
                notificationDropdownOpen = true;
                loadNotifications();
            }
        }

        function closeNotifications() {
            const dropdown = document.getElementById('notificationDropdown');
            dropdown.classList.add('hidden');
            notificationDropdownOpen = false;
        }

        function loadNotifications() {
            const basePath = '<?= getBasePath() ?>';
            const url = currentNotificationFilter === 'all'
                ? `${basePath}/api/admin/notifications.php?limit=10&unread_only=true`
                : `${basePath}/api/admin/notifications.php?limit=10&category=${currentNotificationFilter}&unread_only=true`;

            console.log('🏥 Loading medical notifications from:', url);
            makeRequest(url)
                .then(data => {
                    if (data && data.data) {
                        notifications = data.data.notifications || [];
                        const currentCount = data.data.counts ? data.data.counts.unread : 0;

                        // Initialize lastNotificationCount on first load
                        if (lastNotificationCount === 0) {
                            lastNotificationCount = currentCount;
                        }

                        updateNotificationCounter(currentCount);
                        updateCategoryCounts(data.data.counts ? data.data.counts.categories : {});
                        renderNotifications();
                    } else {
                        console.warn('⚠️ Invalid medical notification response:', data);
                        handleNotificationError('Invalid response format');
                    }
                })
                .catch(error => {
                    console.error('❌ Failed to load medical notifications:', error);
                    handleNotificationError(error.message);
                });
        }

        function loadNotificationsWithToast() {
            const basePath = '<?= getBasePath() ?>';
            const url = currentNotificationFilter === 'all'
                ? `${basePath}/api/admin/notifications.php?limit=10&unread_only=true`
                : `${basePath}/api/admin/notifications.php?limit=10&category=${currentNotificationFilter}&unread_only=true`;

            makeRequest(url)
                .then(data => {
                    if (data && data.data) {
                        notifications = data.data.notifications || [];
                        const currentCount = data.data.counts ? data.data.counts.unread : 0;

                        // Check for new medical notifications
                        if (currentCount > lastNotificationCount) {
                            const newNotifications = currentCount - lastNotificationCount;
                            showHeaderToast(`🏥 ${newNotifications} new medical notification${newNotifications > 1 ? 's' : ''}`, 'info');

                            // Add visual indicator to notification button
                            const notificationButton = document.getElementById('notificationButton');
                            if (notificationButton) {
                                notificationButton.classList.add('animate-pulse');
                                setTimeout(() => {
                                    notificationButton.classList.remove('animate-pulse');
                                }, 3000);
                            }
                        }

                        lastNotificationCount = currentCount;
                        updateNotificationCounter(currentCount);
                        updateCategoryCounts(data.data.counts ? data.data.counts.categories : {});
                        renderNotifications();
                    } else {
                        console.warn('⚠️ Invalid medical notification response:', data);
                        handleNotificationError('Invalid response format');
                    }
                })
                .catch(error => {
                    console.error('❌ Failed to load medical notifications:', error);
                    handleNotificationError(error.message);
                });
        }

        function handleNotificationError(errorMessage) {
            // Set empty state
            notifications = [];
            updateNotificationCounter(0);
            updateCategoryCounts({});

            // Show error in dropdown
            const container = document.getElementById('notificationsList');
            if (container) {
                if (errorMessage.includes('notifications') && errorMessage.includes('exist')) {
                    // Table doesn't exist - show migration message
                    container.innerHTML = `
                        <div class="p-4 text-center text-yellow-600">
                            <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <p class="font-medium">Medical Notification System Not Set Up</p>
                            <p class="text-sm mt-1">Run the migration to enable medical notifications</p>
                            <a href="${'<?= getBasePath() ?>'}/admin/notifications/migrate.php"
                               class="inline-block mt-2 px-3 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-500">
                                Run Medical Migration
                            </a>
                        </div>
                    `;
                } else {
                    // Other error
                    container.innerHTML = `
                        <div class="p-4 text-center text-red-600">
                            <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="font-medium">Error Loading Medical Notifications</p>
                            <p class="text-sm mt-1">${errorMessage}</p>
                            <button onclick="loadNotifications()"
                                    class="inline-block mt-2 px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-500">
                                Retry
                            </button>
                        </div>
                    `;
                }
            }
        }

        function updateNotificationCounter(count) {
            const counter = document.getElementById('notificationCounter');
            const button = document.getElementById('notificationButton');

            if (count > 0) {
                counter.textContent = count > 99 ? '99+' : count;
                counter.classList.remove('hidden');
                button.classList.add('text-redolence-green');
            } else {
                counter.classList.add('hidden');
                button.classList.remove('text-redolence-green');
            }
        }

        function updateCategoryCounts(categoryCounts) {
            const totalUnread = Object.values(categoryCounts).reduce((sum, count) => sum + count, 0);

            document.getElementById('count-all').textContent = totalUnread;
            document.getElementById('count-BOOKING').textContent = categoryCounts.BOOKING || 0;
            document.getElementById('count-CUSTOMER').textContent = categoryCounts.CUSTOMER || 0;
            document.getElementById('count-SYSTEM').textContent = categoryCounts.SYSTEM || 0;
        }

        function renderNotifications() {
            const container = document.getElementById('notificationsList');

            if (notifications.length === 0) {
                container.innerHTML = `
                    <div class="p-4 text-center text-gray-500">
                        <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                        </svg>
                        <p>No new medical notifications</p>
                    </div>
                `;
                return;
            }

            const html = notifications.map(notification => {
                const categoryInfo = getNotificationCategoryInfo(notification.category);
                const priorityClass = getPriorityClass(notification.priority);

                return `
                    <div class="notification-item p-3 border-b border-redolence-green/10 hover:bg-redolence-green/5 cursor-pointer transition-colors ${notification.is_read ? 'opacity-60' : ''}"
                         onclick="handleNotificationClick('${notification.id}', '${notification.action_url || ''}')">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 rounded-full ${categoryInfo.bgColor} flex items-center justify-center">
                                    ${categoryInfo.icon}
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium text-redolence-navy truncate">${notification.title}</p>
                                    <div class="flex items-center space-x-1">
                                        ${notification.priority !== 'MEDIUM' ? `<span class="w-2 h-2 rounded-full ${priorityClass}"></span>` : ''}
                                        <span class="text-xs text-gray-500">${notification.time_ago}</span>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-600 mt-1 line-clamp-2">${notification.message}</p>
                                <div class="flex items-center justify-between mt-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${categoryInfo.badgeClass}">
                                        ${notification.category}
                                    </span>
                                    ${!notification.is_read ? '<div class="w-2 h-2 bg-redolence-green rounded-full"></div>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;
        }

        function getNotificationCategoryInfo(category) {
            const categoryMap = {
                'BOOKING': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>',
                    bgColor: 'bg-gradient-to-br from-redolence-blue to-blue-600',
                    badgeClass: 'bg-blue-100 text-blue-800'
                },
                'CUSTOMER': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>',
                    bgColor: 'bg-gradient-to-br from-redolence-green to-green-600',
                    badgeClass: 'bg-green-100 text-green-800'
                },
                'STAFF': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>',
                    bgColor: 'bg-gradient-to-br from-purple-500 to-purple-600',
                    badgeClass: 'bg-purple-100 text-purple-800'
                },
                'PAYMENT': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path></svg>',
                    bgColor: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
                    badgeClass: 'bg-yellow-100 text-yellow-800'
                },
                'SYSTEM': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
                    bgColor: 'bg-gradient-to-br from-gray-500 to-gray-600',
                    badgeClass: 'bg-gray-100 text-gray-800'
                },
                'MARKETING': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path></svg>',
                    bgColor: 'bg-gradient-to-br from-pink-500 to-pink-600',
                    badgeClass: 'bg-pink-100 text-pink-800'
                },
                'FEEDBACK': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path></svg>',
                    bgColor: 'bg-gradient-to-br from-indigo-500 to-indigo-600',
                    badgeClass: 'bg-indigo-100 text-indigo-800'
                }
            };

            return categoryMap[category] || categoryMap['SYSTEM'];
        }

        function getPriorityClass(priority) {
            const priorityMap = {
                'URGENT': 'bg-red-500',
                'HIGH': 'bg-orange-500',
                'MEDIUM': 'bg-yellow-500',
                'LOW': 'bg-green-500'
            };

            return priorityMap[priority] || priorityMap['MEDIUM'];
        }

        function filterNotifications(category) {
            // Update active tab
            document.querySelectorAll('.notification-tab').forEach(tab => {
                tab.classList.remove('active', 'bg-redolence-green', 'text-white', 'font-medium');
                tab.classList.add('text-gray-600', 'hover:text-redolence-green');
            });

            const activeTab = event.target;
            activeTab.classList.add('active', 'bg-redolence-green', 'text-white', 'font-medium');
            activeTab.classList.remove('text-gray-600', 'hover:text-redolence-green');

            currentNotificationFilter = category;
            loadNotifications();
        }

        function handleNotificationClick(notificationId, actionUrl) {
            // Mark as read
            markNotificationAsRead(notificationId);

            // Navigate to action URL if provided
            if (actionUrl) {
                const basePath = '<?= getBasePath() ?>';

                // If actionUrl starts with /admin/, ensure it's properly resolved from the base path
                if (actionUrl.startsWith('/admin/')) {
                    window.location.href = basePath + actionUrl;
                } else if (actionUrl.startsWith('admin/')) {
                    // Handle case where actionUrl doesn't start with /
                    window.location.href = basePath + '/' + actionUrl;
                } else if (actionUrl.startsWith('http://') || actionUrl.startsWith('https://') || actionUrl.startsWith('/')) {
                    // Handle absolute URLs or root-relative URLs
                    window.location.href = actionUrl;
                } else {
                    // Handle relative URLs by prepending base path
                    window.location.href = basePath + '/' + actionUrl;
                }
            }
        }

        function markNotificationAsRead(notificationId) {
            const basePath = '<?= getBasePath() ?>';
            makeRequest(`${basePath}/api/admin/notifications.php?id=${notificationId}`, {
                method: 'PUT',
                body: JSON.stringify({ is_read: true })
            }).then(() => {
                loadNotifications();
            }).catch(error => {
                console.error('Failed to mark medical notification as read:', error);
            });
        }

        function markAllAsRead() {
            const basePath = '<?= getBasePath() ?>';
            const category = currentNotificationFilter === 'all' ? null : currentNotificationFilter;

            makeRequest(`${basePath}/api/admin/notifications-bulk.php`, {
                method: 'POST',
                body: JSON.stringify({
                    action: 'mark_read',
                    category: category
                })
            }).then(() => {
                loadNotifications();
                showToast('All medical notifications marked as read', 'success');
            }).catch(error => {
                console.error('Failed to mark all medical notifications as read:', error);
                showToast('Failed to mark medical notifications as read', 'error');
            });
        }

        function openNotificationsPage() {
            const basePath = '<?= getBasePath() ?>';
            window.location.href = `${basePath}/admin/notifications/`;
        }

        // Enhanced toast notification function for medical header notifications
        function showHeaderToast(message, type = 'info', duration = 4000) {
            // Create container if it doesn't exist
            let container = document.getElementById('headerToastContainer');
            if (!container) {
                container = document.createElement('div');
                container.id = 'headerToastContainer';
                container.className = 'fixed top-4 right-4 z-[70] space-y-2';
                document.body.appendChild(container);
            }

            // Create toast element
            const toast = document.createElement('div');
            toast.className = `flex items-center p-4 rounded-lg shadow-lg border transform transition-all duration-300 ease-in-out translate-x-full opacity-0 max-w-sm medical-glass`;

            // Set colors and icon based on type
            let bgClass, borderClass, textClass, iconHTML;

            switch(type) {
                case 'success':
                    bgClass = 'bg-gradient-to-r from-redolence-green to-green-600';
                    borderClass = 'border-green-400';
                    textClass = 'text-white';
                    iconHTML = `<svg class="w-5 h-5 text-white mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>`;
                    break;
                case 'error':
                    bgClass = 'bg-gradient-to-r from-red-500 to-red-600';
                    borderClass = 'border-red-400';
                    textClass = 'text-white';
                    iconHTML = `<svg class="w-5 h-5 text-white mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>`;
                    break;
                case 'warning':
                    bgClass = 'bg-gradient-to-r from-yellow-500 to-yellow-600';
                    borderClass = 'border-yellow-400';
                    textClass = 'text-white';
                    iconHTML = `<svg class="w-5 h-5 text-white mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>`;
                    break;
                case 'info':
                default:
                    bgClass = 'bg-gradient-to-r from-redolence-blue to-blue-600';
                    borderClass = 'border-blue-400';
                    textClass = 'text-white';
                    iconHTML = `<svg class="w-5 h-5 text-white mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                    </svg>`;
                    break;
            }

            toast.classList.add(bgClass, borderClass, textClass);

            toast.innerHTML = `
                ${iconHTML}
                <div class="flex-1">
                    <p class="font-medium text-sm">${message}</p>
                </div>
                <button onclick="this.parentElement.remove()" class="ml-3 text-white/80 hover:text-white transition-colors">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            `;

            container.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
            }, 100);

            // Auto remove after duration
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.classList.add('translate-x-full', 'opacity-0');
                    setTimeout(() => {
                        if (toast.parentElement) {
                            toast.remove();
                        }
                    }, 300);
                }
            }, duration);
        }

        // Handle medical form submissions with loading states
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form[data-ajax]');
            
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;
                    
                    // Show medical loading state
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = `
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing Medical Data...
                    `;
                    
                    const formData = new FormData(form);
                    const data = Object.fromEntries(formData.entries());
                    
                    makeRequest(form.action, {
                        method: form.method || 'POST',
                        body: JSON.stringify(data)
                    })
                    .then(response => {
                        if (response.success) {
                            showToast(response.message || 'Medical operation completed successfully');
                            if (response.redirect) {
                                window.location.href = response.redirect;
                            } else if (form.dataset.reload) {
                                window.location.reload();
                            }
                        } else {
                            showToast(response.error || 'Medical operation failed', 'error');
                        }
                    })
                    .catch(() => {
                        showToast('Medical system error occurred. Please try again.', 'error');
                    })
                    .finally(() => {
                        // Restore button state
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;
                    });
                });
            });
        });

        // Medical keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for medical search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[type="search"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // Escape to close medical modals
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal:not(.hidden)');
                modals.forEach(modal => modal.classList.add('hidden'));
            }
        });
    </script>
</body>
</html>