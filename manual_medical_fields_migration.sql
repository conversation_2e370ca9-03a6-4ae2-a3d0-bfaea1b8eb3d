-- Manual SQL Migration for Medical Treatment Fields
-- Run these ALTER statements in your database to add the missing fields

-- Add session frequency field (for treatment schedules like "Once a week for 4 weeks")
ALTER TABLE services 
ADD COLUMN session_frequency VARCHAR(255) DEFAULT NULL 
COMMENT 'Treatment session frequency and schedule';

-- Add technology used field (for equipment/technology information)
ALTER TABLE services 
ADD COLUMN technology_used VARCHAR(255) DEFAULT NULL 
COMMENT 'Technology or equipment used in treatment';

-- Add featured treatment badge
ALTER TABLE services 
ADD COLUMN featured BOOLEAN DEFAULT FALSE 
COMMENT 'Whether treatment is featured (highlighted)';

-- Add popular treatment badge  
ALTER TABLE services 
ADD COLUMN popular BOOLEAN DEFAULT FALSE 
COMMENT 'Whether treatment is popular (high demand)';

-- Add new treatment badge
ALTER TABLE services 
ADD COLUMN new_treatment BOOLEAN DEFAULT FALSE 
COMMENT 'Whether treatment is newly added';

-- Add sort order field for custom ordering
ALTER TABLE services 
ADD COLUMN sort_order INT DEFAULT 0 
COMMENT 'Custom sort order (lower numbers appear first)';

-- Optional: Make price and duration nullable for medical treatments
-- (Only run these if your current schema has them as NOT NULL)
ALTER TABLE services 
MODIFY COLUMN price DECIMAL(10,2) DEFAULT NULL 
COMMENT 'Treatment price (NULL for TSH pricing)';

ALTER TABLE services 
MODIFY COLUMN duration INT DEFAULT NULL 
COMMENT 'Treatment duration in minutes (NULL for variable duration)';

-- Verify the changes
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'services' 
AND COLUMN_NAME IN ('session_frequency', 'technology_used', 'featured', 'popular', 'new_treatment', 'sort_order')
ORDER BY COLUMN_NAME;
