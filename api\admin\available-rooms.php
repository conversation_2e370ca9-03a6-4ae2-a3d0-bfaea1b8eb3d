<?php
/**
 * Get Available Rooms API
 * Returns available rooms for a specific date and time
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/room_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set content type
header('Content-Type: application/json');

try {
    // Get parameters
    $date = $_GET['date'] ?? '';
    $startTime = $_GET['start_time'] ?? '';
    $endTime = $_GET['end_time'] ?? '';
    $roomType = $_GET['room_type'] ?? null;
    $excludeBookingId = $_GET['exclude_booking_id'] ?? null;
    
    // Validate required parameters
    if (empty($date) || empty($startTime) || empty($endTime)) {
        http_response_code(400);
        echo json_encode(['error' => 'Date, start_time, and end_time are required']);
        exit;
    }
    
    // Validate date format
    if (!DateTime::createFromFormat('Y-m-d', $date)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid date format. Use YYYY-MM-DD']);
        exit;
    }
    
    // Validate time format
    if (!DateTime::createFromFormat('H:i:s', $startTime) || !DateTime::createFromFormat('H:i:s', $endTime)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid time format. Use HH:MM:SS']);
        exit;
    }
    
    // Get available rooms
    $availableRooms = getAvailableRooms($date, $startTime, $endTime, $roomType, $excludeBookingId);
    
    // Format response
    $response = [
        'success' => true,
        'data' => [
            'rooms' => $availableRooms,
            'count' => count($availableRooms),
            'filters' => [
                'date' => $date,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'room_type' => $roomType,
                'exclude_booking_id' => $excludeBookingId
            ]
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Error getting available rooms: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
