<?php
/**
 * Enhanced Offers Page - Medical Aesthetics Special Promotions
 * Modern design with countdown timers, promotional cards, and clear pricing
 */

require_once __DIR__ . '/config/app.php';

// Sample offers data (in a real application, this would come from the database)
$offers = [
    [
        'id' => 1,
        'title' => 'New Client Special',
        'subtitle' => 'First Treatment Discount',
        'description' => 'Get 30% off your first medical aesthetics treatment. Perfect for new clients looking to experience our premium services.',
        'original_price' => 150000,
        'discounted_price' => 105000,
        'discount_percentage' => 30,
        'valid_until' => '2024-12-31',
        'image' => 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'featured' => true,
        'treatments_included' => ['Facial Rejuvenation', 'Skin Analysis', 'Consultation'],
        'terms' => 'Valid for new clients only. Cannot be combined with other offers.'
    ],
    [
        'id' => 2,
        'title' => 'Holiday Glow Package',
        'subtitle' => 'Complete Skin Transformation',
        'description' => 'A comprehensive 3-session package including advanced facial treatments, skin rejuvenation, and personalized skincare consultation.',
        'original_price' => 450000,
        'discounted_price' => 315000,
        'discount_percentage' => 30,
        'valid_until' => '2024-11-30',
        'image' => 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'featured' => false,
        'treatments_included' => ['HydraFacial', 'Chemical Peel', 'LED Light Therapy', 'Skincare Kit'],
        'terms' => 'Package must be used within 6 months. Advance booking required.'
    ],
    [
        'id' => 3,
        'title' => 'Couples Retreat',
        'subtitle' => 'Relaxation for Two',
        'description' => 'Enjoy a romantic spa experience with your partner. Includes side-by-side treatments and complimentary refreshments.',
        'original_price' => 300000,
        'discounted_price' => 240000,
        'discount_percentage' => 20,
        'valid_until' => '2024-12-15',
        'image' => 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'featured' => false,
        'treatments_included' => ['Couples Facial', 'Relaxation Massage', 'Refreshments'],
        'terms' => 'Advance booking required. Subject to availability.'
    ],
    [
        'id' => 4,
        'title' => 'Student Discount',
        'subtitle' => 'Special Pricing for Students',
        'description' => 'Students get 25% off all facial treatments. Valid student ID required at time of booking.',
        'original_price' => 120000,
        'discounted_price' => 90000,
        'discount_percentage' => 25,
        'valid_until' => '2024-12-31',
        'image' => 'https://images.unsplash.com/photo-**********-f09722fb4948?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'featured' => false,
        'treatments_included' => ['Basic Facial', 'Skin Consultation'],
        'terms' => 'Valid student ID required. Cannot be combined with other offers.'
    ]
];

$pageTitle = "Special Offers & Promotions - Redolence Medi Aesthetics";
$pageDescription = "Discover exclusive offers and promotions on premium medical aesthetics treatments at Redolence. Limited-time deals on facial rejuvenation, skin treatments, and wellness packages.";

include __DIR__ . '/includes/header.php';
?>

<!-- Enhanced Offers Page Styles -->
<style>
/* Modern Typography */
.text-luxury {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.text-medical {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

.text-display {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
}

/* Enhanced Gradient Effects */
.gradient-text {
    background: linear-gradient(135deg, #49a75c 0%, #5894d2 50%, #d4af37 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Offer Card Animations */
.offer-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.offer-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.offer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 1.5rem;
}

.offer-card:hover::before {
    opacity: 1;
}

/* Countdown Timer Styles */
.countdown-timer {
    background: linear-gradient(135deg, #49a75c, #5894d2);
    border-radius: 1rem;
    padding: 1rem;
    color: white;
    text-align: center;
}

.countdown-number {
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
}

.countdown-label {
    font-size: 0.75rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Price Display */
.price-original {
    position: relative;
}

.price-original::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #ef4444;
    transform: translateY(-50%);
}

/* Floating Elements */
.floating-element {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Enhanced Glass Effect */
.glass-card {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Pulse Animation for Featured Offers */
.pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(73, 167, 92, 0.3); }
    50% { box-shadow: 0 0 40px rgba(73, 167, 92, 0.6); }
}
</style>

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-redolence-green/20">
    <!-- Background Image with Overlay -->
    <div class="absolute inset-0 z-0">
        <img src="https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=100" 
             alt="Medical Aesthetics Spa" 
             class="w-full h-full object-cover opacity-30">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-900/80 via-gray-800/70 to-redolence-green/30"></div>
    </div>

    <!-- Floating Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="floating-element absolute top-1/4 left-1/4 w-64 h-64 bg-redolence-green/10 rounded-full blur-3xl"></div>
        <div class="floating-element absolute bottom-1/4 right-1/4 w-96 h-96 bg-redolence-blue/10 rounded-full blur-3xl" style="animation-delay: 1s;"></div>
        <div class="floating-element absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-128 h-128 bg-white/5 rounded-full blur-3xl" style="animation-delay: 2s;"></div>
    </div>

    <!-- Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-6 text-center">
        <div class="mb-8">
            <div class="inline-flex items-center bg-white/10 text-white px-6 py-3 rounded-full text-sm font-semibold mb-6 backdrop-blur-sm border border-white/20">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                Exclusive Medical Aesthetics Offers
            </div>
            <h1 class="text-4xl md:text-7xl font-display font-bold text-white mb-6 leading-tight">
                Special
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Promotions
                </span>
            </h1>
            <p class="text-xl md:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed mb-8">
                Discover exclusive limited-time offers on our premium medical aesthetics treatments. Transform your beauty journey with professional care at exceptional value.
            </p>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-redolence-green mb-2"><?= count($offers) ?></div>
                <div class="text-white/80 text-sm md:text-base">Active Offers</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">30%</div>
                <div class="text-white/80 text-sm md:text-base">Max Savings</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-red-400 mb-2">Limited</div>
                <div class="text-white/80 text-sm md:text-base">Time Only</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-green-400 mb-2">Premium</div>
                <div class="text-white/80 text-sm md:text-base">Treatments</div>
            </div>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#offers" class="inline-flex items-center justify-center bg-redolence-green hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                View All Offers
            </a>
            <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
                Contact Us
            </a>
        </div>
    </div>
</section>

<!-- Offers Section -->
<section id="offers" class="py-20 bg-gradient-to-br from-redolence-gray to-white">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-display font-bold text-gray-900 mb-6">
                <span class="gradient-text">Exclusive Offers</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Take advantage of our limited-time promotions on premium medical aesthetics treatments. Each offer is carefully crafted to provide exceptional value while maintaining our high standards of care.
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-redolence-green to-redolence-blue mx-auto rounded-full mt-6"></div>
        </div>

        <!-- Offers Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            <?php foreach ($offers as $index => $offer): ?>
                <div class="offer-card relative bg-white rounded-3xl shadow-xl overflow-hidden <?= $offer['featured'] ? 'pulse-glow' : '' ?> <?= $index === 0 ? 'lg:col-span-2' : '' ?>">
                    <?php if ($offer['featured']): ?>
                        <div class="absolute top-6 left-6 z-20">
                            <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                ⭐ Featured Offer
                            </span>
                        </div>
                    <?php endif; ?>

                    <div class="<?= $index === 0 ? 'md:flex' : '' ?>">
                        <!-- Image Section -->
                        <div class="<?= $index === 0 ? 'md:w-1/2' : '' ?> relative h-64 <?= $index === 0 ? 'md:h-auto' : '' ?>">
                            <img src="<?= $offer['image'] ?>"
                                 alt="<?= htmlspecialchars($offer['title']) ?>"
                                 class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

                            <!-- Discount Badge -->
                            <div class="absolute bottom-4 right-4">
                                <div class="bg-red-500 text-white px-4 py-2 rounded-full font-bold text-lg shadow-lg">
                                    <?= $offer['discount_percentage'] ?>% OFF
                                </div>
                            </div>
                        </div>

                        <!-- Content Section -->
                        <div class="<?= $index === 0 ? 'md:w-1/2' : '' ?> p-8">
                            <div class="mb-4">
                                <h3 class="text-2xl font-display font-bold text-gray-900 mb-2">
                                    <?= htmlspecialchars($offer['title']) ?>
                                </h3>
                                <p class="text-redolence-blue font-semibold text-lg">
                                    <?= htmlspecialchars($offer['subtitle']) ?>
                                </p>
                            </div>

                            <p class="text-gray-600 leading-relaxed mb-6">
                                <?= htmlspecialchars($offer['description']) ?>
                            </p>

                            <!-- Treatments Included -->
                            <div class="mb-6">
                                <h4 class="font-semibold text-gray-900 mb-3">Treatments Included:</h4>
                                <div class="flex flex-wrap gap-2">
                                    <?php foreach ($offer['treatments_included'] as $treatment): ?>
                                        <span class="bg-redolence-green/10 text-redolence-green px-3 py-1 rounded-full text-sm font-medium">
                                            <?= htmlspecialchars($treatment) ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Pricing -->
                            <div class="mb-6">
                                <div class="flex items-center space-x-4 mb-2">
                                    <span class="price-original text-xl text-gray-500 font-medium">
                                        TSH <?= number_format($offer['original_price']) ?>
                                    </span>
                                    <span class="text-3xl font-bold text-redolence-green">
                                        TSH <?= number_format($offer['discounted_price']) ?>
                                    </span>
                                </div>
                                <p class="text-sm text-gray-500">
                                    You save TSH <?= number_format($offer['original_price'] - $offer['discounted_price']) ?>
                                </p>
                            </div>

                            <!-- Countdown Timer -->
                            <div class="countdown-timer mb-6" data-end-date="<?= $offer['valid_until'] ?>">
                                <div class="text-sm font-semibold mb-2">Offer expires in:</div>
                                <div class="grid grid-cols-4 gap-2 text-center">
                                    <div>
                                        <span class="countdown-number days">00</span>
                                        <div class="countdown-label">Days</div>
                                    </div>
                                    <div>
                                        <span class="countdown-number hours">00</span>
                                        <div class="countdown-label">Hours</div>
                                    </div>
                                    <div>
                                        <span class="countdown-number minutes">00</span>
                                        <div class="countdown-label">Minutes</div>
                                    </div>
                                    <div>
                                        <span class="countdown-number seconds">00</span>
                                        <div class="countdown-label">Seconds</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-3">
                                <a href="<?= getBasePath() ?>/customer/book?offer=<?= $offer['id'] ?>"
                                   class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                                    Book This Offer
                                </a>
                                <button onclick="showOfferDetails(<?= $offer['id'] ?>)"
                                        class="bg-white hover:bg-gray-50 text-redolence-blue border-2 border-redolence-blue py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                                    Details
                                </button>
                            </div>

                            <!-- Terms -->
                            <div class="mt-4 text-xs text-gray-500">
                                <?= htmlspecialchars($offer['terms']) ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <div class="glass-card rounded-3xl p-8 max-w-4xl mx-auto">
                <h3 class="text-3xl font-display font-bold text-gray-900 mb-4">
                    Ready to Transform Your Beauty?
                </h3>
                <p class="text-xl text-gray-600 mb-8">
                    Don't miss out on these exclusive offers. Book your consultation today and discover the perfect treatment for your needs.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/customer/book"
                       class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                        </svg>
                        Book Consultation
                    </a>
                    <a href="<?= getBasePath() ?>/contact"
                       class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-redolence-blue px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Ask Questions
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Offer Details Modal -->
<div id="offerDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-3xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center rounded-t-3xl">
            <h2 id="modalOfferTitle" class="text-2xl font-display font-bold text-gray-900"></h2>
            <button onclick="closeOfferModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <div id="modalOfferContent">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
// Countdown Timer Functionality
function initCountdownTimers() {
    const timers = document.querySelectorAll('.countdown-timer');

    timers.forEach(timer => {
        const endDate = new Date(timer.dataset.endDate + 'T23:59:59').getTime();

        function updateTimer() {
            const now = new Date().getTime();
            const distance = endDate - now;

            if (distance < 0) {
                timer.innerHTML = '<div class="text-center text-red-500 font-bold">Offer Expired</div>';
                return;
            }

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            timer.querySelector('.days').textContent = days.toString().padStart(2, '0');
            timer.querySelector('.hours').textContent = hours.toString().padStart(2, '0');
            timer.querySelector('.minutes').textContent = minutes.toString().padStart(2, '0');
            timer.querySelector('.seconds').textContent = seconds.toString().padStart(2, '0');
        }

        updateTimer();
        setInterval(updateTimer, 1000);
    });
}

// Offer Details Modal
const offers = <?= json_encode($offers) ?>;

function showOfferDetails(offerId) {
    const offer = offers.find(o => o.id === offerId);
    if (!offer) return;

    document.getElementById('modalOfferTitle').textContent = offer.title;

    const content = `
        <div class="space-y-6">
            <img src="${offer.image}" alt="${offer.title}" class="w-full h-64 object-cover rounded-2xl">

            <div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">${offer.subtitle}</h3>
                <p class="text-gray-600 leading-relaxed">${offer.description}</p>
            </div>

            <div>
                <h4 class="font-semibold text-gray-900 mb-3">What's Included:</h4>
                <ul class="space-y-2">
                    ${offer.treatments_included.map(treatment => `
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-redolence-green mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            ${treatment}
                        </li>
                    `).join('')}
                </ul>
            </div>

            <div class="bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 rounded-2xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <div class="text-sm text-gray-500">Original Price</div>
                        <div class="text-xl text-gray-500 line-through">TSH ${offer.original_price.toLocaleString()}</div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-500">Special Price</div>
                        <div class="text-3xl font-bold text-redolence-green">TSH ${offer.discounted_price.toLocaleString()}</div>
                    </div>
                </div>
                <div class="text-center">
                    <span class="bg-red-500 text-white px-4 py-2 rounded-full font-bold">
                        Save ${offer.discount_percentage}% - TSH ${(offer.original_price - offer.discounted_price).toLocaleString()}
                    </span>
                </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-2xl p-4">
                <h4 class="font-semibold text-yellow-800 mb-2">Terms & Conditions:</h4>
                <p class="text-yellow-700 text-sm">${offer.terms}</p>
            </div>

            <div class="flex gap-3 pt-4">
                <a href="<?= getBasePath() ?>/customer/book?offer=${offer.id}"
                   class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                    Book This Offer Now
                </a>
                <button onclick="closeOfferModal()"
                        class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold transition-all duration-300">
                    Close
                </button>
            </div>
        </div>
    `;

    document.getElementById('modalOfferContent').innerHTML = content;
    document.getElementById('offerDetailsModal').classList.remove('hidden');
}

function closeOfferModal() {
    document.getElementById('offerDetailsModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('offerDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeOfferModal();
    }
});

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initCountdownTimers();

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe offer cards for scroll animations
    document.querySelectorAll('.offer-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
