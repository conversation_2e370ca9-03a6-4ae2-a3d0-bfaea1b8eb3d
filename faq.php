<?php
/**
 * FAQ Page - Medical Aesthetics Redesign
 * Redolence Medi Aesthetics - Advanced Medical Beauty Center
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Frequently Asked Questions - Redolence Medi Aesthetics";
$pageDescription = "Find answers to common questions about our advanced medical aesthetics treatments, procedures, and policies at Redolence Medi Aesthetics.";

include __DIR__ . '/includes/header.php';

// Medical Aesthetics FAQ Data
$faqsByCategory = [
    'treatments' => [
        [
            'question' => 'What types of medical aesthetic treatments do you offer?',
            'answer' => 'We offer a comprehensive range of FDA-approved medical aesthetic treatments including injectable treatments (Botox, dermal fillers), laser procedures (skin resurfacing, hair removal, tattoo removal), body contouring (CoolSculpting, radiofrequency), facial rejuvenation (chemical peels, microneedling), and advanced skin treatments. All procedures are performed by board-certified medical professionals.'
        ],
        [
            'question' => 'Are your treatments safe and FDA-approved?',
            'answer' => 'Yes, we exclusively use FDA-approved treatments and medical-grade products. Our board-certified physicians and licensed medical professionals follow strict safety protocols and clinical guidelines. We conduct thorough consultations and medical assessments before any treatment to ensure your safety and optimal results.'
        ],
        [
            'question' => 'How long do results typically last?',
            'answer' => 'Results vary depending on the treatment type and individual factors. Injectable treatments typically last 3-6 months for Botox and 6-18 months for dermal fillers. Laser treatments may require multiple sessions with long-lasting results. Body contouring results are permanent with proper lifestyle maintenance. We provide detailed timelines during your consultation.'
        ],
        [
            'question' => 'What should I expect during my first consultation?',
            'answer' => 'Your initial consultation includes a comprehensive medical assessment, discussion of your aesthetic goals, skin analysis using advanced diagnostic tools, treatment recommendations tailored to your needs, detailed explanation of procedures and expected outcomes, and a customized treatment plan with pricing. Consultations typically take 45-60 minutes.'
        ]
    ],
    'preparation' => [
        [
            'question' => 'How should I prepare for my treatment?',
            'answer' => 'Preparation varies by treatment type. Generally, avoid blood-thinning medications, alcohol, and excessive sun exposure 1-2 weeks before treatment. For laser procedures, avoid tanning and certain skincare products. We provide detailed pre-treatment instructions specific to your procedure during your consultation and via email confirmation.'
        ],
        [
            'question' => 'Can I wear makeup after treatment?',
            'answer' => 'This depends on the specific treatment. For injectable treatments, you can typically apply makeup 4-6 hours post-treatment. For laser procedures or chemical peels, we recommend avoiding makeup for 24-48 hours. We provide specific post-treatment care instructions for each procedure to ensure optimal healing and results.'
        ],
        [
            'question' => 'What medications should I avoid before treatment?',
            'answer' => 'Avoid blood-thinning medications (aspirin, ibuprofen, fish oil, vitamin E) for 1-2 weeks before treatment to minimize bruising. Stop retinoids and exfoliating products 3-7 days before laser treatments. Always inform us of all medications and supplements you\'re taking during your consultation for personalized guidance.'
        ]
    ],
    'aftercare' => [
        [
            'question' => 'What is the recovery time for treatments?',
            'answer' => 'Recovery varies by treatment. Injectable treatments have minimal downtime with possible mild swelling or bruising for 2-7 days. Laser treatments may cause redness for 1-3 days. Chemical peels may involve peeling for 3-7 days. Body contouring treatments typically have no downtime. We provide detailed recovery timelines for each procedure.'
        ],
        [
            'question' => 'How do I care for my skin after treatment?',
            'answer' => 'Post-treatment care includes gentle cleansing, avoiding sun exposure, using recommended skincare products, staying hydrated, and following specific instructions for your treatment. We provide comprehensive aftercare kits and detailed instructions. Our medical team is available 24/7 for any post-treatment concerns.'
        ],
        [
            'question' => 'When will I see results from my treatment?',
            'answer' => 'Results timeline varies: Injectable treatments show initial results in 3-7 days with full results in 2 weeks. Laser treatments may show immediate improvement with continued enhancement over 2-3 months. Body contouring results appear gradually over 2-4 months. We schedule follow-up appointments to monitor your progress.'
        ]
    ],
    'booking' => [
        [
            'question' => 'How do I schedule an appointment?',
            'answer' => 'You can schedule appointments through our online booking system, by calling our clinic directly, or by visiting our facility. We recommend booking consultations 1-2 weeks in advance and treatments 2-4 weeks in advance due to high demand. Emergency consultations can often be accommodated within 24-48 hours.'
        ],
        [
            'question' => 'What is your cancellation policy?',
            'answer' => 'We require 48-hour notice for appointment cancellations or rescheduling. Cancellations with less than 48 hours notice may incur a fee equal to 50% of the treatment cost. No-shows are charged the full treatment fee. We understand emergencies occur and handle each situation with discretion and compassion.'
        ],
        [
            'question' => 'Do you offer payment plans or financing?',
            'answer' => 'Yes, we offer flexible payment options including interest-free payment plans, medical financing through approved partners, and package deals for multiple treatments. We accept cash, credit cards, and HSA/FSA accounts. Our patient coordinator will discuss all payment options during your consultation.'
        ]
    ],
    'safety' => [
        [
            'question' => 'What safety protocols do you follow?',
            'answer' => 'We maintain the highest safety standards including sterile technique for all procedures, single-use disposable equipment, comprehensive medical screening, emergency protocols, and continuous staff training. Our facility meets all medical regulatory requirements and undergoes regular safety audits.'
        ],
        [
            'question' => 'Are there any side effects or risks?',
            'answer' => 'All medical procedures carry some risk. Common side effects include temporary redness, swelling, or bruising. Serious complications are rare when treatments are performed by qualified professionals. We thoroughly discuss all potential risks and side effects during your consultation and obtain informed consent before any procedure.'
        ],
        [
            'question' => 'What happens if I have an adverse reaction?',
            'answer' => 'Our medical team is trained to handle adverse reactions immediately. We have emergency protocols in place and maintain relationships with nearby hospitals. We provide 24/7 emergency contact information and follow up with all patients post-treatment. Your safety is our absolute priority.'
        ]
    ]
];
?>

<!-- Revolutionary Medical Aesthetics CSS -->
<style>
/* Advanced Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
}

/* Revolutionary Animation Framework */
@keyframes morphingMedicalBg {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes floatingMedical {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(-10px) rotate(-3deg); }
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(73, 167, 92, 0.3); }
    50% { box-shadow: 0 0 40px rgba(88, 148, 210, 0.5); }
}

@keyframes slideInFromLeft {
    0% { opacity: 0; transform: translateX(-50px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
    0% { opacity: 0; transform: scale(0.9); }
    100% { opacity: 1; transform: scale(1); }
}

/* Medical FAQ Card System */
.medical-faq-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-faq-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-faq-card:hover::before {
    left: 100%;
}

.medical-faq-card:hover {
    transform: translateY(-5px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 20px 40px var(--shadow-primary);
}

.faq-toggle {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.faq-toggle:hover .faq-question {
    color: var(--primary-green);
}

.faq-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.23, 1, 0.32, 1), padding 0.4s ease;
    background: rgba(73, 167, 92, 0.02);
    border-top: 1px solid rgba(73, 167, 92, 0.1);
}

.faq-content.show {
    max-height: 300px;
    padding: 2rem;
}

.faq-icon {
    transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    filter: drop-shadow(0 2px 4px rgba(73, 167, 92, 0.2));
}

.faq-icon.rotate {
    transform: rotate(180deg);
}

.category-header {
    position: relative;
    text-center;
    margin-bottom: 3rem;
}

.category-header::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.search-box {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 3rem;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: var(--primary-green);
    box-shadow: 0 0 30px rgba(73, 167, 92, 0.2);
}

.search-input {
    background: transparent;
    border: none;
    outline: none;
    color: var(--deep-navy);
    width: 100%;
    font-size: 1.1rem;
    font-weight: 500;
}

.search-input::placeholder {
    color: rgba(26, 35, 50, 0.6);
}

.quick-links {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
    justify-content: center;
}

.quick-link {
    background: rgba(73, 167, 92, 0.1);
    border: 2px solid rgba(73, 167, 92, 0.2);
    color: var(--primary-green);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-link:hover {
    background: rgba(73, 167, 92, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(73, 167, 92, 0.2);
}

/* Medical Category Icons */
.category-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-soft);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.category-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 30px var(--shadow-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .medical-faq-card {
        margin: 0;
        border-radius: 16px;
    }
    
    .faq-content.show {
        max-height: 400px;
        padding: 1.5rem;
    }
    
    .quick-links {
        gap: 0.5rem;
    }
    
    .quick-link {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .search-box {
        padding: 1rem;
        margin-bottom: 2rem;
    }
}

/* Medical Professional Styling */
.medical-stats {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.medical-stats:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Contact Section Enhancement */
.contact-enhancement {
    background: linear-gradient(135deg, var(--gradient-soft), rgba(255, 255, 255, 0.1));
    border-radius: 30px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

.contact-enhancement::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(73, 167, 92, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.contact-enhancement::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(88, 148, 210, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}
</style>

<!-- Revolutionary Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Dynamic Medical Background -->
    <div class="absolute inset-0 bg-gradient-to-br from-redolence-green via-redolence-blue to-redolence-green bg-[length:400%_400%] animate-[morphingMedicalBg_8s_ease_infinite]"></div>
    
    <!-- Medical Pattern Overlay -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 75% 75%, var(--primary-blue) 2px, transparent 2px); background-size: 60px 60px;"></div>
    </div>
    
    <!-- Floating Medical Elements -->
    <div class="absolute inset-0 pointer-events-none">
        <div class="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full backdrop-blur-sm animate-[floatingMedical_6s_ease-in-out_infinite]"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-white/15 rounded-full backdrop-blur-sm animate-[floatingMedical_6s_ease-in-out_infinite] [animation-delay:2s]"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-white/8 rounded-full backdrop-blur-sm animate-[floatingMedical_6s_ease-in-out_infinite] [animation-delay:4s]"></div>
        <div class="absolute bottom-20 right-10 w-28 h-28 bg-white/12 rounded-full backdrop-blur-sm animate-[floatingMedical_6s_ease-in-out_infinite] [animation-delay:6s]"></div>
    </div>
    
    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white px-6 max-w-6xl mx-auto">
        <div class="animate-[scaleIn_0.8s_ease_0.2s_both]">
            <div class="inline-flex items-center bg-white/20 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-white/30">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-semibold text-lg">MEDICAL AESTHETICS SUPPORT CENTER</span>
            </div>
        </div>
        
        <h1 class="text-6xl md:text-8xl font-black mb-8 leading-none animate-[slideInFromLeft_1s_ease_0.4s_both]">
            Frequently Asked
            <span class="block text-5xl md:text-7xl font-light opacity-90">Questions</span>
        </h1>
        
        <p class="text-xl md:text-3xl font-light mb-12 leading-relaxed animate-[slideInFromLeft_1s_ease_0.6s_both]">
            Expert answers to your <strong>medical aesthetics</strong> questions
            <span class="block mt-4 text-lg md:text-xl opacity-80">From board-certified specialists you can trust</span>
        </p>
        
        <!-- Medical Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto animate-[scaleIn_0.8s_ease_0.8s_both]">
            <div class="medical-stats">
                <div class="text-4xl font-bold mb-2">500+</div>
                <div class="text-white/80 text-sm">Expert Answers</div>
            </div>
            <div class="medical-stats">
                <div class="text-4xl font-bold mb-2">24/7</div>
                <div class="text-white/80 text-sm">Medical Support</div>
            </div>
            <div class="medical-stats">
                <div class="text-4xl font-bold mb-2">100%</div>
                <div class="text-white/80 text-sm">Board-Certified</div>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Revolutionary FAQ Section -->
<section class="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 30% 30%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 70% 70%, var(--primary-blue) 2px, transparent 2px); background-size: 80px 80px;"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 relative">
        <!-- Search and Navigation Section -->
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 px-6 py-3 rounded-full mb-8 border border-redolence-green/20">
                <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="font-semibold text-redolence-green">FIND YOUR ANSWERS</span>
            </div>
            
            <h2 class="text-5xl md:text-6xl font-black text-gray-900 mb-6 leading-tight">
                How Can We
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-redolence-green to-redolence-blue">Help You?</span>
            </h2>
            <p class="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                Search our comprehensive medical aesthetics FAQ or browse by treatment category
            </p>

            <!-- Advanced Search Box -->
            <div class="search-box max-w-3xl mx-auto">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-redolence-green mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="text" id="faqSearch" class="search-input" placeholder="Search for treatment information, procedures, or policies..." onkeyup="searchFAQs()">
                </div>
            </div>

            <!-- Quick Category Navigation -->
            <div class="quick-links">
                <?php foreach (array_keys($faqsByCategory) as $category) : ?>
                    <button onclick="scrollToCategory('<?= htmlspecialchars($category) ?>')" class="quick-link">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <?php if ($category === 'treatments') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            <?php elseif ($category === 'preparation') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            <?php elseif ($category === 'aftercare') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            <?php elseif ($category === 'booking') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            <?php else : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            <?php endif; ?>
                        </svg>
                        <?= htmlspecialchars(ucwords(str_replace('_', ' ', $category))) ?>
                    </button>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- FAQ Categories -->
        <?php foreach ($faqsByCategory as $category => $faqs) : ?>
            <div id="<?= htmlspecialchars($category) ?>" class="mb-20 faq-category">
                <div class="category-header">
                    <div class="category-icon">
                        <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <?php if ($category === 'treatments') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            <?php elseif ($category === 'preparation') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            <?php elseif ($category === 'aftercare') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            <?php elseif ($category === 'booking') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            <?php else : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            <?php endif; ?>
                        </svg>
                    </div>
                    <h2 class="text-4xl md:text-5xl font-bold text-gray-900">
                        <?= htmlspecialchars(ucwords(str_replace('_', ' ', $category))) ?>
                    </h2>
                </div>

                <div class="grid gap-6">
                    <?php foreach ($faqs as $index => $faq) : ?>
                        <div class="medical-faq-card overflow-hidden">
                            <button class="faq-toggle w-full text-left p-8 focus:outline-none" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <div class="w-14 h-14 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mr-6">
                                            <span class="text-redolence-green font-bold text-lg"><?= $index + 1 ?></span>
                                        </div>
                                        <h3 class="text-xl md:text-2xl font-bold text-gray-900 faq-question pr-4">
                                            <?= htmlspecialchars($faq['question']) ?>
                                        </h3>
                                    </div>
                                    <svg class="faq-icon w-8 h-8 text-redolence-green flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="faq-content">
                                <p class="text-gray-700 leading-relaxed text-lg">
                                    <?= nl2br(htmlspecialchars($faq['answer'])) ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- Enhanced Contact Section -->
        <div class="contact-enhancement relative">
            <div class="relative text-center z-10">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full mb-8">
                    <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Still Have Questions?</h2>
                <p class="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                    Our board-certified medical specialists are here to provide personalized answers and expert guidance for your aesthetic journey.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-gray-900 font-bold text-lg mb-2">Call Our Specialists</h3>
                        <p class="text-gray-600">Immediate medical consultation</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-gray-900 font-bold text-lg mb-2">Email Our Team</h3>
                        <p class="text-gray-600">Detailed medical inquiries</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-gray-900 font-bold text-lg mb-2">Visit Our Clinic</h3>
                        <p class="text-gray-600">In-person consultation</p>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="contact.php" class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-redolence-blue hover:from-redolence-blue hover:to-redolence-green text-white px-8 py-4 rounded-xl font-bold text-lg transition-all hover:scale-105 shadow-lg">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Schedule Consultation
                    </a>
                    <a href="tel:+255781985757" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-green px-8 py-4 rounded-xl font-bold text-lg transition-all border-2 border-redolence-green/20 hover:border-redolence-green/40">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Call: +255 781 985 757
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary JavaScript -->
<script>
// Enhanced FAQ functionality with medical aesthetics focus
function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('.faq-icon');
    const isOpen = content.classList.contains('show');

    // Close all other FAQs with smooth animation
    document.querySelectorAll('.faq-content').forEach(item => {
        if (item !== content && item.classList.contains('show')) {
            item.classList.remove('show');
            const otherIcon = item.previousElementSibling.querySelector('.faq-icon');
            if (otherIcon) {
                otherIcon.classList.remove('rotate');
            }
        }
    });

    // Toggle current FAQ with smooth animation
    if (isOpen) {
        content.classList.remove('show');
        icon.classList.remove('rotate');
    } else {
        content.classList.add('show');
        icon.classList.add('rotate');

        // Smooth scroll to the opened FAQ
        setTimeout(() => {
            button.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 200);
    }
}

// Advanced search functionality
function searchFAQs() {
    const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
    const faqCards = document.querySelectorAll('.medical-faq-card');
    let visibleCount = 0;

    faqCards.forEach(card => {
        const question = card.querySelector('.faq-question').textContent.toLowerCase();
        const answer = card.querySelector('.faq-content p').textContent.toLowerCase();

        if (question.includes(searchTerm) || answer.includes(searchTerm)) {
            card.style.display = 'block';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
            visibleCount++;
        } else {
            card.style.display = 'none';
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
        }
    });

    // Show/hide category headers based on visible FAQs
    const categories = document.querySelectorAll('.faq-category');
    categories.forEach(category => {
        const categoryCards = category.querySelectorAll('.medical-faq-card');
        const visibleCards = Array.from(categoryCards).filter(card => card.style.display !== 'none');

        if (visibleCards.length > 0 || searchTerm === '') {
            category.style.display = 'block';
        } else {
            category.style.display = 'none';
        }
    });

    // Show no results message if needed
    showNoResultsMessage(visibleCount === 0 && searchTerm !== '');
}

// Scroll to category with enhanced animation
function scrollToCategory(categoryId) {
    const category = document.getElementById(categoryId);
    if (category) {
        category.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Add highlight effect
        category.style.transform = 'scale(1.02)';
        category.style.filter = 'brightness(1.05)';
        setTimeout(() => {
            category.style.transform = 'scale(1)';
            category.style.filter = 'brightness(1)';
        }, 500);
    }
}

// Show/hide no results message
function showNoResultsMessage(show) {
    let noResultsDiv = document.getElementById('noResults');

    if (show && !noResultsDiv) {
        noResultsDiv = document.createElement('div');
        noResultsDiv.id = 'noResults';
        noResultsDiv.className = 'text-center py-20';
        noResultsDiv.innerHTML = `
            <div class="w-32 h-32 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-8">
                <svg class="w-16 h-16 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-6">No Results Found</h3>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">We couldn't find any medical aesthetics FAQs matching your search. Try different keywords or browse our treatment categories.</p>
            <button onclick="clearSearch()" class="bg-gradient-to-r from-redolence-green to-redolence-blue text-white px-8 py-4 rounded-xl font-bold text-lg hover:scale-105 transition-all">
                Clear Search & Browse All
            </button>
        `;
        document.querySelector('.max-w-7xl').appendChild(noResultsDiv);
    } else if (!show && noResultsDiv) {
        noResultsDiv.remove();
    }
}

// Clear search
function clearSearch() {
    document.getElementById('faqSearch').value = '';
    searchFAQs();
}

// Initialize page with medical aesthetics enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth entrance animations to FAQ cards
    const faqCards = document.querySelectorAll('.medical-faq-card');
    faqCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.23, 1, 0.32, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });

    // Add search input event listener
    const searchInput = document.getElementById('faqSearch');
    if (searchInput) {
        searchInput.addEventListener('input', searchFAQs);
        
        // Add focus enhancement
        searchInput.addEventListener('focus', function() {
            this.parentElement.parentElement.style.transform = 'scale(1.02)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.parentElement.parentElement.style.transform = 'scale(1)';
        });
    }

    // Add category hover enhancements
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach(header => {
        header.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.category-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });
        
        header.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.category-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>