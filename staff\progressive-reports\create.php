<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php');
}

// Get parameters
$customerId = $_GET['customer_id'] ?? '';
$appointmentId = $_GET['appointment_id'] ?? '';

if (!$customerId || !$appointmentId) {
    header('Location: ' . getBasePath() . '/staff/appointments');
    exit;
}

// Get customer and appointment details
global $database;

$customer = $database->fetch("
    SELECT id, name, email, phone 
    FROM users 
    WHERE id = ? AND role = 'CUSTOMER'
", [$customerId]);

$appointment = $database->fetch("
    SELECT b.*, s.name as service_name, p.name as package_name
    FROM bookings b
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN packages p ON b.package_id = p.id
    WHERE b.id = ? AND b.staff_id = ?
", [$appointmentId, $_SESSION['user_id']]);

if (!$customer || !$appointment) {
    header('Location: ' . getBasePath() . '/staff/appointments');
    exit;
}

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $treatment = trim($_POST['treatment'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    $entryDate = $_POST['entry_date'] ?? '';
    
    // Validation
    if (empty($title)) {
        $errors[] = 'Medical report title is required.';
    }
    
    if (empty($treatment)) {
        $errors[] = 'Medical treatment description is required.';
    }
    
    if (empty($notes)) {
        $errors[] = 'Medical progress notes are required.';
    }
    
    if (empty($entryDate)) {
        $errors[] = 'Treatment date is required.';
    } elseif (!strtotime($entryDate)) {
        $errors[] = 'Please enter a valid treatment date.';
    }
    
    // Create report if no errors
    if (empty($errors)) {
        try {
            $database->beginTransaction();
            
            // Create progressive report
            $reportId = generateUUID();
            $database->query("
                INSERT INTO progressive_reports (id, client_id, title, description, status, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, 'ACTIVE', ?, NOW(), NOW())
            ", [$reportId, $customerId, $title, $description, $_SESSION['user_id']]);
            
            // Create first entry
            $entryId = generateUUID();
            $database->query("
                INSERT INTO progressive_report_entries (id, report_id, appointment_id, treatment, description, notes, entry_date, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ", [$entryId, $reportId, $appointmentId, $treatment, $treatment, $notes, $entryDate, $_SESSION['user_id']]);
            
            $database->commit();
            
            // Redirect to view the new report
            header('Location: ' . getBasePath() . '/staff/progressive-reports/view.php?id=' . $reportId);
            exit;
            
        } catch (Exception $e) {
            $database->rollback();
            $errors[] = 'Failed to create medical progress report. Please try again. Error: ' . $e->getMessage();
            error_log("Progressive Report Creation Error: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
        }
    }
}

$pageTitle = "Create Medical Progress Report - " . $customer['name'];
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Progress Report Creation Back Navigation -->
<div class="mb-6">
    <a href="<?= getBasePath() ?>/staff/appointments" 
       class="inline-flex items-center text-sm font-medium text-redolence-green hover:text-redolence-blue transition-colors focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2 rounded-lg px-3 py-2">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        Back to Patient Appointments
    </a>
</div>

<!-- Medical Progress Report Creation Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex items-center">
        <div class="flex-shrink-0 mr-6">
            <div class="w-16 h-16 bg-gradient-to-r from-redolence-green to-redolence-blue rounded-2xl flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
            </div>
        </div>
        <div>
            <h1 class="text-3xl font-bold text-redolence-navy">Create New Medical Progress Report</h1>
            <p class="mt-2 text-lg text-gray-600">Patient: <span class="text-redolence-green font-semibold"><?= htmlspecialchars($customer['name']) ?></span></p>
            <div class="mt-3 flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                HIPAA Compliant Medical Record Creation
            </div>
        </div>
    </div>
</div>

<!-- Patient Information Card -->
<div class="medical-card mb-8 p-6">
    <h3 class="text-lg font-bold text-redolence-navy mb-4">Patient Information</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <label class="block text-sm font-medium text-gray-600 mb-1">Patient Name</label>
            <p class="text-redolence-navy font-semibold"><?= htmlspecialchars($customer['name']) ?></p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-600 mb-1">Email Address</label>
            <p class="text-gray-700"><?= htmlspecialchars($customer['email']) ?></p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-600 mb-1">Phone Number</label>
            <p class="text-gray-700"><?= htmlspecialchars($customer['phone'] ?? 'Not provided') ?></p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-600 mb-1">Related Treatment</label>
            <p class="text-gray-700"><?= htmlspecialchars($appointment['service_name'] ?? $appointment['package_name'] ?? 'General Treatment') ?></p>
        </div>
    </div>
</div>

<!-- Error Messages -->
<?php if (!empty($errors)): ?>
    <div class="medical-card mb-8 p-6 bg-red-50 border-l-4 border-red-400">
        <div class="flex items-start">
            <svg class="w-6 h-6 text-red-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <h3 class="text-red-800 font-medium mb-2">Please correct the following errors:</h3>
                <ul class="text-red-700 list-disc list-inside space-y-1">
                    <?php foreach ($errors as $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Medical Progress Report Creation Form -->
<div class="medical-card p-8">
    <form method="POST" class="space-y-8">
        <!-- Medical Report Title -->
        <div>
            <label for="title" class="block text-sm font-bold text-redolence-navy mb-3">
                Medical Progress Report Title *
            </label>
            <input type="text" 
                   id="title" 
                   name="title" 
                   value="<?= htmlspecialchars($_POST['title'] ?? '') ?>"
                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                   placeholder="e.g., Botox Treatment Progress - <?= htmlspecialchars($customer['name']) ?>"
                   required>
            <p class="mt-2 text-sm text-gray-600">Provide a descriptive title for this medical progress report.</p>
        </div>

        <!-- Medical Report Description -->
        <div>
            <label for="description" class="block text-sm font-bold text-redolence-navy mb-3">
                Medical Report Description
            </label>
            <textarea id="description" 
                      name="description" 
                      rows="4"
                      class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all resize-none"
                      placeholder="Describe the overall treatment plan, goals, and expected outcomes for this patient..."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
            <p class="mt-2 text-sm text-gray-600">Optional: Provide an overview of the treatment plan and goals.</p>
        </div>

        <!-- First Entry Details -->
        <div class="border-t border-gray-200 pt-8">
            <h3 class="text-xl font-bold text-redolence-navy mb-6">Initial Medical Treatment Entry</h3>
            
            <!-- Medical Treatment Description -->
            <div class="mb-6">
                <label for="treatment" class="block text-sm font-bold text-redolence-navy mb-3">
                    Medical Treatment Description *
                </label>
                <input type="text" 
                       id="treatment" 
                       name="treatment" 
                       value="<?= htmlspecialchars($_POST['treatment'] ?? '') ?>"
                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                       placeholder="e.g., Initial Botox injection, Dermal filler treatment, Laser therapy..."
                       required>
                <p class="mt-2 text-sm text-gray-600">Describe the medical treatment or procedure performed.</p>
            </div>

            <!-- Treatment Date -->
            <div class="mb-6">
                <label for="entry_date" class="block text-sm font-bold text-redolence-navy mb-3">
                    Treatment Date *
                </label>
                <input type="date" 
                       id="entry_date" 
                       name="entry_date" 
                       value="<?= htmlspecialchars($_POST['entry_date'] ?? date('Y-m-d')) ?>"
                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                       required>
                <p class="mt-2 text-sm text-gray-600">Date when the medical treatment was performed.</p>
            </div>

            <!-- Medical Progress Notes -->
            <div>
                <label for="notes" class="block text-sm font-bold text-redolence-navy mb-3">
                    Medical Progress Notes *
                </label>
                <textarea id="notes" 
                          name="notes" 
                          rows="8"
                          class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all resize-none"
                          placeholder="Document patient progress, treatment outcomes, observations, side effects, patient response, and any follow-up recommendations..."
                          required><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                <p class="mt-2 text-sm text-gray-600">Detailed medical notes about patient progress, treatment response, and clinical observations.</p>
            </div>
        </div>

        <!-- HIPAA Compliance Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <div class="flex items-start">
                <svg class="w-6 h-6 text-blue-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                <div>
                    <h4 class="text-blue-800 font-semibold mb-2">HIPAA Compliance Notice</h4>
                    <p class="text-blue-700 text-sm">This medical progress report contains protected health information (PHI). All information must be accurate, relevant to patient care, and handled in accordance with HIPAA privacy regulations. Only authorized medical personnel should access this information.</p>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
            <button type="submit" class="medical-btn-primary px-8 py-4 flex-1 sm:flex-none">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Medical Report
            </button>
            <a href="<?= getBasePath() ?>/staff/appointments" 
               class="medical-btn-secondary px-8 py-4 text-center flex-1 sm:flex-none">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Cancel
            </a>
        </div>
    </form>
</div>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
