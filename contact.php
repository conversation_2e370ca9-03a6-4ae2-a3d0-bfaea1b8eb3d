<?php
/**
 * Contact Page - Revolutionary Redesign
 * Redolence Medi Aesthetics - Advanced Medical Aesthetics Center
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/contact_functions.php';

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Handle form submission
$message = '';
$messageType = '';
$formData = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid security token. Please try again.');
        }

        // Sanitize input data
        $formData = sanitizeContactData($_POST);

        // Validate data
        $errors = validateContactData($formData);

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // Create contact message
        $contactId = createContactMessage($formData);

        if ($contactId) {
            $message = 'Thank you for contacting Redolence Medi Aesthetics! Our medical specialists will respond within 24 hours to discuss your aesthetic goals.';
            $messageType = 'success';

            // Clear form data on success
            $formData = [];

            // Regenerate CSRF token
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        } else {
            throw new Exception('Failed to send message. Please try again.');
        }

    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

$pageTitle = "Contact Redolence Medi Aesthetics - Schedule Your Consultation";
$pageDescription = "Contact Redolence Medi Aesthetics for advanced medical aesthetics consultations. Schedule your appointment with our board-certified specialists today.";

include __DIR__ . '/includes/header.php';
?>

<!-- Revolutionary CSS Framework -->
<style>
/* Advanced Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    --gradient-contact: linear-gradient(135deg, #49a75c, #5894d2, #6ba3d6, #4db86d);
}

/* Revolutionary Animation Framework */
@keyframes morphingContactBg {
    0%, 100% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 50% 100%; }
    75% { background-position: 0% 100%; }
}

@keyframes floatingContact {
    0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
    33% { transform: translateY(-25px) rotate(8deg) scale(1.05); }
    66% { transform: translateY(-15px) rotate(-5deg) scale(0.95); }
}

@keyframes pulseGlowContact {
    0%, 100% { box-shadow: 0 0 30px rgba(73, 167, 92, 0.3), 0 0 60px rgba(88, 148, 210, 0.2); }
    50% { box-shadow: 0 0 50px rgba(88, 148, 210, 0.4), 0 0 80px rgba(73, 167, 92, 0.3); }
}

@keyframes slideInContact {
    0% { opacity: 0; transform: translateX(-100px) rotateY(-30deg); }
    100% { opacity: 1; transform: translateX(0) rotateY(0deg); }
}

@keyframes scaleInContact {
    0% { opacity: 0; transform: scale(0.7) rotateZ(-10deg); }
    100% { opacity: 1; transform: scale(1) rotateZ(0deg); }
}

@keyframes formFieldFocus {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Revolutionary Contact Card System */
.contact-card-revolutionary {
    background: white;
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.contact-card-revolutionary:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(73, 167, 92, 0.3);
}

/* Advanced Form System */
.form-field-revolutionary {
    position: relative;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.form-field-revolutionary input,
.form-field-revolutionary textarea,
.form-field-revolutionary select {
    width: 100%;
    padding: 1rem 1.5rem;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    color: #374151;
    transition: all 0.3s ease;
    display: block;
    box-sizing: border-box;
}

.form-field-revolutionary input::placeholder,
.form-field-revolutionary textarea::placeholder {
    color: #9ca3af;
    opacity: 1;
}

.form-field-revolutionary input:focus,
.form-field-revolutionary textarea:focus,
.form-field-revolutionary select:focus {
    outline: none;
    border-color: #49a75c;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.form-field-revolutionary:focus-within {
    transform: translateY(-2px);
}

/* Revolutionary Button System */
.btn-contact-revolutionary {
    background: var(--gradient-contact);
    background-size: 300% 300%;
    border: none;
    padding: 1.5rem 3rem;
    border-radius: 50px;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
    animation: morphingContactBg 8s ease infinite;
    cursor: pointer;
}

.btn-contact-revolutionary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.7s ease;
}

.btn-contact-revolutionary:hover::before {
    left: 100%;
}

.btn-contact-revolutionary:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.4), 0 0 30px rgba(88, 148, 210, 0.3);
}

.btn-contact-revolutionary:active {
    transform: translateY(-2px) scale(1.02);
}

/* Contact Info Cards */
.contact-info-revolutionary {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.contact-info-revolutionary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-soft);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.contact-info-revolutionary:hover::after {
    opacity: 1;
}

.contact-info-revolutionary:hover {
    transform: translateY(-10px) rotateY(8deg) scale(1.05);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

/* Morphing Background System */
.morphing-contact-bg {
    background: var(--gradient-contact);
    background-size: 400% 400%;
    animation: morphingContactBg 12s ease infinite;
}

/* Floating Elements */
.floating-contact {
    animation: floatingContact 8s ease-in-out infinite;
}

.floating-contact:nth-child(2) { animation-delay: 2.5s; }
.floating-contact:nth-child(3) { animation-delay: 5s; }
.floating-contact:nth-child(4) { animation-delay: 7.5s; }

/* Advanced Typography */
.text-contact-revolutionary {
    background: var(--gradient-contact);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: morphingContactBg 8s ease infinite;
}

/* Scroll Reveal System */
.contact-reveal {
    opacity: 0;
    transform: translateY(60px) rotateX(-15deg);
    transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
}

.contact-reveal.revealed {
    opacity: 1;
    transform: translateY(0) rotateX(0deg);
}

/* Interactive Elements */
.contact-interactive {
    transition: all 0.3s ease;
    cursor: pointer;
}

.contact-interactive:hover {
    transform: translateY(-3px);
}

/* Map Container */
.map-revolutionary {
    border-radius: 32px;
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(73, 167, 92, 0.2);
    transition: all 0.5s ease;
}

.map-revolutionary:hover {
    transform: scale(1.02);
    border-color: rgba(73, 167, 92, 0.4);
    box-shadow: 0 20px 40px var(--shadow-primary);
}

/* Remove all gradient hover effects */
.contact-card-revolutionary:hover::before,
.contact-info-revolutionary:hover::after {
    opacity: 0 !important;
}

.contact-card-revolutionary:hover,
.contact-info-revolutionary:hover {
    transform: none !important;
    box-shadow: none !important;
    border-color: initial !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-card-revolutionary {
        margin: 0;
        border-radius: 16px;
        padding: 1.5rem;
    }
    
    .form-field-revolutionary input,
    .form-field-revolutionary textarea,
    .form-field-revolutionary select {
        padding: 1rem 1.2rem;
        font-size: 1rem;
        border-radius: 12px;
    }
    
    .btn-contact-revolutionary {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        border-radius: 12px;
    }
    
    /* Mobile Form Section */
    .py-40 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .py-32 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    /* Mobile Form Header */
    .contact-card-revolutionary h2 {
        font-size: 2rem;
    }
    
    .contact-card-revolutionary .text-5xl {
        font-size: 2rem;
    }
    
    .contact-card-revolutionary .w-20 {
        width: 3rem;
        height: 3rem;
    }
    
    /* Mobile Contact Info */
    .contact-info-revolutionary h3 {
        font-size: 1.5rem;
    }
    
    .contact-info-revolutionary h4 {
        font-size: 1.2rem;
    }
    
    .contact-info-revolutionary .text-4xl {
        font-size: 1.5rem;
    }
    
    .contact-info-revolutionary .w-20 {
        width: 3rem;
        height: 3rem;
    }
    
    .contact-info-revolutionary .w-16 {
        width: 3rem;
        height: 3rem;
    }
    
    /* Mobile Map Section */
    .map-revolutionary {
        border-radius: 16px;
        border-width: 2px;
    }
    
    .map-revolutionary:hover {
        transform: none;
        border-color: initial;
        box-shadow: none;
    }
    
    /* Mobile Grid Adjustments */
    .grid.grid-cols-1.lg\\:grid-cols-2 {
        gap: 2rem;
    }
    
    .grid.grid-cols-1.md\\:grid-cols-2 {
        gap: 1rem;
    }
    
    .grid.grid-cols-1.md\\:grid-cols-3 {
        gap: 1rem;
    }
}

/* Success/Error Messages */
.message-revolutionary {
    border-radius: 20px;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(15px);
    border: 2px solid;
    animation: scaleInContact 0.5s ease;
}

.message-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border-color: rgba(34, 197, 94, 0.3);
    color: #059669;
}

.message-error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border-color: rgba(239, 68, 68, 0.3);
    color: #dc2626;
}
</style>

<!-- Hero Section -->
<section class="py-16 md:py-24 bg-redolence-green text-white">
    <div class="max-w-4xl mx-auto px-4 md:px-6 text-center">
        <div class="mb-8">
            <div class="inline-flex items-center bg-white/20 px-4 py-2 rounded-full mb-6">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span class="font-semibold text-sm">CONNECT WITH OUR SPECIALISTS</span>
            </div>
        </div>
        
        <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Contact Redolence
            <span class="block font-light opacity-90">Medi Aesthetics</span>
        </h1>
        
        <p class="text-lg md:text-xl mb-8 opacity-90">
            Your Medical Aesthetics Journey Begins Here
        </p>
        
        <!-- Quick Contact Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto">
            <a href="tel:+255781985757" class="bg-white/20 p-4 rounded-lg text-center hover:bg-white/30 transition-colors">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <h3 class="font-semibold">Call Us</h3>
                <p class="text-sm opacity-80">+255 781 985 757</p>
            </a>

            <a href="mailto:<EMAIL>" class="bg-white/20 p-4 rounded-lg text-center hover:bg-white/30 transition-colors">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <h3 class="font-semibold">Email Us</h3>
                <p class="text-sm opacity-80"><EMAIL></p>
            </a>

            <div class="bg-white/20 p-4 rounded-lg text-center">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <h3 class="font-semibold">Visit Us</h3>
                <p class="text-sm opacity-80">Medical Plaza</p>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Contact Form Section -->
<section class="py-40 bg-gradient-to-br from-gray-50 via-white to-redolence-green/5 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 30% 30%, var(--primary-green) 3px, transparent 3px), radial-gradient(circle at 70% 70%, var(--primary-blue) 3px, transparent 3px); background-size: 100px 100px;"></div>
    </div>
    
    <!-- Floating Background Elements -->
    <div class="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-redolence-green/10 to-transparent rounded-full blur-3xl"></div>
    <div class="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-redolence-blue/10 to-transparent rounded-full blur-3xl"></div>
    
    <div class="max-w-7xl mx-auto px-6 relative">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-20">
            <!-- Revolutionary Contact Form -->
            <div class="contact-reveal">
                <div class="contact-card-revolutionary p-12">
                    <!-- Form Header -->
                    <div class="text-center mb-12">
                        <div class="w-20 h-20 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <h2 class="text-5xl font-black text-gray-900 mb-6">
                            Schedule Your
                            <span class="text-contact-revolutionary block">Medical Consultation</span>
                        </h2>
                        <p class="text-xl text-gray-600 leading-relaxed">
                            Our board-certified specialists will respond within 24 hours to discuss your aesthetic goals
                        </p>
                    </div>
                    
                    <!-- Success/Error Messages -->
                    <?php if ($message): ?>
                        <div class="message-revolutionary <?= $messageType === 'success' ? 'message-success' : 'message-error' ?>">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <?php if ($messageType === 'success'): ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <?php else: ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    <?php endif; ?>
                                </svg>
                                <div class="font-semibold"><?= $message ?></div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Revolutionary Form -->
                    <form method="POST" class="space-y-8" id="revolutionaryContactForm">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

                        <!-- Name and Email Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-field-revolutionary">
                                <input type="text" id="name" name="name" required
                                       value="<?= htmlspecialchars($formData['name'] ?? '') ?>"
                                       placeholder="Full Name *"
                                       class="contact-interactive">
                            </div>
                            <div class="form-field-revolutionary">
                                <input type="email" id="email" name="email" required
                                       value="<?= htmlspecialchars($formData['email'] ?? '') ?>"
                                       placeholder="Email Address *"
                                       class="contact-interactive">
                            </div>
                        </div>

                        <!-- Phone and Subject Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-field-revolutionary">
                                <input type="tel" id="phone" name="phone"
                                       value="<?= htmlspecialchars($formData['phone'] ?? '') ?>"
                                       placeholder="Phone Number (Optional)"
                                       class="contact-interactive">
                            </div>
                            <div class="form-field-revolutionary">
                                <select id="subject" name="subject" class="contact-interactive">
                                    <option value="">Select Treatment Interest</option>
                                    <option value="consultation" <?= ($formData['subject'] ?? '') === 'consultation' ? 'selected' : '' ?>>Initial Consultation</option>
                                    <option value="facial" <?= ($formData['subject'] ?? '') === 'facial' ? 'selected' : '' ?>>Facial Rejuvenation</option>
                                    <option value="body" <?= ($formData['subject'] ?? '') === 'body' ? 'selected' : '' ?>>Body Contouring</option>
                                    <option value="skin" <?= ($formData['subject'] ?? '') === 'skin' ? 'selected' : '' ?>>Skin Treatments</option>
                                    <option value="injectables" <?= ($formData['subject'] ?? '') === 'injectables' ? 'selected' : '' ?>>Injectable Treatments</option>
                                    <option value="laser" <?= ($formData['subject'] ?? '') === 'laser' ? 'selected' : '' ?>>Laser Procedures</option>
                                    <option value="other" <?= ($formData['subject'] ?? '') === 'other' ? 'selected' : '' ?>>Other Treatment</option>
                                </select>
                            </div>
                        </div>

                        <!-- Message Field -->
                        <div class="form-field-revolutionary">
                            <textarea id="message" name="message" rows="6" required
                                      placeholder="Tell us about your aesthetic goals, concerns, and any questions you have... *"
                                      class="contact-interactive resize-none"><?= htmlspecialchars($formData['message'] ?? '') ?></textarea>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" id="submitBtn" class="btn-contact-revolutionary w-full contact-interactive">
                            <svg class="w-6 h-6 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            <span id="submitText">Schedule My Consultation</span>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Revolutionary Contact Information -->
            <div class="space-y-10 contact-reveal" style="animation-delay: 0.3s;">
                <!-- Main Contact Info -->
                <div class="contact-info-revolutionary">
                    <div class="relative z-10">
                        <div class="text-center mb-10">
                            <div class="w-20 h-20 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-4xl font-bold text-gray-900 mb-6">
                                Get in <span class="text-contact-revolutionary">Touch</span>
                            </h3>
                            <p class="text-xl text-gray-600">Multiple ways to reach our medical specialists</p>
                        </div>
                        
                        <!-- Contact Methods -->
                        <div class="space-y-8">
                            <!-- Location -->
                            <div class="flex items-start space-x-6 p-6 bg-white/50 rounded-2xl backdrop-blur-sm border border-redolence-green/10 contact-interactive">
                                <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-bold text-gray-900 mb-3">Medical Clinic Location</h4>
                                    <p class="text-gray-600 leading-relaxed text-lg">
                                        Medical Plaza, Professional District<br>
                                        Healthcare Avenue, Suite 205<br>
                                        Dar es Salaam, Tanzania
                                    </p>
                                    <a href="https://maps.app.goo.gl/9R4GdECM3wykgtRv8" target="_blank" 
                                       class="inline-flex items-center text-redolence-green hover:text-redolence-blue transition-colors mt-4 font-semibold">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                        </svg>
                                        Get Directions
                                    </a>
                                </div>
                            </div>

                            <!-- Phone -->
                            <div class="flex items-start space-x-6 p-6 bg-white/50 rounded-2xl backdrop-blur-sm border border-redolence-blue/10 contact-interactive">
                                <div class="w-16 h-16 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-bold text-gray-900 mb-3">Call Our Specialists</h4>
                                    <p class="text-gray-600 mb-2">
                                        <a href="tel:+255781985757" class="text-redolence-blue hover:text-redolence-green transition-colors text-2xl font-bold">
                                            +255 781 985 757
                                        </a>
                                    </p>
                                    <p class="text-gray-500">Monday - Saturday: 9:00 AM - 7:00 PM</p>
                                    <p class="text-gray-500">Sunday: Closed</p>
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="flex items-start space-x-6 p-6 bg-white/50 rounded-2xl backdrop-blur-sm border border-purple-500/10 contact-interactive">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-bold text-gray-900 mb-3">Email Our Medical Team</h4>
                                    <p class="text-gray-600 mb-2">
                                        <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-redolence-green transition-colors text-lg font-semibold">
                                            <EMAIL>
                                        </a>
                                    </p>
                                    <p class="text-gray-600">
                                        <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-redolence-blue transition-colors text-lg font-semibold">
                                            <EMAIL>
                                        </a>
                                    </p>
                                    <p class="text-gray-500 mt-2">24-hour response guarantee</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media & Hours -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Social Media -->
                    <div class="contact-info-revolutionary">
                        <div class="relative z-10 text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Follow Us</h3>
                            
                            <div class="flex justify-center space-x-4 mb-6">
                                <a href="#" class="w-14 h-14 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center text-redolence-green hover:bg-redolence-green hover:text-white transition-all contact-interactive">
                                    <i class="fab fa-facebook-f text-xl"></i>
                                </a>
                                <a href="#" class="w-14 h-14 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center text-redolence-blue hover:bg-redolence-blue hover:text-white transition-all contact-interactive">
                                    <i class="fab fa-instagram text-xl"></i>
                                </a>
                                <a href="#" class="w-14 h-14 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center text-purple-600 hover:bg-purple-600 hover:text-white transition-all contact-interactive">
                                    <i class="fab fa-twitter text-xl"></i>
                                </a>
                                <a href="#" class="w-14 h-14 bg-gradient-to-br from-red-500/20 to-red-500/10 rounded-full flex items-center justify-center text-red-600 hover:bg-red-600 hover:text-white transition-all contact-interactive">
                                    <i class="fab fa-youtube text-xl"></i>
                                </a>
                            </div>
                            
                            <p class="text-gray-600 text-sm">
                                Latest treatments, tips, and special offers
                            </p>
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    <div class="contact-info-revolutionary">
                        <div class="relative z-10 text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-red-500/20 to-red-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Emergency</h3>
                            
                            <p class="text-gray-600 mb-4">
                                For post-treatment emergencies or urgent medical concerns
                            </p>
                            
                            <a href="tel:+255781985757" class="inline-flex items-center bg-red-600 text-white px-6 py-3 rounded-full font-semibold hover:bg-red-700 transition-all contact-interactive">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                Emergency Line
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Map Section -->
<section class="py-32 bg-gradient-to-br from-white to-gray-50">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16 contact-reveal">
            <h2 class="text-5xl md:text-6xl font-black text-gray-900 mb-8">
                Find Our
                <span class="text-contact-revolutionary">Medical Clinic</span>
            </h2>
            <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Conveniently located in the Professional Medical District with state-of-the-art facilities and ample parking
            </p>
        </div>

        <!-- Map Container -->
        <div class="map-revolutionary aspect-video relative contact-reveal" style="animation-delay: 0.2s;">
            <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3961.8269!2d39.2912!3d-6.7977!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x185c4b0c1e5d7f27%3A0x5e0e5e5e5e5e5e5e!2sCity%20Plaza%2C%20Jamhuri%20Street%2C%20Dar%20es%20Salaam%2C%20Tanzania!5e0!3m2!1sen!2stz!4v1703000000000!5m2!1sen!2stz"
                width="100%"
                height="100%"
                style="border:0;"
                allowfullscreen=""
                loading="lazy"
                referrerpolicy="no-referrer-when-downgrade"
                class="absolute inset-0"
                title="Redolence Medi Aesthetics Location Map">
            </iframe>
            
            <!-- Map Overlay Info -->
            <div class="absolute bottom-8 left-8 bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-redolence-green/20 max-w-sm">
                <h3 class="text-xl font-bold text-gray-900 mb-2">Redolence Medi Aesthetics</h3>
                <p class="text-gray-600 text-sm mb-4">
                    Medical Plaza, Professional District<br>
                    Healthcare Avenue, Suite 205<br>
                    Dar es Salaam, Tanzania
                </p>
                <div class="flex space-x-3">
                    <a href="https://maps.app.goo.gl/9R4GdECM3wykgtRv8" target="_blank" 
                       class="inline-flex items-center bg-redolence-green text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-redolence-green/90 transition-all contact-interactive">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                        </svg>
                        Directions
                    </a>
                    <button onclick="openInGoogleMaps()" 
                            class="inline-flex items-center bg-white border border-redolence-green text-redolence-green px-4 py-2 rounded-lg text-sm font-semibold hover:bg-redolence-green/10 transition-all contact-interactive">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        Open
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Advanced Contact Form System
    const form = document.getElementById('revolutionaryContactForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');

    // Form submission handling with advanced animations
    if (form) {
        form.addEventListener('submit', function(e) {
            // Show loading state with animation
            submitBtn.disabled = true;
            submitText.textContent = 'Sending...';
            submitBtn.style.transform = 'scale(0.95)';
            
            // Add loading animation
            const loadingSpinner = document.createElement('div');
            loadingSpinner.innerHTML = `
                <svg class="w-5 h-5 animate-spin inline mr-2" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            `;
            submitBtn.prepend(loadingSpinner.firstElementChild);

            setTimeout(() => {
                if (!e.defaultPrevented) {
                    // Form will submit normally
                }
            }, 200);
        });

        // Reset button state if form submission fails
        window.addEventListener('pageshow', function() {
            submitBtn.disabled = false;
            submitText.textContent = 'Schedule My Consultation';
            submitBtn.style.transform = 'scale(1)';
            
            // Remove loading spinner if exists
            const spinner = submitBtn.querySelector('.animate-spin');
            if (spinner) spinner.remove();
        });
    }

    // Advanced Scroll Reveal System
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                
                // Add staggered animations for form fields
                if (entry.target.classList.contains('contact-card-revolutionary')) {
                    const fields = entry.target.querySelectorAll('.form-field-revolutionary');
                    fields.forEach((field, index) => {
                        setTimeout(() => {
                            field.style.opacity = '1';
                            field.style.transform = 'translateY(0) rotateX(0deg)';
                        }, index * 100);
                    });
                }
            }
        });
    }, observerOptions);

    // Observe all reveal elements
    document.querySelectorAll('.contact-reveal').forEach(el => {
        observer.observe(el);
    });

    // Revolutionary Form Field Interactions
    document.querySelectorAll('.form-field-revolutionary input, .form-field-revolutionary textarea, .form-field-revolutionary select').forEach(field => {
        // Focus animations
        field.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-5px) scale(1.02)';
            this.style.boxShadow = '0 0 0 4px rgba(73, 167, 92, 0.1), 0 10px 30px rgba(73, 167, 92, 0.2)';
        });

        field.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });

        // Typing animations
        field.addEventListener('input', function() {
            this.style.borderColor = 'var(--primary-green)';
            setTimeout(() => {
                this.style.borderColor = '';
            }, 300);
        });
    });

    // Auto-resize textarea
    const messageTextarea = document.getElementById('message');
    if (messageTextarea) {
        messageTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }

    // Simplified Interactive Elements
    document.querySelectorAll('.contact-interactive').forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });

        element.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Simplified Contact Card Hover Effects
    document.querySelectorAll('.contact-card-revolutionary').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Simplified Contact Info Hover Effects
    document.querySelectorAll('.contact-info-revolutionary').forEach(info => {
        info.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        info.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Button Ripple Effects
    document.querySelectorAll('.btn-contact-revolutionary').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('contact-ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 700);
        });
    });

    // Smooth Scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Page Load Animation
    setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transform = 'translateY(0)';
    }, 100);

    // Parallax Effect for Hero Section
    let ticking = false;
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const morphingBgs = document.querySelectorAll('.morphing-contact-bg');
        
        morphingBgs.forEach(bg => {
            const speed = 0.3;
            bg.style.transform = `translateY(${scrolled * speed}px)`;
        });
        
        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    });

    // Form validation enhancements
    const inputs = form?.querySelectorAll('input[required], textarea[required]');
    inputs?.forEach(input => {
        input.addEventListener('invalid', function() {
            this.style.borderColor = '#ef4444';
            this.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
        });

        input.addEventListener('input', function() {
            if (this.validity.valid) {
                this.style.borderColor = 'var(--primary-green)';
                this.style.boxShadow = '0 0 0 3px rgba(73, 167, 92, 0.1)';
            }
        });
    });
});

// Map Functions
function openInGoogleMaps() {
    const url = 'https://www.google.com/maps/search/?api=1&query=Medical+Plaza+Professional+District+Healthcare+Avenue+Dar+es+Salaam+Tanzania';
    window.open(url, '_blank');
}

// Add CSS for ripple effect and page load
const contactStyle = document.createElement('style');
contactStyle.textContent = `
    .contact-ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transform: scale(0);
        animation: contact-ripple-animation 0.7s linear;
        pointer-events: none;
    }
    
    @keyframes contact-ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    body {
        opacity: 1;
        transform: translateY(0);
        transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
    }
    
    .form-field-revolutionary {
        opacity: 1;
        transform: translateY(0) rotateX(0deg);
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(contactStyle);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>