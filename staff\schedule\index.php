<?php
session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action']) && $_POST['action'] === 'update_schedule') {
            $schedule = [];
            $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            
            foreach ($daysOfWeek as $day) {
                if (isset($_POST[$day . '_working']) && $_POST[$day . '_working'] === '1') {
                    $schedule[$day] = [
                        'is_working' => true,
                        'start_time' => $_POST[$day . '_start'],
                        'end_time' => $_POST[$day . '_end']
                    ];
                } else {
                    $schedule[$day] = [
                        'is_working' => false,
                        'start_time' => null,
                        'end_time' => null
                    ];
                }
            }
            
            updateStaffWorkingHours($_SESSION['user_id'], $schedule);
            $message = 'Schedule updated successfully!';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get current week start (Monday)
$weekStart = $_GET['week'] ?? date('Y-m-d', strtotime('monday this week'));

// Get staff schedule data
$staffId = $_SESSION['user_id'];
$profile = getStaffProfile($staffId);
$weeklySchedule = getStaffWeeklySchedule($staffId, $weekStart);

$pageTitle = "Medical Treatment Schedule";
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Message Display -->
<?php if ($message): ?>
    <div class="medical-card mb-8 p-6 <?= $messageType === 'success' ? 'border-redolence-green/30 bg-gradient-to-r from-redolence-green/10 to-redolence-green/5' : 'border-red-500/30 bg-gradient-to-r from-red-500/10 to-red-500/5' ?>">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 <?= $messageType === 'success' ? 'text-redolence-green' : 'text-red-500' ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <?php if ($messageType === 'success'): ?>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    <?php else: ?>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    <?php endif; ?>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium <?= $messageType === 'success' ? 'text-redolence-green' : 'text-red-600' ?>">
                    <?= htmlspecialchars($message) ?>
                </p>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Medical Schedule Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-redolence-navy">Medical Treatment <span class="text-redolence-green">Schedule</span></h1>
            <p class="mt-2 text-lg text-gray-600">Manage your medical consultation hours and patient appointment schedule</p>
            <div class="mt-3 flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Professional Medical Schedule Management
            </div>
        </div>
        <div class="mt-6 sm:mt-0">
            <button onclick="openScheduleModal()" class="medical-btn-primary px-6 py-3 text-sm">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Update Medical Hours
            </button>
        </div>
    </div>
</div>

<!-- Medical Week Navigation -->
<div class="medical-card p-6 mb-8">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
            <a href="?week=<?= date('Y-m-d', strtotime($weekStart . ' -1 week')) ?>"
               class="flex items-center text-gray-600 hover:text-redolence-green transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Previous Week
            </a>
            <div class="text-center">
                <h2 class="text-2xl font-bold text-redolence-navy">
                    Week of <?= date('F j, Y', strtotime($weekStart)) ?>
                </h2>
                <p class="text-sm text-gray-600 mt-1">Medical Treatment Schedule</p>
            </div>
            <a href="?week=<?= date('Y-m-d', strtotime($weekStart . ' +1 week')) ?>"
               class="flex items-center text-gray-600 hover:text-redolence-green transition-colors">
                Next Week
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
        <div>
            <a href="?week=<?= date('Y-m-d', strtotime('monday this week')) ?>"
               class="medical-btn-secondary px-4 py-2 text-sm">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Current Week
            </a>
        </div>
    </div>
</div>

<!-- Medical Weekly Schedule Grid -->
<div class="grid grid-cols-1 lg:grid-cols-7 gap-6">
    <?php foreach ($weeklySchedule as $daySchedule): ?>
        <div class="medical-card p-6">
            <div class="text-center mb-6">
                <h3 class="text-lg font-bold text-redolence-navy"><?= $daySchedule['dayName'] ?></h3>
                <p class="text-sm text-gray-600 font-medium"><?= date('M j', strtotime($daySchedule['date'])) ?></p>

                <?php if ($daySchedule['workingHours'] && $daySchedule['workingHours']['is_working']): ?>
                    <div class="mt-3 px-3 py-1 bg-gradient-to-r from-redolence-green/10 to-redolence-green/5 rounded-full">
                        <p class="text-xs text-redolence-green font-semibold">
                            <?= date('g:i A', strtotime($daySchedule['workingHours']['start_time'])) ?> -
                            <?= date('g:i A', strtotime($daySchedule['workingHours']['end_time'])) ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="mt-3 px-3 py-1 bg-gray-100 rounded-full">
                        <p class="text-xs text-gray-500 font-medium">Medical Day Off</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="space-y-3">
                <?php if (!$daySchedule['workingHours'] || !$daySchedule['workingHours']['is_working']): ?>
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </div>
                        <p class="text-sm text-gray-500 font-medium">Medical Rest Day</p>
                    </div>
                <?php elseif (empty($daySchedule['appointments'])): ?>
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <p class="text-sm text-redolence-green font-medium">Available for Patients</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($daySchedule['appointments'] as $appointment): ?>
                        <div class="bg-gradient-to-r from-white to-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between mb-2">
                                <div class="text-sm font-bold text-redolence-navy">
                                    <?= date('g:i A', strtotime($appointment['start_time'])) ?>
                                </div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    <?php
                                    switch($appointment['status']) {
                                        case 'CONFIRMED': echo 'bg-blue-100 text-blue-800 border border-blue-200'; break;
                                        case 'IN_PROGRESS': echo 'bg-purple-100 text-purple-800 border border-purple-200'; break;
                                        case 'COMPLETED': echo 'bg-green-100 text-green-800 border border-green-200'; break;
                                        case 'CANCELLED': echo 'bg-red-100 text-red-800 border border-red-200'; break;
                                        default: echo 'bg-gray-100 text-gray-800 border border-gray-200';
                                    }
                                    ?>">
                                    <?= ucfirst(strtolower($appointment['status'])) ?>
                                </span>
                            </div>
                            <div class="text-sm font-semibold text-gray-900 truncate">
                                <?= htmlspecialchars($appointment['customer_name']) ?>
                            </div>
                            <div class="text-xs text-gray-600 truncate mt-1">
                                <?= htmlspecialchars($appointment['service_name']) ?>
                            </div>
                            <div class="text-xs text-redolence-green font-medium mt-2">
                                Medical Treatment Session
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
                    </div>
                <?php endforeach; ?>
            </div>
<!-- Medical Schedule Modal -->
<div id="scheduleModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="medical-glass max-w-3xl w-full max-h-screen overflow-y-auto" style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95)); backdrop-filter: blur(20px);">
            <div class="p-8">
                <div class="flex items-center justify-between mb-8">
                    <div>
                        <h3 class="text-2xl font-bold text-redolence-navy">Update Medical Hours</h3>
                        <p class="text-gray-600 mt-1">Configure your medical consultation availability</p>
                    </div>
                    <button onclick="closeScheduleModal()" class="text-gray-500 hover:text-redolence-green transition-colors p-2 rounded-lg hover:bg-redolence-green/10">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="action" value="update_schedule">

                        <?php 
                        $daysOfWeek = [
                            'monday' => 'Monday',
                            'tuesday' => 'Tuesday', 
                            'wednesday' => 'Wednesday',
                            'thursday' => 'Thursday',
                            'friday' => 'Friday',
                            'saturday' => 'Saturday',
                            'sunday' => 'Sunday'
                        ];
                        
                        // Get current working hours
                        $currentSchedule = [];
                        foreach ($weeklySchedule as $daySchedule) {
                            $dayKey = strtolower($daySchedule['dayName']);
                            $currentSchedule[$dayKey] = $daySchedule['workingHours'];
                        }
                        ?>

                    <?php foreach ($daysOfWeek as $dayKey => $dayName): ?>
                        <div class="medical-card p-6 border border-redolence-green/20">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-semibold text-redolence-navy"><?= $dayName ?></h4>
                                <label class="flex items-center">
                                    <input type="checkbox" name="<?= $dayKey ?>_working" value="1"
                                           <?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey] && $currentSchedule[$dayKey]['is_working']) ? 'checked' : '' ?>
                                           onchange="toggleDaySchedule('<?= $dayKey ?>')"
                                           class="rounded border-redolence-green/30 text-redolence-green focus:ring-redolence-green focus:ring-offset-0 w-5 h-5">
                                    <span class="ml-3 text-sm font-medium text-gray-700">Medical Consultation Day</span>
                                </label>
                            </div>

                            <div id="<?= $dayKey ?>_schedule" class="grid grid-cols-2 gap-6 <?= (!isset($currentSchedule[$dayKey]) || !$currentSchedule[$dayKey] || !$currentSchedule[$dayKey]['is_working']) ? 'hidden' : '' ?>">
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Treatment Start Time</label>
                                    <input type="time" name="<?= $dayKey ?>_start"
                                           value="<?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey]) ? $currentSchedule[$dayKey]['start_time'] : '09:00' ?>"
                                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-redolence-navy mb-3">Treatment End Time</label>
                                    <input type="time" name="<?= $dayKey ?>_end"
                                           value="<?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey]) ? $currentSchedule[$dayKey]['end_time'] : '17:00' ?>"
                                           class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <div class="flex items-center justify-end space-x-4 pt-8 border-t border-redolence-green/20">
                        <button type="button" onclick="closeScheduleModal()"
                                class="medical-btn-secondary px-6 py-3">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Cancel
                        </button>
                        <button type="submit"
                                class="medical-btn-primary px-6 py-3">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update Medical Schedule
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

    <script>
        function openScheduleModal() {
            document.getElementById('scheduleModal').classList.remove('hidden');
        }

        function closeScheduleModal() {
            document.getElementById('scheduleModal').classList.add('hidden');
        }

        function toggleDaySchedule(day) {
            const checkbox = document.querySelector(`input[name="${day}_working"]`);
            const scheduleDiv = document.getElementById(`${day}_schedule`);
            
            if (checkbox.checked) {
                scheduleDiv.classList.remove('hidden');
            } else {
                scheduleDiv.classList.add('hidden');
            }
        }

        // Close modal when clicking outside
        document.getElementById('scheduleModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeScheduleModal();
            }
        });
    </script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
