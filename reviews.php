<?php
/**
 * Reviews Page - Medical Aesthetics Redesign
 * Redolence Medi Aesthetics - Advanced Medical Beauty Center
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Patient Reviews - Redolence Medi Aesthetics";
$pageDescription = "Read authentic patient reviews and testimonials about medical aesthetic treatments at Redolence Medi Aesthetics.";

include __DIR__ . '/includes/header.php';

// Sample patient reviews data
$reviews = [
    [
        'name' => '<PERSON>',
        'treatment' => 'Facial Rejuvenation Package',
        'rating' => 5,
        'date' => '2024-01-15',
        'review' => 'The results exceeded my expectations! Dr<PERSON> and the team at Redolence are true professionals. The facial rejuvenation treatment has given me back my confidence. The clinic is state-of-the-art and the staff made me feel comfortable throughout the entire process.',
        'verified' => true,
        'before_after' => true
    ],
    [
        'name' => '<PERSON>.',
        'treatment' => 'Laser Hair Removal',
        'rating' => 5,
        'date' => '2024-01-10',
        'review' => 'Outstanding service and results! I completed my laser hair removal package and couldn\'t be happier. The medical team was knowledgeable, professional, and made sure I was comfortable during every session. Highly recommend Redolence for anyone considering medical aesthetics.',
        'verified' => true,
        'before_after' => false
    ],
    [
        'name' => '<PERSON>.',
        'treatment' => 'Injectable Treatments',
        'rating' => 5,
        'date' => '2024-01-08',
        'review' => 'Dr. Smith has an artistic eye and medical expertise that\'s unmatched. My injectable treatment looks completely natural - exactly what I wanted. The consultation was thorough and they explained every step. The results speak for themselves!',
        'verified' => true,
        'before_after' => true
    ],
    [
        'name' => 'David K.',
        'treatment' => 'Body Contouring',
        'rating' => 5,
        'date' => '2024-01-05',
        'review' => 'The CoolSculpting treatment at Redolence was a game-changer. The medical staff was professional and the facility is top-notch. I saw noticeable results after just one session, and the final outcome after my full treatment plan was amazing.',
        'verified' => true,
        'before_after' => true
    ],
    [
        'name' => 'Lisa T.',
        'treatment' => 'Chemical Peel Series',
        'rating' => 5,
        'date' => '2024-01-03',
        'review' => 'My skin has never looked better! The chemical peel series was customized for my specific skin concerns. The medical aesthetician was knowledgeable and guided me through the entire process. The results are incredible - my skin is glowing!',
        'verified' => true,
        'before_after' => false
    ],
    [
        'name' => 'Robert H.',
        'treatment' => 'Laser Skin Resurfacing',
        'rating' => 5,
        'date' => '2023-12-28',
        'review' => 'Exceptional care from start to finish. The laser skin resurfacing treatment has dramatically improved my skin texture and appearance. Dr. Johnson explained everything clearly and the results are exactly what we discussed during consultation.',
        'verified' => true,
        'before_after' => true
    ]
];

$stats = [
    'total_reviews' => 847,
    'average_rating' => 4.9,
    'five_star_percentage' => 94,
    'verified_percentage' => 100
];
?>

<!-- Revolutionary Medical Aesthetics CSS -->
<style>
/* Advanced Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
}

/* Revolutionary Animation Framework */
@keyframes morphingReviewBg {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes floatingReview {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(-5px) rotate(-0.5deg); }
}

@keyframes slideInReview {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes scaleInReview {
    0% { opacity: 0; transform: scale(0.95); }
    100% { opacity: 1; transform: scale(1); }
}

@keyframes starGlow {
    0%, 100% { filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5)); }
    50% { filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.8)); }
}

/* Medical Review Card System */
.review-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    padding: 2.5rem;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.review-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.review-card:hover::before {
    left: 100%;
}

.review-card:hover {
    transform: translateY(-8px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

.review-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.review-avatar {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
    margin-right: 1rem;
    position: relative;
}

.review-avatar::after {
    content: '';
    position: absolute;
    inset: -3px;
    background: var(--gradient-primary);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
}

.star-rating {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.star {
    width: 20px;
    height: 20px;
    color: #fbbf24;
    animation: starGlow 2s ease-in-out infinite;
}

.star:nth-child(2) { animation-delay: 0.2s; }
.star:nth-child(3) { animation-delay: 0.4s; }
.star:nth-child(4) { animation-delay: 0.6s; }
.star:nth-child(5) { animation-delay: 0.8s; }

.verified-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #059669;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-top: 1rem;
}

.treatment-tag {
    display: inline-block;
    background: var(--gradient-soft);
    color: var(--primary-green);
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px var(--shadow-primary);
}

.stats-number {
    font-size: 3rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.filter-button {
    background: rgba(73, 167, 92, 0.1);
    border: 2px solid rgba(73, 167, 92, 0.2);
    color: var(--primary-green);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-button:hover,
.filter-button.active {
    background: var(--primary-green);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(73, 167, 92, 0.3);
}

.review-content {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #374151;
    margin-bottom: 1.5rem;
}

.review-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .review-card {
        padding: 2rem;
        margin: 0;
        border-radius: 16px;
    }
    
    .review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .review-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stats-card {
        padding: 1.5rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
    }
    
    .filter-button {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* Medical Professional Styling */
.testimonial-highlight {
    background: linear-gradient(135deg, var(--gradient-soft), rgba(255, 255, 255, 0.1));
    border-radius: 30px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
    margin: 3rem 0;
}

.testimonial-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(73, 167, 92, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.testimonial-highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(88, 148, 210, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.quote-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.rating-breakdown {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    border: 2px solid rgba(73, 167, 92, 0.1);
}

.rating-bar {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.rating-bar-fill {
    height: 8px;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width 0.8s ease;
}

.rating-bar-bg {
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    flex: 1;
    margin: 0 1rem;
    overflow: hidden;
}
</style>

<!-- Revolutionary Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Dynamic Medical Background -->
    <div class="absolute inset-0 bg-gradient-to-br from-redolence-green via-redolence-blue to-redolence-green bg-[length:400%_400%] animate-[morphingReviewBg_15s_ease_infinite]"></div>
    
    <!-- Medical Pattern Overlay -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 75% 75%, var(--primary-blue) 2px, transparent 2px); background-size: 60px 60px;"></div>
    </div>
    
    <!-- Floating Medical Elements -->
    <div class="absolute inset-0 pointer-events-none">
        <div class="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full backdrop-blur-sm animate-[floatingReview_12s_ease-in-out_infinite]"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-white/15 rounded-full backdrop-blur-sm animate-[floatingReview_12s_ease-in-out_infinite] [animation-delay:4s]"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-white/8 rounded-full backdrop-blur-sm animate-[floatingReview_12s_ease-in-out_infinite] [animation-delay:8s]"></div>
        <div class="absolute bottom-20 right-10 w-28 h-28 bg-white/12 rounded-full backdrop-blur-sm animate-[floatingReview_12s_ease-in-out_infinite] [animation-delay:12s]"></div>
    </div>
    
    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white px-6 max-w-6xl mx-auto">
        <div class="animate-[scaleInReview_0.8s_ease_0.2s_both]">
            <div class="inline-flex items-center bg-white/20 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-white/30">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
                <span class="font-semibold text-lg">VERIFIED PATIENT REVIEWS</span>
            </div>
        </div>
        
        <h1 class="text-6xl md:text-8xl font-black mb-8 leading-none animate-[slideInReview_1s_ease_0.4s_both]">
            Patient
            <span class="block text-5xl md:text-7xl font-light opacity-90">Reviews</span>
        </h1>
        
        <p class="text-xl md:text-3xl font-light mb-12 leading-relaxed animate-[slideInReview_1s_ease_0.6s_both]">
            Real stories from <strong>satisfied patients</strong>
            <span class="block mt-4 text-lg md:text-xl opacity-80">Authentic experiences with medical aesthetics excellence</span>
        </p>
        
        <!-- Review Stats Preview -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto animate-[scaleInReview_0.8s_ease_0.8s_both]">
            <div class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl">
                <div class="text-3xl font-bold mb-2"><?= $stats['total_reviews'] ?>+</div>
                <div class="text-sm opacity-80">Total Reviews</div>
            </div>
            <div class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl">
                <div class="text-3xl font-bold mb-2"><?= $stats['average_rating'] ?></div>
                <div class="text-sm opacity-80">Average Rating</div>
            </div>
            <div class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl">
                <div class="text-3xl font-bold mb-2"><?= $stats['five_star_percentage'] ?>%</div>
                <div class="text-sm opacity-80">5-Star Reviews</div>
            </div>
            <div class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl">
                <div class="text-3xl font-bold mb-2"><?= $stats['verified_percentage'] ?>%</div>
                <div class="text-sm opacity-80">Verified Patients</div>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Revolutionary Reviews Content Section -->
<section class="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 30% 30%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 70% 70%, var(--primary-blue) 2px, transparent 2px); background-size: 80px 80px;"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 relative">
        
        <!-- Review Statistics Section -->
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 px-6 py-3 rounded-full mb-8 border border-redolence-green/20">
                <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="font-semibold text-redolence-green">PATIENT SATISFACTION METRICS</span>
            </div>
            
            <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Trusted by Hundreds of
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-redolence-green to-redolence-blue">Satisfied Patients</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our commitment to medical excellence and patient care is reflected in these authentic reviews from real patients.
            </p>
        </div>

        <!-- Detailed Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
            <div class="stats-card">
                <div class="stats-number"><?= $stats['total_reviews'] ?>+</div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">Total Reviews</h3>
                <p class="text-gray-600">Verified patient testimonials</p>
            </div>
            
            <div class="stats-card">
                <div class="stats-number"><?= $stats['average_rating'] ?></div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">Average Rating</h3>
                <div class="star-rating justify-center">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    <?php endfor; ?>
                </div>
            </div>
            
            <div class="stats-card">
                <div class="stats-number"><?= $stats['five_star_percentage'] ?>%</div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">5-Star Reviews</h3>
                <p class="text-gray-600">Exceptional satisfaction rate</p>
            </div>
            
            <div class="stats-card">
                <div class="stats-number"><?= $stats['verified_percentage'] ?>%</div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">Verified Patients</h3>
                <p class="text-gray-600">Authentic testimonials only</p>
            </div>
        </div>

        <!-- Rating Breakdown -->
        <div class="rating-breakdown mb-20 max-w-2xl mx-auto">
            <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">Rating Breakdown</h3>
            
            <?php 
            $ratingData = [
                5 => 94,
                4 => 5,
                3 => 1,
                2 => 0,
                1 => 0
            ];
            ?>
            
            <?php foreach ($ratingData as $stars => $percentage): ?>
                <div class="rating-bar">
                    <span class="text-sm font-medium text-gray-700 w-12"><?= $stars ?> star</span>
                    <div class="rating-bar-bg">
                        <div class="rating-bar-fill" style="width: <?= $percentage ?>%"></div>
                    </div>
                    <span class="text-sm font-medium text-gray-700 w-12 text-right"><?= $percentage ?>%</span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Filter Buttons -->
        <div class="text-center mb-16">
            <h3 class="text-2xl font-bold text-gray-900 mb-8">Filter Reviews by Treatment</h3>
            <div class="flex flex-wrap justify-center gap-4">
                <button class="filter-button active" data-filter="all">All Reviews</button>
                <button class="filter-button" data-filter="facial">Facial Treatments</button>
                <button class="filter-button" data-filter="injectable">Injectable Treatments</button>
                <button class="filter-button" data-filter="laser">Laser Procedures</button>
                <button class="filter-button" data-filter="body">Body Contouring</button>
                <button class="filter-button" data-filter="skin">Skin Treatments</button>
            </div>
        </div>

        <!-- Reviews Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20" id="reviewsGrid">
            <?php foreach ($reviews as $index => $review): ?>
                <div class="review-card" data-treatment="<?= strtolower(explode(' ', $review['treatment'])[0]) ?>" style="animation-delay: <?= $index * 0.1 ?>s;">
                    <div class="review-header">
                        <div class="review-avatar">
                            <?= strtoupper(substr($review['name'], 0, 1)) ?>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold text-gray-900 text-lg"><?= htmlspecialchars($review['name']) ?></h4>
                            <div class="star-rating">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="treatment-tag">
                        <?= htmlspecialchars($review['treatment']) ?>
                    </div>
                    
                    <div class="review-content">
                        "<?= htmlspecialchars($review['review']) ?>"
                    </div>
                    
                    <div class="review-meta">
                        <span><?= date('F j, Y', strtotime($review['date'])) ?></span>
                        <?php if ($review['before_after']): ?>
                            <span class="text-redolence-green font-semibold">Before/After Available</span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($review['verified']): ?>
                        <div class="verified-badge">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Verified Patient
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Featured Testimonial -->
        <div class="testimonial-highlight relative">
            <div class="relative z-10 text-center">
                <div class="quote-icon mx-auto">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M6.5 10c-.223 0-.437.034-.65.065.069-.232.14-.468.254-.68.114-.308.292-.575.469-.844.148-.291.409-.488.601-.737.201-.242.475-.403.692-.604.213-.21.492-.315.714-.463.232-.133.434-.28.65-.35l.539-.222.474-.197-.485-1.938-.597.144c-.191.048-.424.104-.689.171-.271.05-.56.187-.882.312-.318.142-.686.238-1.028.466-.344.218-.741.4-1.091.692-.339.301-.748.562-1.05.945-.33.358-.656.734-.909 1.162-.293.408-.492.856-.702 1.299-.19.443-.343.896-.468 1.336-.237.882-.343 1.72-.384 2.437-.034.718-.014 1.315.028 1.747.015.204.043.402.063.539l.025.168.026-.006A4.5 4.5 0 1011.5 10h-5zm11 0c-.223 0-.437.034-.65.065.069-.232.14-.468.254-.68.114-.308.292-.575.469-.844.148-.291.409-.488.601-.737.201-.242.475-.403.692-.604.213-.21.492-.315.714-.463.232-.133.434-.28.65-.35l.539-.222.474-.197-.485-1.938-.597.144c-.191.048-.424.104-.689.171-.271.05-.56.187-.882.312-.318.142-.686.238-1.028.466-.344.218-.741.4-1.091.692-.339.301-.748.562-1.05.945-.33.358-.656.734-.909 1.162-.293.408-.492.856-.702 1.299-.19.443-.343.896-.468 1.336-.237.882-.343 1.72-.384 2.437-.034.718-.014 1.315.028 1.747.015.204.043.402.063.539l.025.168.026-.006A4.5 4.5 0 1022.5 10h-5z"></path>
                    </svg>
                </div>
                <blockquote class="text-2xl md:text-3xl font-light text-gray-900 mb-8 leading-relaxed">
                    "Redolence Medi Aesthetics has completely transformed my confidence. The medical team's expertise and the state-of-the-art facility exceeded all my expectations. I couldn't be happier with my results!"
                </blockquote>
                <div class="flex items-center justify-center">
                    <div class="review-avatar mr-4">
                        A
                    </div>
                    <div class="text-left">
                        <div class="font-bold text-gray-900 text-lg">Amanda K.</div>
                        <div class="text-gray-600">Comprehensive Facial Rejuvenation</div>
                        <div class="star-rating">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <div class="review-card max-w-4xl mx-auto">
                <div class="quote-icon mx-auto mb-8">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Ready to Join Our Satisfied Patients?
                </h2>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Experience the same exceptional care and outstanding results that our patients rave about.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="contact.php" class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-redolence-blue hover:from-redolence-blue hover:to-redolence-green text-white px-8 py-4 rounded-xl font-bold text-lg transition-all hover:scale-105 shadow-lg">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Schedule Your Consultation
                    </a>
                    <a href="tel:+255781985757" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-green px-8 py-4 rounded-xl font-bold text-lg transition-all border-2 border-redolence-green/20 hover:border-redolence-green/40">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Call: +255 781 985 757
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary JavaScript -->
<script>
// Enhanced reviews functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth entrance animations to review cards
    const reviewCards = document.querySelectorAll('.review-card');
    reviewCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.23, 1, 0.32, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });

    // Filter functionality
    const filterButtons = document.querySelectorAll('.filter-button');
    const reviewsGrid = document.getElementById('reviewsGrid');
    const allReviews = document.querySelectorAll('.review-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');

            // Filter reviews with animation
            allReviews.forEach((review, index) => {
                const treatmentType = review.getAttribute('data-treatment');
                const shouldShow = filter === 'all' || treatmentType.includes(filter);

                if (shouldShow) {
                    setTimeout(() => {
                        review.style.display = 'block';
                        review.style.opacity = '0';
                        review.style.transform = 'translateY(20px)';
                        
                        setTimeout(() => {
                            review.style.transition = 'all 0.4s ease';
                            review.style.opacity = '1';
                            review.style.transform = 'translateY(0)';
                        }, 50);
                    }, index * 50);
                } else {
                    review.style.transition = 'all 0.3s ease';
                    review.style.opacity = '0';
                    review.style.transform = 'translateY(-20px)';
                    
                    setTimeout(() => {
                        review.style.display = 'none';
                    }, 300);
                }
            });
        });
    });

    // Animate rating bars
    const ratingBars = document.querySelectorAll('.rating-bar-fill');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const bar = entry.target;
                const width = bar.style.width;
                bar.style.width = '0%';
                
                setTimeout(() => {
                    bar.style.transition = 'width 1s ease-out';
                    bar.style.width = width;
                }, 200);
            }
        });
    }, { threshold: 0.5 });

    ratingBars.forEach(bar => observer.observe(bar));

    // Add hover effects to review cards
    reviewCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add stats counter animation
    const statsNumbers = document.querySelectorAll('.stats-number');
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const finalValue = element.textContent;
                const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));
                
                if (!isNaN(numericValue)) {
                    animateCounter(element, 0, numericValue, finalValue);
                }
            }
        });
    }, { threshold: 0.5 });

    statsNumbers.forEach(stat => statsObserver.observe(stat));

    // Counter animation function
    function animateCounter(element, start, end, finalText) {
        const duration = 2000;
        const increment = end / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                element.textContent = finalText;
                clearInterval(timer);
            } else {
                const suffix = finalText.includes('+') ? '+' : (finalText.includes('%') ? '%' : '');
                element.textContent = Math.floor(current) + suffix;
            }
        }, 16);
    }

    // Add star animation on hover
    const starRatings = document.querySelectorAll('.star-rating');
    starRatings.forEach(rating => {
        const stars = rating.querySelectorAll('.star');
        
        stars.forEach((star, index) => {
            star.addEventListener('mouseenter', function() {
                stars.forEach((s, i) => {
                    if (i <= index) {
                        s.style.transform = 'scale(1.2)';
                        s.style.filter = 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.8))';
                    }
                });
            });
            
            star.addEventListener('mouseleave', function() {
                stars.forEach(s => {
                    s.style.transform = 'scale(1)';
                    s.style.filter = 'drop-shadow(0 0 5px rgba(255, 215, 0, 0.5))';
                });
            });
        });
    });
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>