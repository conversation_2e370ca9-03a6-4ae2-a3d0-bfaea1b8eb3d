<?php
/**
 * Mobile Menu Component
 * DEVELOPED BY CHARLES VENTURE MY SELF 
 * This component is rendered at the body level to ensure proper z-index stacking
 */

$basePath = getBasePath();
$currentPath = $_SERVER['REQUEST_URI'];

// Get service categories for mobile dropdown (random selection)
require_once __DIR__ . '/service_category_functions.php';
$serviceCategories = getActiveServiceCategories();

// Shuffle categories and take up to 5 for random display
if (!empty($serviceCategories)) {
    shuffle($serviceCategories);
    $displayCategories = array_slice($serviceCategories, 0, 5);
    $hasMoreCategories = count($serviceCategories) > 5;
} else {
    $displayCategories = [];
    $hasMoreCategories = false;
}
?>

<!-- Mobile Menu Overlay - Positioned at body level for proper z-index -->
<div id="mobileMenuOverlay" class="mobile-menu hidden lg:hidden fixed inset-0 z-[9999]">
    <!-- Background Overlay -->
    <div class="fixed inset-0 bg-black/50 mobile-menu-backdrop" onclick="closeMobileMenu()"></div>
    
    <!-- Mobile Menu Panel -->
    <div class="mobile-menu-panel fixed inset-y-0 right-0 z-[9999] w-80 max-w-[50vw] overflow-y-auto bg-salon-black px-6 py-6 shadow-2xl">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
            <a href="<?= $basePath ?>/" class="-m-1.5 p-1.5">
                <span class="text-xl font-bold font-serif text-salon-gold">
                    Flix Salon & Spa
                </span>
            </a>
            <button type="button" 
                    class="mobile-menu-close -m-2.5 rounded-md p-2.5 text-gray-400 hover:text-white transition-colors"
                    onclick="closeMobileMenu()">
                <span class="sr-only">Close menu</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Navigation Links -->
        <div class="flow-root">
            <div class="-my-6 divide-y divide-secondary-700">
                <div class="space-y-2 py-6">
                    <!-- Home -->
                    <?php $isActive = $currentPath === $basePath . '/'; ?>
                    <a href="<?= $basePath ?>/"
                       class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors <?= $isActive ? 'text-salon-gold bg-salon-gold/10' : 'text-white hover:text-salon-gold hover:bg-salon-gold/5' ?>">
                        Home
                    </a>

                    <!-- Services with Expandable Categories -->
                    <?php $isServicesActive = $currentPath === $basePath . '/services' || strpos($currentPath, $basePath . '/services') === 0; ?>
                    <div class="services-dropdown">
                        <button onclick="toggleMobileServicesDropdown()"
                                class="-mx-3 w-full flex items-center justify-between rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors <?= $isServicesActive ? 'text-salon-gold bg-salon-gold/10' : 'text-white hover:text-salon-gold hover:bg-salon-gold/5' ?>">
                            <span>Services</span>
                            <svg id="servicesDropdownIcon" class="h-5 w-5 transition-transform" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                            </svg>
                        </button>

                        <!-- Services Dropdown Content -->
                        <div id="mobileServicesDropdown" class="hidden ml-3 mt-2 space-y-1">
                            <!-- Random Categories Header -->
                            <?php if (!empty($displayCategories)): ?>
                                <div class="flex items-center justify-between px-3 py-1 mb-2">
                                    <span class="text-xs font-medium text-salon-gold">✨ Featured Categories</span>
                                    <button onclick="refreshDropdownCategories()"
                                            class="text-xs text-gray-400 hover:text-salon-gold transition-colors p-1"
                                            title="Refresh categories">
                                        <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0013.803-3.7l3.181 3.182m0-4.991v4.99" />
                                        </svg>
                                    </button>
                                </div>
                            <?php endif; ?>

                            <!-- All Services Link -->
                            <a href="<?= $basePath ?>/services"
                               class="block rounded-lg px-3 py-2 text-sm text-gray-300 hover:text-salon-gold hover:bg-salon-gold/5 transition-colors">
                                <svg class="inline-block w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6z" />
                                </svg>
                                All Services
                            </a>

                            <?php if (!empty($displayCategories)): ?>
                                <!-- Category Links -->
                                <?php foreach ($displayCategories as $category): ?>
                                    <a href="<?= $basePath ?>/services?category=<?= urlencode($category['name']) ?>"
                                       class="block rounded-lg px-3 py-2 text-sm text-gray-300 hover:text-salon-gold hover:bg-salon-gold/5 transition-colors">
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3 flex-shrink-0"></div>
                                            <div>
                                                <div class="font-medium"><?= htmlspecialchars($category['name']) ?></div>
                                                <?php if ($category['description']): ?>
                                                    <div class="text-xs text-gray-400 line-clamp-1"><?= htmlspecialchars($category['description']) ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </a>
                                <?php endforeach; ?>

                                <?php if ($hasMoreCategories): ?>
                                    <a href="<?= $basePath ?>/services"
                                       class="block rounded-lg px-3 py-2 text-sm text-salon-gold hover:bg-salon-gold/5 transition-colors">
                                        <svg class="inline-block w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                                        </svg>
                                        View More Categories
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Other Navigation Items -->
                    <?php
                    $otherNavigation = [
                        ['name' => 'Packages', 'href' => $basePath . '/packages'],
                        ['name' => 'Offers', 'href' => $basePath . '/offers'],
                        ['name' => 'Gallery', 'href' => $basePath . '/gallery'],
                        ['name' => 'Contact', 'href' => $basePath . '/contact'],
                    ];

                    foreach ($otherNavigation as $item):
                        $isActive = $currentPath === $item['href'] || ($item['href'] !== $basePath . '/' && strpos($currentPath, $item['href']) === 0);
                    ?>
                        <a href="<?= $item['href'] ?>"
                           class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors <?= $isActive ? 'text-salon-gold bg-salon-gold/10' : 'text-white hover:text-salon-gold hover:bg-salon-gold/5' ?>">
                            <?= $item['name'] ?>
                        </a>
                    <?php endforeach; ?>
                </div>

                <!-- Action Buttons Section -->
                <div class="py-6 space-y-4">
                    <!-- Book Now Button -->
                    <?php if (isLoggedIn()): ?>
                        <a href="<?= $basePath ?>/customer/book" 
                           class="w-full bg-salon-gold text-black py-3 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors inline-block text-center">
                            <svg class="inline-block w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 715.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                            </svg>
                            Book Appointment
                        </a>
                    <?php else: ?>
                        <a href="<?= $basePath ?>/services" 
                           class="w-full bg-salon-gold text-black py-3 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors inline-block text-center">
                            <svg class="inline-block w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5a2.25 2.25 0 0 0 2.25-2.25m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5a2.25 2.25 0 0 1 2.25 2.25v7.5" />
                            </svg>
                            Book Appointment
                        </a>
                    <?php endif; ?>
                    
                    <!-- User Account Section -->
                    <?php if (isLoggedIn()): ?>
                        <?php $user = getCurrentUser(); ?>
                        <div class="space-y-3">
                            <div class="text-center py-2 border-t border-secondary-700">
                                <p class="text-sm text-gray-400">Welcome back,</p>
                                <p class="text-salon-gold font-semibold"><?= htmlspecialchars(explode(' ', $user['name'])[0]) ?></p>
                            </div>
                            
                            <a href="<?= $basePath . ($user['role'] === 'ADMIN' ? '/admin' : ($user['role'] === 'STAFF' ? '/staff' : '/customer')) ?>"
                               class="w-full border border-salon-gold text-salon-gold py-3 px-4 rounded-lg font-semibold hover:bg-salon-gold hover:text-black transition-colors inline-block text-center">
                                <svg class="inline-block w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                                </svg>
                                <?= $user['role'] === 'ADMIN' ? 'Admin Dashboard' : ($user['role'] === 'STAFF' ? 'Staff Dashboard' : 'My Dashboard') ?>
                            </a>
                            
                            <?php if ($user['role'] === 'CUSTOMER'): ?>
                                <a href="<?= $basePath ?>/customer/bookings"
                                   class="w-full border border-gray-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-600 transition-colors inline-block text-center">
                                    <svg class="inline-block w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5a2.25 2.25 0 0 0 2.25-2.25m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5a2.25 2.25 0 0 1 2.25 2.25v7.5" />
                                    </svg>
                                    My Bookings
                                </a>
                            <?php endif; ?>
                            
                            <a href="<?= $basePath ?>/auth/logout.php" 
                               class="w-full text-red-400 py-3 px-4 rounded-lg font-semibold hover:bg-red-500/10 hover:text-red-300 transition-colors inline-block text-center">
                                <svg class="inline-block w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                                </svg>
                                Logout
                            </a>
                        </div>
                    <?php else: ?>
                        <a href="<?= $basePath ?>/auth/login.php" 
                           class="w-full border border-salon-gold text-salon-gold py-3 px-4 rounded-lg font-semibold hover:bg-salon-gold hover:text-black transition-colors inline-block text-center">
                            <svg class="inline-block w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            Login / Register
                        </a>
                    <?php endif; ?>

                    <!-- Contact Info -->
                    <div class="flex items-center justify-center pt-4">
                        <a href="tel:+255745456789" class="flex items-center text-salon-gold hover:text-gold-light transition-colors">
                            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                            </svg>
                            +255 745 456 789
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Mobile Menu Styles - Positioned at body level with smooth animations */
.mobile-menu {
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                visibility 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, visibility;
}

.mobile-menu.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.mobile-menu:not(.hidden) {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Backdrop smooth fade */
.mobile-menu-backdrop {
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
}

.mobile-menu.hidden .mobile-menu-backdrop {
    opacity: 0;
}

.mobile-menu:not(.hidden) .mobile-menu-backdrop {
    opacity: 1;
}

/* Panel smooth slide animation */
.mobile-menu-panel {
    transform: translateX(100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.mobile-menu:not(.hidden) .mobile-menu-panel {
    transform: translateX(0);
}

/* Ensure mobile menu is always on top */
.mobile-menu {
    z-index: 9999 !important;
}

.mobile-menu-backdrop {
    z-index: 9998 !important;
}

.mobile-menu-panel {
    z-index: 9999 !important;
}

/* Body scroll lock when menu is open - Handled by JavaScript for scroll preservation */
body.mobile-menu-open {
    overflow: hidden !important;
}

/* Smooth hover effects for menu items */
.mobile-menu a {
    transition: all 0.2s ease-in-out;
}

.mobile-menu button {
    transition: all 0.2s ease-in-out;
}

/* Responsive adjustments for half-screen menu */
@media (max-width: 480px) {
    .mobile-menu-panel {
        width: 75vw !important;
        max-width: 75vw !important;
    }
}

@media (max-width: 360px) {
    .mobile-menu-panel {
        width: 85vw !important;
        max-width: 85vw !important;
    }
}

/* Hardware acceleration for better performance */
.mobile-menu,
.mobile-menu-backdrop,
.mobile-menu-panel {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}
</style>

<script>
// Mobile Menu JavaScript - Global functions with smooth animations and scroll preservation
let isMenuAnimating = false;
let scrollPosition = 0;
let bodyOriginalStyles = {};

// Mobile Services Dropdown Toggle
function toggleMobileServicesDropdown() {
    const dropdown = document.getElementById('mobileServicesDropdown');
    const icon = document.getElementById('servicesDropdownIcon');

    if (dropdown && icon) {
        const isHidden = dropdown.classList.contains('hidden');

        if (isHidden) {
            dropdown.classList.remove('hidden');
            icon.style.transform = 'rotate(180deg)';
        } else {
            dropdown.classList.add('hidden');
            icon.style.transform = 'rotate(0deg)';
        }
    }
}

function openMobileMenu() {
    if (isMenuAnimating) return;

    const mobileMenu = document.getElementById('mobileMenuOverlay');
    if (mobileMenu) {
        isMenuAnimating = true;

        // Store current scroll position
        scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

        // Store original body styles for restoration
        bodyOriginalStyles = {
            position: document.body.style.position,
            top: document.body.style.top,
            width: document.body.style.width,
            overflow: document.body.style.overflow
        };

        // Apply scroll lock while preserving position
        document.body.style.top = `-${scrollPosition}px`;
        document.body.style.position = 'fixed';
        document.body.style.width = '100%';
        document.body.style.overflow = 'hidden';
        document.body.classList.add('mobile-menu-open');

        // Show menu with smooth animation
        mobileMenu.classList.remove('hidden');

        // Force reflow to ensure the hidden class is removed before animation
        mobileMenu.offsetHeight;

        // Reset animation flag after transition completes
        setTimeout(() => {
            isMenuAnimating = false;
        }, 400);
    }
}

function closeMobileMenu() {
    if (isMenuAnimating) return;

    const mobileMenu = document.getElementById('mobileMenuOverlay');
    if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
        isMenuAnimating = true;

        // Start closing animation
        mobileMenu.classList.add('hidden');

        // Remove body scroll lock and restore scroll position after animation completes
        setTimeout(() => {
            document.body.classList.remove('mobile-menu-open');

            // Restore original body styles
            document.body.style.position = bodyOriginalStyles.position || '';
            document.body.style.top = bodyOriginalStyles.top || '';
            document.body.style.width = bodyOriginalStyles.width || '';
            document.body.style.overflow = bodyOriginalStyles.overflow || '';

            // Restore scroll position smoothly
            if (scrollPosition > 0) {
                window.scrollTo({
                    top: scrollPosition,
                    behavior: 'instant' // Use instant to avoid animation conflicts
                });
            }

            isMenuAnimating = false;
        }, 400);
    }
}

// Initialize mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMobileMenu();
        }
    });

    // Safety mechanism: restore scroll if page is refreshed with menu open
    if (document.body.classList.contains('mobile-menu-open')) {
        document.body.classList.remove('mobile-menu-open');
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.overflow = '';
    }

    // Close menu when clicking on navigation links
    const mobileNavLinks = document.querySelectorAll('#mobileMenuOverlay a');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Don't close immediately for external links
            if (this.hostname !== window.location.hostname) {
                return;
            }

            // Smooth close with slight delay for better UX
            setTimeout(() => {
                closeMobileMenu();
            }, 150);
        });
    });

    // Prevent menu from closing when clicking inside the panel
    const menuPanel = document.querySelector('.mobile-menu-panel');
    if (menuPanel) {
        menuPanel.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Add smooth scroll behavior for anchor links
    const anchorLinks = document.querySelectorAll('#mobileMenuOverlay a[href*="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href.includes('#') && href !== '#') {
                // Close menu immediately for anchor links
                closeMobileMenu();
            }
        });
    });
});
</script>
