<?php
/**
 * Medical Aesthetics Service Card Component
 * Reusable service card for displaying medical treatments - matches index.php design
 */
?>
<div class="treatment-card group w-full bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 flex flex-col">
    <!-- Enhanced Image Container -->
    <div class="relative h-48 overflow-hidden">
        <?php if ($service['image']): ?>
            <?php
            // Handle both uploaded files and external URLs
            $imageSrc = $service['image'];
            if (!filter_var($imageSrc, FILTER_VALIDATE_URL)) {
                // If not a URL, treat as uploaded file and prepend uploads path
                $imageSrc = getBasePath() . '/uploads/' . ltrim($imageSrc, '/');
            }
            ?>
             <!-- High-Quality Image -->
             <img src="<?= htmlspecialchars($imageSrc) ?>"
                  alt="<?= htmlspecialchars($service['name']) ?>"
                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                  loading="lazy"
                  decoding="async"
                  width="320"
                  height="256"
                  style="backface-visibility: hidden; transform: translateZ(0);"
            />
        <?php else: ?>
            <!-- Fallback Design -->
            <div class="w-full h-full bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 flex items-center justify-center">
                <div class="text-center">
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto backdrop-blur-sm">
                        <svg class="w-8 h-8 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                    </div>
                    <div class="text-gray-700 font-medium"><?= htmlspecialchars($service['category'] ?? 'Medical Treatment') ?></div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Gradient Overlays -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/10 to-redolence-blue/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Hover Overlay Content -->
        <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
            <div class="text-center text-white">
                <div class="bg-white/20 backdrop-blur-md rounded-2xl p-4 border border-white/30">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                    <span class="text-sm font-semibold">View Details</span>
                </div>
            </div>
        </div>

        <!-- Professional Badge -->
        <div class="absolute top-4 left-4 bg-redolence-green/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-bold border border-white/20">
            <?php if (isset($service['subcategory_name']) && $service['subcategory_name']): ?>
                <?= htmlspecialchars($service['subcategory_name']) ?>
            <?php else: ?>
                Medical Grade
            <?php endif; ?>
        </div>

        <!-- Price Badge -->
        <?php if (shouldShowPricing()): ?>
            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm text-redolence-green px-3 py-1 rounded-full text-sm font-bold border border-redolence-green/20">
                <?= formatCurrency($service['price'], null, true) ?>
            </div>
        <?php else: ?>
            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm text-redolence-blue px-3 py-1 rounded-full text-sm font-bold border border-redolence-blue/20">
                Consultation
            </div>
        <?php endif; ?>
    </div>

    <!-- Treatment Content -->
    <div class="p-4 flex-1 flex flex-col">
        <!-- Treatment Header -->
        <div class="mb-3">
            <h3 class="text-lg font-bold text-gray-900 mb-2 group-hover:text-redolence-green transition-colors duration-300 line-clamp-1">
                <?= htmlspecialchars($service['name']) ?>
            </h3>
            <?php if ($service['description']): ?>
                <p class="text-gray-600 text-sm leading-relaxed line-clamp-2">
                    <?= htmlspecialchars($service['description']) ?>
                </p>
            <?php endif; ?>
        </div>

        <!-- Treatment Details -->
        <div class="flex items-center justify-between mb-4 text-sm">
            <div class="flex items-center text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-medium"><?= $service['duration'] ?> minutes</span>
            </div>
            <div class="flex items-center text-redolence-green">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <span class="font-semibold">Professional</span>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-auto space-y-2">
            <!-- Primary Action Button -->
            <?php if (shouldShowPricing()): ?>
                <button onclick="checkServiceVariations('<?= $service['id'] ?>', '<?= htmlspecialchars($service['name']) ?>', <?= $service['price'] ?>, <?= $service['duration'] ?>)"
                        class="w-full bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-redolence-green text-white py-2 px-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl text-sm">
                    Book Now
                </button>
            <?php else: ?>
                <button onclick="checkServiceVariations('<?= $service['id'] ?>', '<?= htmlspecialchars($service['name']) ?>', 0, <?= $service['duration'] ?>)"
                        class="w-full bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-redolence-green text-white py-2 px-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl text-sm">
                    Book Now
                </button>
            <?php endif; ?>

            <!-- Secondary Actions -->
            <div class="flex gap-1">
                <button onclick="viewServiceDetails('<?= $service['id'] ?>')"
                        class="flex-1 bg-white hover:bg-gray-50 text-redolence-blue border border-redolence-blue py-1.5 px-2 rounded-lg font-medium transition-all duration-300 hover:scale-105 text-sm">
                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Details
                </button>

                <!-- Wishlist Heart Icon -->
                <button onclick="toggleWishlist('service', '<?= $service['id'] ?>', this)"
                        class="wishlist-btn bg-white hover:bg-gray-50 text-gray-400 hover:text-red-500 border border-gray-200 hover:border-red-300 py-1.5 px-2 rounded-lg transition-all duration-300 hover:scale-105"
                        data-item-type="service"
                        data-item-id="<?= $service['id'] ?>"
                        title="Add to wishlist">
                    <i class="far fa-heart text-sm"></i>
                </button>
            </div>
        </div>
    </div>
</div>
