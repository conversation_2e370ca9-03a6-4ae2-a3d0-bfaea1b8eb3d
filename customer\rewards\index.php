<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Get customer data
$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);
$pointsData = getCustomerPointsData($customerId);
$loyaltyTier = getCustomerLoyaltyTierEnhanced($customerId);
$stats = getIndividualCustomerStats($customerId);

// Get available rewards
global $database;
$rewards = $database->fetchAll("
    SELECT * FROM rewards 
    WHERE is_active = 1 
    ORDER BY points_required ASC
");

$pageTitle = "Rewards & Points";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<style>
/* Professional Rewards Page Styles */
.rewards-container {
    min-height: 100vh;
    background: #fafbfc;
    padding: 1rem 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.rewards-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.rewards-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
}

.points-display {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #ffffff;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}



.stat-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
}

.stat-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-color: #d1d5db;
}

.tier-badge {
    background: #6366f1;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    box-shadow: 0 1px 3px rgba(99, 102, 241, 0.2);
}

.progress-bar {
    background: linear-gradient(90deg, #49a75c, #2563eb);
    height: 8px;
    border-radius: 4px;
    transition: width 0.8s ease;
    position: relative;
    overflow: hidden;
}



.reward-item {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
    position: relative;
}

.reward-item.available {
    border-color: #f59e0b;
    background: #fffbeb;
}

.reward-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: #49a75c;
    transform: scaleY(0);
    transition: transform 0.2s ease;
}

.reward-item:hover::before {
    transform: scaleY(1);
}

.reward-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-color: #d1d5db;
}

.btn-primary {
    background: #49a75c;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: #3d8b4e;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}



.btn-gold {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1e293b;
    box-shadow: 0 8px 20px rgba(251, 191, 36, 0.3);
}

.btn-disabled {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: #d1d5db;
    cursor: not-allowed;
}

.transaction-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    backdrop-filter: blur(5px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.earn-method-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.earn-method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.tier-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tier-card.current {
    border-color: rgba(251, 191, 36, 0.5);
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(255, 255, 255, 0.9));
    box-shadow: 0 10px 30px rgba(251, 191, 36, 0.2);
}

.tier-card.unlocked {
    border-color: rgba(34, 197, 94, 0.5);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(255, 255, 255, 0.9));
}

.tier-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 1rem;
    position: relative;
    padding-left: 0.75rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: #49a75c;
    border-radius: 1.5px;
}







.modal {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.empty-state {
    text-align: center;
    padding: 3rem 2rem;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rewards-container {
        padding: 0.75rem;
    }

    .rewards-card {
        border-radius: 12px;
        margin-bottom: 0.75rem;
    }

    .points-display {
        padding: 1rem;
        border-radius: 12px;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .reward-item, .earn-method-card, .tier-card {
        padding: 0.75rem;
        border-radius: 8px;
    }

    .btn-primary {
        padding: 0.625rem 1.25rem;
        font-size: 0.8rem;
    }
}
</style>



<div class="rewards-container">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Page Header -->
        <div class="rewards-card p-6 mb-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-4 lg:mb-0">
                    <div class="inline-flex items-center bg-gradient-to-r from-yellow-400/10 to-yellow-500/10 text-yellow-600 px-4 py-2 rounded-full text-xs font-semibold mb-3 backdrop-blur-sm border border-yellow-400/20">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        Rewards & Loyalty
                    </div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                        Your
                        <span class="bg-gradient-to-r from-yellow-500 to-yellow-600 bg-clip-text text-transparent">
                            Rewards
                        </span>
                    </h1>
                    <p class="text-base text-gray-600">
                        Earn points with every visit and unlock exclusive rewards
                    </p>
                </div>

                <div class="tier-badge text-sm px-4 py-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <?= $loyaltyTier['name'] ?> Member
                </div>
            </div>
        </div>

        <!-- Points Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
            <!-- Current Points - Featured -->
            <div class="lg:col-span-2">
                <div class="points-display">
                    <div class="relative z-10">
                        <h2 class="text-base font-semibold mb-2">Your Points Balance</h2>
                        <div class="text-3xl font-bold mb-2"><?= number_format($pointsData['currentPoints']) ?></div>
                        <p class="text-sm opacity-80">Worth TSH <?= number_format($pointsData['currentPoints'] * 10) ?></p>
                        <div class="mt-3">
                            <span class="bg-white/20 px-3 py-1 rounded-full text-xs font-semibold">
                                <?= $loyaltyTier['pointsMultiplier'] ?>x Multiplier Active
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Stats -->
            <div class="space-y-3">
                <div class="stat-card">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center mx-auto mb-2">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="text-xl font-bold text-gray-900 mb-1">+<?= number_format($pointsData['monthlyEarned']) ?></div>
                    <div class="text-xs text-gray-600">Earned This Month</div>
                </div>
            </div>

            <div class="space-y-3">
                <div class="stat-card">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center mx-auto mb-2">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="text-xl font-bold text-gray-900 mb-1">-<?= number_format($pointsData['monthlyRedeemed']) ?></div>
                    <div class="text-xs text-gray-600">Redeemed This Month</div>
                </div>
            </div>
        </div>

        <!-- Loyalty Progress -->
        <div class="rewards-card p-8 mb-8">
            <h3 class="section-title">Loyalty Progress</h3>
            
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                <div class="flex items-center gap-4 mb-4 lg:mb-0">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-2xl font-bold text-gray-900"><?= $loyaltyTier['name'] ?> Member</h4>
                        <p class="text-gray-600">Total spent: TSH <?= number_format($stats['totalSpent']) ?></p>
                        <p class="text-yellow-600 font-semibold"><?= $loyaltyTier['pointsMultiplier'] ?>x points multiplier</p>
                    </div>
                </div>
                
                <?php if ($loyaltyTier['nextTier']): ?>
                    <div class="text-center lg:text-right">
                        <p class="text-gray-600">Next tier: <span class="font-semibold"><?= $loyaltyTier['nextTier'] ?></span></p>
                        <p class="text-lg font-bold text-redolence-green">TSH <?= number_format($loyaltyTier['nextTierAmount']) ?> to go</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <?php if ($loyaltyTier['nextTier']): ?>
                <?php 
                $progress = (($stats['totalSpent'] - $loyaltyTier['minSpent']) / 
                            ($loyaltyTier['nextTierAmount'] + $stats['totalSpent'] - $loyaltyTier['minSpent'])) * 100;
                ?>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-6">
                    <div class="progress-bar rounded-full" style="width: <?= min(100, max(0, $progress)) ?>%"></div>
                </div>
            <?php else: ?>
                <div class="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full h-2 mb-6"></div>
                <p class="text-center text-yellow-600 font-semibold text-lg">🎉 You've reached the highest tier!</p>
            <?php endif; ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <h5 class="font-semibold text-gray-900 col-span-full mb-2">Your Benefits:</h5>
                <?php foreach ($loyaltyTier['benefits'] as $benefit): ?>
                    <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <span class="text-sm text-gray-700"><?= htmlspecialchars($benefit) ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            
            <!-- Available Rewards -->
            <div class="rewards-card p-8">
                <h3 class="section-title">Available Rewards</h3>
                
                <?php if (empty($rewards)): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"/>
                            </svg>
                        </div>
                        <h4 class="text-xl font-semibold text-gray-900 mb-2">No Rewards Available</h4>
                        <p class="text-gray-600">Check back soon for exciting rewards!</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($rewards as $reward): ?>
                            <div class="reward-item <?= $pointsData['currentPoints'] >= $reward['points_required'] ? 'available' : '' ?>">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center gap-3">
                                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-500 flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-900"><?= htmlspecialchars($reward['name']) ?></h4>
                                            <p class="text-sm text-gray-600"><?= htmlspecialchars($reward['description']) ?></p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-bold text-yellow-600"><?= number_format($reward['points_required']) ?> pts</p>
                                        <p class="text-sm text-gray-500">TSH <?= number_format($reward['value']) ?> value</p>
                                    </div>
                                </div>
                                
                                <?php if ($pointsData['currentPoints'] >= $reward['points_required']): ?>
                                    <button onclick="redeemReward('<?= $reward['id'] ?>', '<?= htmlspecialchars($reward['name']) ?>', <?= $reward['points_required'] ?>)" 
                                            class="btn-primary btn-gold w-full">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"/>
                                        </svg>
                                        Redeem Now
                                    </button>
                                <?php else: ?>
                                    <div class="btn-primary btn-disabled w-full">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                        </svg>
                                        Need <?= number_format($reward['points_required'] - $pointsData['currentPoints']) ?> more points
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Points History -->
            <div class="rewards-card p-8">
                <h3 class="section-title">Points History</h3>
                
                <?php if (empty($pointsData['transactions'])): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h4 class="text-xl font-semibold text-gray-900 mb-2">No Transactions Yet</h4>
                        <p class="text-gray-600">Your point history will appear here</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-3 max-h-96 overflow-y-auto">
                        <?php foreach ($pointsData['transactions'] as $transaction): ?>
                            <div class="transaction-item">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 rounded-full <?= $transaction['type'] === 'EARNED' ? 'bg-green-100' : 'bg-red-100' ?> flex items-center justify-center">
                                            <svg class="w-5 h-5 <?= $transaction['type'] === 'EARNED' ? 'text-green-600' : 'text-red-600' ?>" fill="currentColor" viewBox="0 0 20 20">
                                                <?php if ($transaction['type'] === 'EARNED'): ?>
                                                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"/>
                                                <?php else: ?>
                                                    <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                                <?php endif; ?>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900"><?= htmlspecialchars($transaction['description']) ?></p>
                                            <p class="text-sm text-gray-500"><?= date('M j, Y g:i A', strtotime($transaction['created_at'])) ?></p>
                                        </div>
                                    </div>
                                    <span class="font-bold text-lg <?= $transaction['type'] === 'EARNED' ? 'text-green-600' : 'text-red-600' ?>">
                                        <?= $transaction['type'] === 'EARNED' ? '+' : '' ?><?= number_format($transaction['points']) ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- How to Earn Points -->
        <div class="rewards-card p-8 mb-8">
            <h3 class="section-title">How to Earn Points</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="earn-method-card">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9a1 1 0 01-1 1H5a1 1 0 01-1-1V7z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Book Services</h4>
                    <p class="text-sm text-gray-600 mb-2">Earn 1 point for every TSH 1,000 spent</p>
                    <p class="text-xs text-yellow-600 font-semibold">Your tier: <?= $loyaltyTier['pointsMultiplier'] ?>x multiplier</p>
                </div>

                <div class="earn-method-card">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Refer Friends</h4>
                    <p class="text-sm text-gray-600">Get 100 points when a friend books their first service</p>
                </div>

                <div class="earn-method-card">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2L3 7v11a1 1 0 001 1h3v-8a1 1 0 011-1h4a1 1 0 011 1v8h3a1 1 0 001-1V7l-7-5z"/>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Birthday Bonus</h4>
                    <p class="text-sm text-gray-600">Receive 50 bonus points on your birthday month</p>
                </div>

                <div class="earn-method-card">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Leave Reviews</h4>
                    <p class="text-sm text-gray-600">Earn 25 points for each service review</p>
                </div>
            </div>
        </div>

        <!-- Customer Tiers Overview -->
        <div class="rewards-card p-8">
            <h3 class="section-title">Customer Tiers</h3>
            <p class="text-gray-600 mb-8">Spend more to unlock higher tiers and better rewards!</p>

            <?php
            // Get all tiers for display
            require_once __DIR__ . '/../../includes/rewards_functions.php';
            $allTiers = getCustomerTiers();
            ?>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <?php foreach ($allTiers as $tier): ?>
                    <?php
                    $isCurrentTier = $tier['name'] === $loyaltyTier['name'];
                    $isUnlocked = $stats['totalSpent'] >= $tier['minSpent'];
                    ?>
                    <div class="tier-card <?= $isCurrentTier ? 'current' : ($isUnlocked ? 'unlocked' : '') ?>">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </div>
                        <h4 class="font-bold text-gray-900 mb-2"><?= $tier['name'] ?></h4>
                        <p class="text-sm text-gray-600 mb-2">TSH <?= number_format($tier['minSpent']) ?>+</p>
                        <p class="text-sm text-yellow-600 font-semibold mb-4"><?= $tier['pointsMultiplier'] ?>x points</p>

                        <?php if ($isCurrentTier): ?>
                            <span class="inline-block px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full font-semibold">Current Tier</span>
                        <?php elseif ($isUnlocked): ?>
                            <span class="inline-block px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full font-semibold">Unlocked</span>
                        <?php else: ?>
                            <span class="inline-block px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full">Locked</span>
                        <?php endif; ?>

                        <div class="mt-4 space-y-2">
                            <?php foreach (array_slice($tier['benefits'], 0, 2) as $benefit): ?>
                                <p class="text-xs text-gray-600"><?= htmlspecialchars($benefit) ?></p>
                            <?php endforeach; ?>
                            <?php if (count($tier['benefits']) > 2): ?>
                                <p class="text-xs text-gray-500">+<?= count($tier['benefits']) - 2 ?> more benefits</p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- Redeem Confirmation Modal -->
<div id="redeemModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="modal max-w-md w-full p-8">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-gray-900">Confirm Redemption</h3>
            <button onclick="closeRedeemModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <div class="text-center mb-8">
            <div class="w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-500 flex items-center justify-center mx-auto mb-4">
                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"/>
                </svg>
            </div>
            <h4 class="text-xl font-bold text-gray-900 mb-2" id="rewardName">Reward Name</h4>
            <p class="text-gray-600 mb-4">Are you sure you want to redeem this reward?</p>
            <p class="text-yellow-600 font-bold text-lg"><span id="rewardPoints">0</span> points will be deducted</p>
        </div>

        <div class="flex items-center justify-end space-x-4">
            <button type="button" onclick="closeRedeemModal()" 
                    class="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-semibold transition-colors">
                Cancel
            </button>
            <button type="button" onclick="confirmRedemption()" 
                    class="btn-primary btn-gold">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                Confirm Redemption
            </button>
        </div>
    </div>
</div>

<script>
// Enhanced rewards page interactions
document.addEventListener('DOMContentLoaded', function() {
    // Smooth animations on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all reward cards
    document.querySelectorAll('.rewards-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

let selectedReward = null;

function redeemReward(rewardId, rewardName, pointsRequired) {
    selectedReward = {
        id: rewardId,
        name: rewardName,
        points: pointsRequired
    };
    
    document.getElementById('rewardName').textContent = rewardName;
    document.getElementById('rewardPoints').textContent = pointsRequired.toLocaleString();
    document.getElementById('redeemModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeRedeemModal() {
    document.getElementById('redeemModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    selectedReward = null;
}

async function confirmRedemption() {
    if (!selectedReward) return;
    
    try {
        const response = await fetch('<?= getBasePath() ?>/api/customer/redeem-reward.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reward_id: selectedReward.id
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showToast('Reward redeemed successfully!', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast('Error: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('Error redeeming reward:', error);
        showToast('An error occurred while redeeming the reward', 'error');
    }
    
    closeRedeemModal();
}

// Close modal when clicking outside
document.getElementById('redeemModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRedeemModal();
    }
});

// Escape key to close modal
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('redeemModal').classList.contains('hidden')) {
        closeRedeemModal();
    }
});

// Modern toast notification system
function showToast(message, type = 'success') {
    const existingToast = document.getElementById('toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    const toast = document.createElement('div');
    toast.id = 'toast';
    toast.className = `fixed top-4 right-4 z-50 flex items-center p-4 rounded-xl shadow-lg transform translate-x-full transition-all duration-300 ${
        type === 'success' 
            ? 'bg-green-500 text-white' 
            : 'bg-red-500 text-white'
    }`;
    
    toast.innerHTML = `
        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
            ${type === 'success' 
                ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>'
                : '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>'
            }
        </svg>
        <span class="font-medium">${message}</span>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(full)';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>