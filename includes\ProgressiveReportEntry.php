<?php
/**
 * Progressive Report Entry Class
 * Handles individual entries within progressive treatment reports
 */
class ProgressiveReportEntry {
    private $db;

    public function __construct() {
        global $database;
        $this->db = $database;
    }
    
    /**
     * Generate UUID for new records
     */
    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    /**
     * Create a new progressive report entry
     */
    public function create($reportId, $data) {
        try {
            $id = $this->generateUUID();
            $createdBy = $data['created_by'] ?? $_SESSION['user_id'] ?? null;
            
            if (!$createdBy) {
                throw new Exception('Created by user ID is required');
            }
            
            // Validate required fields
            $requiredFields = ['entry_date', 'treatment', 'description'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    throw new Exception("Field '$field' is required");
                }
            }
            
            $images = isset($data['images']) && is_array($data['images']) ? json_encode($data['images']) : null;

            $result = $this->db->execute("
                INSERT INTO progressive_report_entries
                (id, report_id, appointment_id, entry_date, treatment, description, notes, images, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ", [
                $id,
                $reportId,
                $data['appointment_id'] ?? null,
                $data['entry_date'],
                $data['treatment'],
                $data['description'],
                $data['notes'] ?? null,
                $images,
                $createdBy
            ]);

            if ($result) {
                return $this->getById($id);
            }

            return false;
        } catch (Exception $e) {
            error_log("Progressive Report Entry creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get entry by ID
     */
    public function getById($id) {
        try {
            $entry = $this->db->fetch("
                SELECT pre.*, pr.client_id, pr.title as report_title, u.name as client_name,
                       creator.name as created_by_name, creator.role as created_by_role,
                       b.date as appointment_date, b.start_time as appointment_start_time, b.status as appointment_status,
                       s.name as service_name, staff.name as staff_name
                FROM progressive_report_entries pre
                LEFT JOIN progressive_reports pr ON pre.report_id = pr.id
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pre.created_by = creator.id
                LEFT JOIN bookings b ON pre.appointment_id = b.id
                LEFT JOIN services s ON b.service_id = s.id
                LEFT JOIN users staff ON b.staff_id = staff.id
                WHERE pre.id = ?
            ", [$id]);

            if ($entry && $entry['images']) {
                $entry['images'] = json_decode($entry['images'], true);
            }

            return $entry;
        } catch (Exception $e) {
            error_log("Error fetching progressive report entry: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get entries by report ID
     */
    public function getByReportId($reportId, $orderBy = 'entry_date DESC') {
        try {
            $entries = $this->db->fetchAll("
                SELECT pre.*, pr.client_id, pr.title as report_title, u.name as client_name,
                       creator.name as created_by_name, creator.role as created_by_role,
                       b.date as appointment_date, b.start_time as appointment_start_time, b.status as appointment_status,
                       s.name as service_name, staff.name as staff_name
                FROM progressive_report_entries pre
                LEFT JOIN progressive_reports pr ON pre.report_id = pr.id
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pre.created_by = creator.id
                LEFT JOIN bookings b ON pre.appointment_id = b.id
                LEFT JOIN services s ON b.service_id = s.id
                LEFT JOIN users staff ON b.staff_id = staff.id
                WHERE pre.report_id = ?
                ORDER BY $orderBy
            ", [$reportId]);

            // Decode images JSON for each entry
            if ($entries) {
                foreach ($entries as &$entry) {
                    if ($entry['images']) {
                        $entry['images'] = json_decode($entry['images'], true);
                    }
                }
            }

            return $entries;
        } catch (Exception $e) {
            error_log("Error fetching report entries: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get entries by appointment ID
     */
    public function getByAppointmentId($appointmentId) {
        try {
            $entries = $this->db->fetchAll("
                SELECT pre.*, pr.client_id, pr.title as report_title, u.name as client_name,
                       creator.name as created_by_name, creator.role as created_by_role,
                       b.date as appointment_date, b.start_time as appointment_start_time, b.status as appointment_status,
                       s.name as service_name, staff.name as staff_name
                FROM progressive_report_entries pre
                LEFT JOIN progressive_reports pr ON pre.report_id = pr.id
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pre.created_by = creator.id
                LEFT JOIN bookings b ON pre.appointment_id = b.id
                LEFT JOIN services s ON b.service_id = s.id
                LEFT JOIN users staff ON b.staff_id = staff.id
                WHERE pre.appointment_id = ?
                ORDER BY pre.entry_date DESC
            ", [$appointmentId]);

            // Decode images JSON for each entry
            if ($entries) {
                foreach ($entries as &$entry) {
                    if ($entry['images']) {
                        $entry['images'] = json_decode($entry['images'], true);
                    }
                }
            }

            return $entries;
        } catch (Exception $e) {
            error_log("Error fetching entries by appointment: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update progressive report entry
     */
    public function update($id, $data) {
        try {
            $allowedFields = ['entry_date', 'treatment', 'description', 'notes', 'images'];
            $updateFields = [];
            $params = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    if ($field === 'images' && is_array($data[$field])) {
                        $updateFields[] = "$field = ?";
                        $params[] = json_encode($data[$field]);
                    } else {
                        $updateFields[] = "$field = ?";
                        $params[] = $data[$field];
                    }
                }
            }
            
            if (empty($updateFields)) {
                return false;
            }
            
            $params[] = $id;
            
            return $this->db->execute("
                UPDATE progressive_report_entries
                SET " . implode(', ', $updateFields) . "
                WHERE id = ?
            ", $params);
        } catch (Exception $e) {
            error_log("Error updating progressive report entry: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete progressive report entry
     */
    public function delete($id) {
        try {
            return $this->db->execute("DELETE FROM progressive_report_entries WHERE id = ?", [$id]);
        } catch (Exception $e) {
            error_log("Error deleting progressive report entry: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if user can edit entry
     */
    public function canEdit($entryId, $userId, $userRole) {
        if ($userRole === 'ADMIN') {
            return true;
        }
        
        try {
            $entry = $this->db->fetch("SELECT created_by FROM progressive_report_entries WHERE id = ?", [$entryId]);

            return $entry && $entry['created_by'] === $userId;
        } catch (Exception $e) {
            error_log("Error checking entry edit permission: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get entries created by specific user
     */
    public function getByCreatedBy($userId, $limit = 50) {
        try {
            $entries = $this->db->fetchAll("
                SELECT pre.*, pr.client_id, pr.title as report_title, u.name as client_name,
                       creator.name as created_by_name, creator.role as created_by_role,
                       b.date as appointment_date, b.start_time as appointment_start_time, b.status as appointment_status,
                       s.name as service_name, staff.name as staff_name
                FROM progressive_report_entries pre
                LEFT JOIN progressive_reports pr ON pre.report_id = pr.id
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pre.created_by = creator.id
                LEFT JOIN bookings b ON pre.appointment_id = b.id
                LEFT JOIN services s ON b.service_id = s.id
                LEFT JOIN users staff ON b.staff_id = staff.id
                WHERE pre.created_by = ?
                ORDER BY pre.created_at DESC
                LIMIT ?
            ", [$userId, $limit]);

            // Decode images JSON for each entry
            if ($entries) {
                foreach ($entries as &$entry) {
                    if ($entry['images']) {
                        $entry['images'] = json_decode($entry['images'], true);
                    }
                }
            }

            return $entries;
        } catch (Exception $e) {
            error_log("Error fetching entries by creator: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create entry from appointment
     */
    public function createFromAppointment($appointmentId, $treatment, $description, $notes = null) {
        try {
            // Get appointment details
            $appointment = $this->db->fetch("
                SELECT b.*, u.id as client_id, u.name as client_name
                FROM bookings b
                JOIN users u ON b.user_id = u.id
                WHERE b.id = ?
            ", [$appointmentId]);
            
            if (!$appointment) {
                throw new Exception('Appointment not found');
            }
            
            // Check if progressive report exists for this client
            require_once __DIR__ . '/ProgressiveReport.php';
            $progressiveReport = new ProgressiveReport();
            $report = $progressiveReport->getByClientId($appointment['client_id']);
            
            // Create report if it doesn't exist
            if (!$report) {
                $report = $progressiveReport->create(
                    $appointment['client_id'],
                    'Progressive Treatment Report - ' . $appointment['client_name'],
                    'Comprehensive treatment progress tracking'
                );
                
                if (!$report) {
                    throw new Exception('Failed to create progressive report');
                }
            }
            
            // Create entry
            $entryData = [
                'appointment_id' => $appointmentId,
                'entry_date' => $appointment['date'],
                'treatment' => $treatment,
                'description' => $description,
                'notes' => $notes
            ];
            
            return $this->create($report['id'], $entryData);
        } catch (Exception $e) {
            error_log("Error creating entry from appointment: " . $e->getMessage());
            return false;
        }
    }
}
