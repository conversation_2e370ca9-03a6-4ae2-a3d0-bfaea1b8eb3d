<?php
/**
 * Booking Policy Page - Medical Aesthetics Redesign
 * Redolence Medi Aesthetics - Advanced Medical Beauty Center
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Booking Policy - Redolence Medi Aesthetics";
$pageDescription = "Learn about our booking policies, appointment scheduling, and medical consultation procedures at Redolence Medi Aesthetics.";

include __DIR__ . '/includes/header.php';
?>

<!-- Revolutionary Medical Aesthetics CSS -->
<style>
/* Advanced Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
}

/* Revolutionary Animation Framework */
@keyframes morphingPolicyBg {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes floatingPolicy {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-15px) rotate(3deg); }
    66% { transform: translateY(-8px) rotate(-2deg); }
}

@keyframes slideInPolicy {
    0% { opacity: 0; transform: translateX(-30px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes scaleInPolicy {
    0% { opacity: 0; transform: scale(0.95); }
    100% { opacity: 1; transform: scale(1); }
}

/* Medical Policy Card System */
.policy-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    padding: 2.5rem;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.policy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.policy-card:hover::before {
    left: 100%;
}

.policy-card:hover {
    transform: translateY(-8px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

.policy-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-soft);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.policy-icon:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 30px var(--shadow-primary);
}

.policy-section {
    margin-bottom: 4rem;
    padding: 3rem 0;
    border-bottom: 1px solid rgba(73, 167, 92, 0.1);
}

.policy-section:last-child {
    border-bottom: none;
}

.policy-highlight {
    background: var(--gradient-soft);
    border-left: 4px solid var(--primary-green);
    padding: 2rem;
    border-radius: 0 16px 16px 0;
    margin: 2rem 0;
}

.policy-list {
    list-style: none;
    padding: 0;
}

.policy-list li {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 1rem;
    color: #374151;
    line-height: 1.7;
}

.policy-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.7rem;
    width: 8px;
    height: 8px;
    background: var(--primary-green);
    border-radius: 50%;
}

.policy-table {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(73, 167, 92, 0.1);
}

.policy-table th {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem;
    font-weight: 700;
    text-align: left;
}

.policy-table td {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(73, 167, 92, 0.1);
}

.policy-table tr:hover {
    background: rgba(73, 167, 92, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .policy-card {
        padding: 2rem;
        margin: 0;
        border-radius: 16px;
    }
    
    .policy-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1.5rem;
    }
    
    .policy-section {
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .policy-highlight {
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
    
    .policy-table th,
    .policy-table td {
        padding: 1rem;
        font-size: 0.9rem;
    }
}

/* Medical Professional Styling */
.medical-notice {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border: 2px solid rgba(239, 68, 68, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
}

.medical-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border: 2px solid rgba(34, 197, 94, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
}

.medical-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
}
</style>

<!-- Revolutionary Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Dynamic Medical Background -->
    <div class="absolute inset-0 bg-gradient-to-br from-redolence-green via-redolence-blue to-redolence-green bg-[length:400%_400%] animate-[morphingPolicyBg_10s_ease_infinite]"></div>
    
    <!-- Medical Pattern Overlay -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 75% 75%, var(--primary-blue) 2px, transparent 2px); background-size: 60px 60px;"></div>
    </div>
    
    <!-- Floating Medical Elements -->
    <div class="absolute inset-0 pointer-events-none">
        <div class="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full backdrop-blur-sm animate-[floatingPolicy_8s_ease-in-out_infinite]"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-white/15 rounded-full backdrop-blur-sm animate-[floatingPolicy_8s_ease-in-out_infinite] [animation-delay:2s]"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-white/8 rounded-full backdrop-blur-sm animate-[floatingPolicy_8s_ease-in-out_infinite] [animation-delay:4s]"></div>
        <div class="absolute bottom-20 right-10 w-28 h-28 bg-white/12 rounded-full backdrop-blur-sm animate-[floatingPolicy_8s_ease-in-out_infinite] [animation-delay:6s]"></div>
    </div>
    
    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white px-6 max-w-6xl mx-auto">
        <div class="animate-[scaleInPolicy_0.8s_ease_0.2s_both]">
            <div class="inline-flex items-center bg-white/20 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-white/30">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                </svg>
                <span class="font-semibold text-lg">MEDICAL BOOKING POLICIES</span>
            </div>
        </div>
        
        <h1 class="text-6xl md:text-8xl font-black mb-8 leading-none animate-[slideInPolicy_1s_ease_0.4s_both]">
            Booking
            <span class="block text-5xl md:text-7xl font-light opacity-90">Policy</span>
        </h1>
        
        <p class="text-xl md:text-3xl font-light mb-12 leading-relaxed animate-[slideInPolicy_1s_ease_0.6s_both]">
            Professional <strong>medical aesthetics</strong> scheduling guidelines
            <span class="block mt-4 text-lg md:text-xl opacity-80">Ensuring optimal care and treatment outcomes</span>
        </p>
        
        <!-- Quick Policy Links -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto animate-[scaleInPolicy_0.8s_ease_0.8s_both]">
            <a href="#scheduling" class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl hover:bg-white/30 transition-all">
                <svg class="w-8 h-8 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <h3 class="font-bold text-lg">Scheduling</h3>
                <p class="text-sm opacity-80">Appointment booking process</p>
            </a>
            <a href="#preparation" class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl hover:bg-white/30 transition-all">
                <svg class="w-8 h-8 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="font-bold text-lg">Preparation</h3>
                <p class="text-sm opacity-80">Pre-treatment requirements</p>
            </a>
            <a href="#payment" class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl hover:bg-white/30 transition-all">
                <svg class="w-8 h-8 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                <h3 class="font-bold text-lg">Payment</h3>
                <p class="text-sm opacity-80">Payment policies & options</p>
            </a>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Revolutionary Policy Content Section -->
<section class="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 30% 30%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 70% 70%, var(--primary-blue) 2px, transparent 2px); background-size: 80px 80px;"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 relative">
        
        <!-- Appointment Scheduling Section -->
        <div id="scheduling" class="policy-section">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="animate-[slideInPolicy_0.8s_ease_0.2s_both]">
                    <div class="policy-icon">
                        <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                        Appointment Scheduling
                    </h2>
                    <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                        Our medical scheduling system ensures optimal treatment planning and personalized care for every patient.
                    </p>
                    
                    <div class="medical-info">
                        <div class="flex items-start">
                            <svg class="w-6 h-6 text-blue-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h4 class="font-bold text-blue-900 mb-2">Medical Consultation Required</h4>
                                <p class="text-blue-800">All new patients must complete an initial medical consultation before any aesthetic treatment can be scheduled.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="policy-card animate-[scaleInPolicy_0.8s_ease_0.4s_both]">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Scheduling Guidelines</h3>
                    
                    <ul class="policy-list">
                        <li><strong>Initial Consultations:</strong> Book 1-2 weeks in advance for comprehensive medical assessment</li>
                        <li><strong>Treatment Appointments:</strong> Schedule 2-4 weeks ahead due to high demand for our specialists</li>
                        <li><strong>Follow-up Visits:</strong> Typically scheduled 2-4 weeks after initial treatment</li>
                        <li><strong>Emergency Consultations:</strong> Available within 24-48 hours for urgent medical concerns</li>
                        <li><strong>Package Treatments:</strong> Multiple sessions pre-scheduled for optimal treatment outcomes</li>
                    </ul>
                    
                    <div class="policy-highlight mt-6">
                        <h4 class="font-bold text-redolence-green mb-2">Booking Methods</h4>
                        <p class="text-gray-700">Schedule online through our patient portal, call our medical center directly, or visit our clinic for in-person booking assistance.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Appointment Preparation Section -->
        <div id="preparation" class="policy-section">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="policy-card animate-[scaleInPolicy_0.8s_ease_0.2s_both]">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Pre-Treatment Requirements</h3>
                    
                    <div class="space-y-6">
                        <div>
                            <h4 class="font-bold text-redolence-green mb-3">Medical History & Assessment</h4>
                            <ul class="policy-list">
                                <li>Complete medical history form 48 hours before appointment</li>
                                <li>List all current medications and supplements</li>
                                <li>Disclose any allergies or medical conditions</li>
                                <li>Provide previous aesthetic treatment records</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h4 class="font-bold text-redolence-blue mb-3">Pre-Treatment Instructions</h4>
                            <ul class="policy-list">
                                <li>Avoid blood-thinning medications 1-2 weeks prior</li>
                                <li>No alcohol consumption 24 hours before treatment</li>
                                <li>Arrive with clean, makeup-free skin</li>
                                <li>Bring valid ID and insurance information</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="medical-notice mt-6">
                        <div class="flex items-start">
                            <svg class="w-6 h-6 text-red-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                            <div>
                                <h4 class="font-bold text-red-900 mb-2">Important Medical Notice</h4>
                                <p class="text-red-800">Failure to follow pre-treatment instructions may result in appointment postponement for your safety.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="animate-[slideInPolicy_0.8s_ease_0.4s_both]">
                    <div class="policy-icon">
                        <svg class="w-10 h-10 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                        Treatment Preparation
                    </h2>
                    <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                        Proper preparation ensures optimal treatment results and minimizes potential complications.
                    </p>
                    
                    <div class="medical-success">
                        <div class="flex items-start">
                            <svg class="w-6 h-6 text-green-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h4 class="font-bold text-green-900 mb-2">Preparation Support</h4>
                                <p class="text-green-800">Our medical team provides detailed preparation instructions and is available to answer any questions before your treatment.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Policy Section -->
        <div id="payment" class="policy-section">
            <div class="text-center mb-16">
                <div class="policy-icon mx-auto">
                    <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Payment Policies
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Flexible payment options designed to make advanced medical aesthetics accessible and convenient.
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div class="policy-card">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Payment Methods</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                            <svg class="w-6 h-6 text-redolence-green mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <div>
                                <h4 class="font-bold text-gray-900">Cash & Credit Cards</h4>
                                <p class="text-gray-600 text-sm">All major credit cards accepted</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                            <svg class="w-6 h-6 text-redolence-blue mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            <div>
                                <h4 class="font-bold text-gray-900">Medical Financing</h4>
                                <p class="text-gray-600 text-sm">0% interest plans available</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                            <svg class="w-6 h-6 text-purple-600 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <div>
                                <h4 class="font-bold text-gray-900">HSA/FSA Accounts</h4>
                                <p class="text-gray-600 text-sm">Health savings accounts accepted</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="policy-card">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Payment Schedule</h3>
                    
                    <div class="policy-table">
                        <table class="w-full">
                            <thead>
                                <tr>
                                    <th>Service Type</th>
                                    <th>Payment Timing</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="font-semibold">Consultations</td>
                                    <td>Payment due at time of service</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Single Treatments</td>
                                    <td>Full payment before treatment</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Treatment Packages</td>
                                    <td>50% deposit, balance before final session</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Surgical Procedures</td>
                                    <td>Full payment 1 week prior to procedure</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="policy-highlight mt-6">
                        <h4 class="font-bold text-redolence-green mb-2">Package Discounts</h4>
                        <p class="text-gray-700">Save up to 20% when booking multiple treatment sessions in advance. Ask about our loyalty program benefits.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cancellation & Rescheduling Section -->
        <div class="policy-section">
            <div class="text-center mb-16">
                <div class="policy-icon mx-auto">
                    <svg class="w-10 h-10 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Cancellation & Rescheduling
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    We understand that schedules change. Our flexible policies accommodate your needs while ensuring optimal clinic operations.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="policy-card text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">48+ Hours Notice</h3>
                    <p class="text-gray-600 mb-4">Full refund or free rescheduling with no penalties</p>
                    <div class="text-green-600 font-bold">No Fees</div>
                </div>
                
                <div class="policy-card text-center">
                    <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">24-48 Hours Notice</h3>
                    <p class="text-gray-600 mb-4">Rescheduling available with partial fee</p>
                    <div class="text-yellow-600 font-bold">50% Fee</div>
                </div>
                
                <div class="policy-card text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Less Than 24 Hours</h3>
                    <p class="text-gray-600 mb-4">Full treatment fee charged for no-shows</p>
                    <div class="text-red-600 font-bold">100% Fee</div>
                </div>
            </div>
            
            <div class="medical-info mt-12">
                <div class="flex items-start">
                    <svg class="w-6 h-6 text-blue-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h4 class="font-bold text-blue-900 mb-2">Emergency Exceptions</h4>
                        <p class="text-blue-800">Medical emergencies and unforeseen circumstances are handled with compassion and discretion. Please contact our medical team to discuss your situation.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="text-center">
            <div class="policy-card max-w-4xl mx-auto">
                <div class="policy-icon mx-auto">
                    <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Questions About Our Policies?
                </h2>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Our patient care coordinators are here to help you understand our policies and schedule your treatments.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="contact.php" class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-redolence-blue hover:from-redolence-blue hover:to-redolence-green text-white px-8 py-4 rounded-xl font-bold text-lg transition-all hover:scale-105 shadow-lg">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Schedule Consultation
                    </a>
                    <a href="tel:+255781985757" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-green px-8 py-4 rounded-xl font-bold text-lg transition-all border-2 border-redolence-green/20 hover:border-redolence-green/40">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Call: +255 781 985 757
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary JavaScript -->
<script>
// Enhanced page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth entrance animations to policy cards
    const policyCards = document.querySelectorAll('.policy-card');
    policyCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.23, 1, 0.32, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Add highlight effect
                target.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    target.style.transform = 'scale(1)';
                }, 300);
            }
        });
    });

    // Add hover enhancements to policy icons
    const policyIcons = document.querySelectorAll('.policy-icon');
    policyIcons.forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Add table row hover effects
    const tableRows = document.querySelectorAll('.policy-table tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>