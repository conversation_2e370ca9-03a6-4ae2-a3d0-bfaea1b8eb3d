/**
 * Progressive Reports JavaScript
 * Enhanced functionality for progressive report management
 */

class ProgressiveReports {
    constructor() {
        this.basePath = window.basePath || '';
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeComponents();
    }

    bindEvents() {
        // Auto-save functionality for forms
        this.initAutoSave();
        
        // Enhanced search functionality
        this.initSearchEnhancements();
        
        // Real-time validation
        this.initFormValidation();
        
        // Image upload handling
        this.initImageUpload();
    }

    initializeComponents() {
        // Initialize any third-party components
        this.initDatePickers();
        this.initRichTextEditors();
    }

    // Auto-save functionality
    initAutoSave() {
        const forms = document.querySelectorAll('[data-autosave]');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', this.debounce(() => {
                    this.autoSave(form);
                }, 2000));
            });
        });
    }

    autoSave(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // Save to localStorage as backup
        const formId = form.id || 'progressive-report-form';
        localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
        
        // Show auto-save indicator
        this.showAutoSaveIndicator();
    }

    showAutoSaveIndicator() {
        const indicator = document.getElementById('autosave-indicator');
        if (indicator) {
            indicator.textContent = 'Auto-saved';
            indicator.classList.remove('hidden');
            setTimeout(() => {
                indicator.classList.add('hidden');
            }, 2000);
        }
    }

    // Enhanced search functionality
    initSearchEnhancements() {
        const searchInputs = document.querySelectorAll('[data-search-enhanced]');
        searchInputs.forEach(input => {
            input.addEventListener('input', this.debounce((e) => {
                this.performSearch(e.target.value);
            }, 300));
        });
    }

    performSearch(query) {
        if (query.length < 2) return;
        
        fetch(`${this.basePath}/api/admin/progressive-reports.php?search=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateSearchResults(data.data.reports);
                }
            })
            .catch(error => {
                console.error('Search error:', error);
            });
    }

    updateSearchResults(reports) {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) return;

        if (reports.length === 0) {
            resultsContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No reports found</p>';
            return;
        }

        const html = reports.map(report => `
            <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer" 
                 onclick="window.location.href='${this.basePath}/admin/progressive-reports/view.php?id=${report.id}'">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-semibold text-redolence-navy">${this.escapeHtml(report.title)}</h3>
                        <p class="text-sm text-gray-600">${this.escapeHtml(report.client_name)}</p>
                    </div>
                    <span class="text-xs text-gray-500">${report.total_entries} entries</span>
                </div>
            </div>
        `).join('');

        resultsContainer.innerHTML = html;
    }

    // Form validation
    initFormValidation() {
        const forms = document.querySelectorAll('[data-validate]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });

            // Real-time validation
            const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateField(field) {
        const value = field.value.trim();
        const isValid = value !== '';
        
        this.toggleFieldError(field, !isValid);
        return isValid;
    }

    toggleFieldError(field, hasError) {
        const errorClass = 'border-red-500';
        const successClass = 'border-green-500';
        
        field.classList.remove(errorClass, successClass);
        
        if (hasError) {
            field.classList.add(errorClass);
        } else if (field.value.trim() !== '') {
            field.classList.add(successClass);
        }
    }

    // Image upload handling
    initImageUpload() {
        const uploadAreas = document.querySelectorAll('[data-image-upload]');
        uploadAreas.forEach(area => {
            this.setupImageUpload(area);
        });
    }

    setupImageUpload(uploadArea) {
        const input = uploadArea.querySelector('input[type="file"]');
        const preview = uploadArea.querySelector('[data-preview]');
        
        if (!input) return;

        input.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleImageFiles(files, preview);
        });

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('border-blue-500', 'bg-blue-50');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
            
            const files = Array.from(e.dataTransfer.files);
            this.handleImageFiles(files, preview);
        });
    }

    handleImageFiles(files, preview) {
        if (!preview) return;

        files.forEach(file => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'w-20 h-20 object-cover rounded-lg';
                    preview.appendChild(img);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Date picker initialization
    initDatePickers() {
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            // Set max date to today for entry dates
            if (input.name === 'entry_date') {
                input.max = new Date().toISOString().split('T')[0];
            }
        });
    }

    // Rich text editor initialization
    initRichTextEditors() {
        const textareas = document.querySelectorAll('[data-rich-editor]');
        textareas.forEach(textarea => {
            // You can integrate a rich text editor like TinyMCE or Quill here
            this.enhanceTextarea(textarea);
        });
    }

    enhanceTextarea(textarea) {
        // Add character counter
        const counter = document.createElement('div');
        counter.className = 'text-sm text-gray-500 mt-1';
        counter.textContent = `${textarea.value.length} characters`;
        textarea.parentNode.appendChild(counter);

        textarea.addEventListener('input', () => {
            counter.textContent = `${textarea.value.length} characters`;
        });

        // Auto-resize
        textarea.addEventListener('input', () => {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        });
    }

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, (m) => map[m]);
    }

    // API helpers
    async apiCall(endpoint, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const response = await fetch(`${this.basePath}${endpoint}`, {
            ...defaultOptions,
            ...options,
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    // Notification system
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${this.getNotificationClasses(type)}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    getNotificationClasses(type) {
        const classes = {
            success: 'bg-green-500 text-white',
            error: 'bg-red-500 text-white',
            warning: 'bg-yellow-500 text-black',
            info: 'bg-blue-500 text-white'
        };
        return classes[type] || classes.info;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.progressiveReports = new ProgressiveReports();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProgressiveReports;
}
