<?php
/**
 * Cancellation Policy Page - Medical Aesthetics Redesign
 * Redolence Medi Aesthetics - Advanced Medical Beauty Center
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Cancellation Policy - Redolence Medi Aesthetics";
$pageDescription = "Understand our cancellation and rescheduling policies for medical aesthetic treatments at Redolence Medi Aesthetics.";

include __DIR__ . '/includes/header.php';
?>

<!-- Revolutionary Medical Aesthetics CSS -->
<style>
/* Advanced Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
}

/* Revolutionary Animation Framework */
@keyframes morphingCancelBg {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes floatingCancel {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-12px) rotate(2deg); }
    66% { transform: translateY(-6px) rotate(-1deg); }
}

@keyframes slideInCancel {
    0% { opacity: 0; transform: translateX(-40px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes scaleInCancel {
    0% { opacity: 0; transform: scale(0.9); }
    100% { opacity: 1; transform: scale(1); }
}

@keyframes pulseWarning {
    0%, 100% { box-shadow: 0 0 20px rgba(239, 68, 68, 0.3); }
    50% { box-shadow: 0 0 30px rgba(239, 68, 68, 0.5); }
}

/* Medical Cancellation Card System */
.cancellation-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    padding: 2.5rem;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.cancellation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.cancellation-card:hover::before {
    left: 100%;
}

.cancellation-card:hover {
    transform: translateY(-8px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

.timeline-card {
    background: white;
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    transition: all 0.3s ease;
}

.timeline-card::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    border-radius: 2px;
}

.timeline-card.success::before {
    background: linear-gradient(to bottom, #10b981, #059669);
}

.timeline-card.warning::before {
    background: linear-gradient(to bottom, #f59e0b, #d97706);
}

.timeline-card.danger::before {
    background: linear-gradient(to bottom, #ef4444, #dc2626);
}

.timeline-card:hover {
    transform: translateX(10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.policy-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-soft);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.policy-icon:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 30px var(--shadow-primary);
}

.fee-structure {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
}

.fee-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(73, 167, 92, 0.1);
    transition: all 0.3s ease;
}

.fee-item:hover {
    background: rgba(73, 167, 92, 0.05);
    transform: translateX(5px);
}

.fee-item:last-child {
    border-bottom: none;
}

/* Medical Alert Boxes */
.medical-alert {
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.medical-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
}

.medical-alert.success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border: 2px solid rgba(34, 197, 94, 0.2);
}

.medical-alert.success::before {
    background: linear-gradient(to bottom, #10b981, #059669);
}

.medical-alert.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    border: 2px solid rgba(245, 158, 11, 0.2);
}

.medical-alert.warning::before {
    background: linear-gradient(to bottom, #f59e0b, #d97706);
}

.medical-alert.danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border: 2px solid rgba(239, 68, 68, 0.2);
    animation: pulseWarning 3s ease-in-out infinite;
}

.medical-alert.danger::before {
    background: linear-gradient(to bottom, #ef4444, #dc2626);
}

.medical-alert.info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    border: 2px solid rgba(59, 130, 246, 0.2);
}

.medical-alert.info::before {
    background: linear-gradient(to bottom, #3b82f6, #2563eb);
}

/* Responsive Design */
@media (max-width: 768px) {
    .cancellation-card {
        padding: 2rem;
        margin: 0;
        border-radius: 16px;
    }
    
    .timeline-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .policy-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1.5rem;
    }
    
    .fee-structure {
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
    
    .medical-alert {
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
}

/* Interactive Elements */
.interactive-timeline {
    position: relative;
    padding-left: 2rem;
}

.interactive-timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-green), var(--primary-blue));
}

.timeline-point {
    position: absolute;
    left: -6px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: white;
    border: 3px solid var(--primary-green);
    transition: all 0.3s ease;
}

.timeline-point:hover {
    transform: scale(1.3);
    box-shadow: 0 0 20px rgba(73, 167, 92, 0.5);
}
</style>

<!-- Revolutionary Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Dynamic Medical Background -->
    <div class="absolute inset-0 bg-gradient-to-br from-redolence-green via-redolence-blue to-redolence-green bg-[length:400%_400%] animate-[morphingCancelBg_12s_ease_infinite]"></div>
    
    <!-- Medical Pattern Overlay -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 75% 75%, var(--primary-blue) 2px, transparent 2px); background-size: 60px 60px;"></div>
    </div>
    
    <!-- Floating Medical Elements -->
    <div class="absolute inset-0 pointer-events-none">
        <div class="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full backdrop-blur-sm animate-[floatingCancel_10s_ease-in-out_infinite]"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-white/15 rounded-full backdrop-blur-sm animate-[floatingCancel_10s_ease-in-out_infinite] [animation-delay:3s]"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-white/8 rounded-full backdrop-blur-sm animate-[floatingCancel_10s_ease-in-out_infinite] [animation-delay:6s]"></div>
        <div class="absolute bottom-20 right-10 w-28 h-28 bg-white/12 rounded-full backdrop-blur-sm animate-[floatingCancel_10s_ease-in-out_infinite] [animation-delay:9s]"></div>
    </div>
    
    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white px-6 max-w-6xl mx-auto">
        <div class="animate-[scaleInCancel_0.8s_ease_0.2s_both]">
            <div class="inline-flex items-center bg-white/20 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-white/30">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-semibold text-lg">MEDICAL CANCELLATION POLICIES</span>
            </div>
        </div>
        
        <h1 class="text-6xl md:text-8xl font-black mb-8 leading-none animate-[slideInCancel_1s_ease_0.4s_both]">
            Cancellation
            <span class="block text-5xl md:text-7xl font-light opacity-90">Policy</span>
        </h1>
        
        <p class="text-xl md:text-3xl font-light mb-12 leading-relaxed animate-[slideInCancel_1s_ease_0.6s_both]">
            Flexible <strong>medical scheduling</strong> with clear guidelines
            <span class="block mt-4 text-lg md:text-xl opacity-80">Protecting both patient and clinic interests</span>
        </p>
        
        <!-- Quick Policy Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto animate-[scaleInCancel_0.8s_ease_0.8s_both]">
            <div class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl hover:bg-white/30 transition-all">
                <svg class="w-8 h-8 mx-auto mb-3 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="font-bold text-lg">48+ Hours</h3>
                <p class="text-sm opacity-80">No cancellation fees</p>
            </div>
            <div class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl hover:bg-white/30 transition-all">
                <svg class="w-8 h-8 mx-auto mb-3 text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <h3 class="font-bold text-lg">24-48 Hours</h3>
                <p class="text-sm opacity-80">50% cancellation fee</p>
            </div>
            <div class="bg-white/20 backdrop-blur-sm border border-white/30 p-6 rounded-xl hover:bg-white/30 transition-all">
                <svg class="w-8 h-8 mx-auto mb-3 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <h3 class="font-bold text-lg">Under 24 Hours</h3>
                <p class="text-sm opacity-80">Full treatment fee</p>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Revolutionary Policy Content Section -->
<section class="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 30% 30%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 70% 70%, var(--primary-blue) 2px, transparent 2px); background-size: 80px 80px;"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 relative">
        
        <!-- Policy Overview Section -->
        <div class="text-center mb-20">
            <div class="policy-icon mx-auto">
                <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Understanding Our Cancellation Policy
            </h2>
            <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Our cancellation policy is designed to be fair and flexible while ensuring we can provide the highest quality medical aesthetic care to all our patients.
            </p>
        </div>

        <!-- Timeline Section -->
        <div class="mb-20">
            <h3 class="text-3xl font-bold text-gray-900 mb-12 text-center">Cancellation Timeline & Fees</h3>
            
            <div class="interactive-timeline space-y-8">
                <!-- 48+ Hours Notice -->
                <div class="timeline-card success relative">
                    <div class="timeline-point" style="top: 2rem;"></div>
                    <div class="flex items-start">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mr-6 flex-shrink-0">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-2xl font-bold text-gray-900 mb-3">48+ Hours Notice</h4>
                            <p class="text-gray-600 mb-4 text-lg">Cancel or reschedule your appointment with no penalties when you provide at least 48 hours advance notice.</p>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex justify-between items-center">
                                    <span class="font-semibold text-green-800">Cancellation Fee:</span>
                                    <span class="text-2xl font-bold text-green-600">$0</span>
                                </div>
                                <p class="text-green-700 text-sm mt-2">Full refund or free rescheduling available</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 24-48 Hours Notice -->
                <div class="timeline-card warning relative">
                    <div class="timeline-point" style="top: 2rem; border-color: #f59e0b;"></div>
                    <div class="flex items-start">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mr-6 flex-shrink-0">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-2xl font-bold text-gray-900 mb-3">24-48 Hours Notice</h4>
                            <p class="text-gray-600 mb-4 text-lg">Cancellations within this timeframe incur a partial fee to cover preparation costs and lost scheduling opportunities.</p>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex justify-between items-center">
                                    <span class="font-semibold text-yellow-800">Cancellation Fee:</span>
                                    <span class="text-2xl font-bold text-yellow-600">50% of Treatment Cost</span>
                                </div>
                                <p class="text-yellow-700 text-sm mt-2">Rescheduling still available with fee</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Less than 24 Hours / No Show -->
                <div class="timeline-card danger relative">
                    <div class="timeline-point" style="top: 2rem; border-color: #ef4444;"></div>
                    <div class="flex items-start">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mr-6 flex-shrink-0">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-2xl font-bold text-gray-900 mb-3">Less than 24 Hours Notice / No Show</h4>
                            <p class="text-gray-600 mb-4 text-lg">Late cancellations and no-shows result in full treatment fee to compensate for reserved medical staff time and prepared materials.</p>
                            <div class="bg-red-50 p-4 rounded-lg">
                                <div class="flex justify-between items-center">
                                    <span class="font-semibold text-red-800">Cancellation Fee:</span>
                                    <span class="text-2xl font-bold text-red-600">100% of Treatment Cost</span>
                                </div>
                                <p class="text-red-700 text-sm mt-2">Full treatment fee charged</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Special Circumstances Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
            <div class="cancellation-card">
                <div class="policy-icon">
                    <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Emergency Exceptions</h3>
                <p class="text-gray-600 mb-6 leading-relaxed">
                    We understand that medical emergencies and unforeseen circumstances can occur. Our medical team handles these situations with compassion and discretion.
                </p>
                
                <div class="space-y-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-redolence-green mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-gray-700">Medical emergencies (patient or immediate family)</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-redolence-green mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-gray-700">Severe weather conditions or natural disasters</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-redolence-green mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-gray-700">Sudden illness that prevents safe treatment</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-redolence-green mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-gray-700">Military deployment or emergency travel</span>
                    </div>
                </div>
                
                <div class="medical-alert info mt-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-blue-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h4 class="font-bold text-blue-900 mb-2">Documentation Required</h4>
                            <p class="text-blue-800">Emergency exceptions require appropriate documentation and are reviewed case-by-case by our medical director.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="cancellation-card">
                <div class="policy-icon">
                    <svg class="w-10 h-10 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Treatment Package Policies</h3>
                <p class="text-gray-600 mb-6 leading-relaxed">
                    Special considerations apply to patients who have purchased treatment packages or multiple session plans.
                </p>
                
                <div class="fee-structure">
                    <h4 class="font-bold text-gray-900 mb-4">Package Cancellation Rules</h4>
                    <div class="space-y-3">
                        <div class="fee-item">
                            <span class="font-semibold text-gray-700">First Cancellation</span>
                            <span class="text-redolence-green font-bold">Standard Policy Applies</span>
                        </div>
                        <div class="fee-item">
                            <span class="font-semibold text-gray-700">Second Cancellation</span>
                            <span class="text-yellow-600 font-bold">Additional 25% Fee</span>
                        </div>
                        <div class="fee-item">
                            <span class="font-semibold text-gray-700">Third+ Cancellation</span>
                            <span class="text-red-600 font-bold">Package Review Required</span>
                        </div>
                    </div>
                </div>
                
                <div class="medical-alert warning">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-yellow-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <div>
                            <h4 class="font-bold text-yellow-900 mb-2">Package Expiration</h4>
                            <p class="text-yellow-800">Treatment packages expire 12 months from purchase date. Extensions may be available for medical reasons.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- How to Cancel Section -->
        <div class="cancellation-card mb-20">
            <div class="text-center mb-12">
                <div class="policy-icon mx-auto">
                    <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                </div>
                <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    How to Cancel or Reschedule
                </h3>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Multiple convenient ways to manage your appointments with our medical team.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-3">Call Our Clinic</h4>
                    <p class="text-gray-600 mb-4">Speak directly with our patient coordinators</p>
                    <a href="tel:+255781985757" class="text-redolence-green font-semibold hover:text-redolence-blue transition-colors">
                        +255 781 985 757
                    </a>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-3">Email Us</h4>
                    <p class="text-gray-600 mb-4">Send cancellation requests via email</p>
                    <a href="mailto:<EMAIL>" class="text-redolence-blue font-semibold hover:text-redolence-green transition-colors">
                        <EMAIL>
                    </a>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-3">Patient Portal</h4>
                    <p class="text-gray-600 mb-4">Manage appointments online 24/7</p>
                    <a href="#" class="text-purple-600 font-semibold hover:text-redolence-green transition-colors">
                        Access Portal
                    </a>
                </div>
            </div>
            
            <div class="medical-alert success mt-12">
                <div class="flex items-start">
                    <svg class="w-6 h-6 text-green-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h4 class="font-bold text-green-900 mb-2">Confirmation Required</h4>
                        <p class="text-green-800">All cancellations and rescheduling requests will be confirmed within 2 hours during business hours. You will receive email confirmation of any changes.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Important Notes Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
            <div class="medical-alert danger">
                <div class="flex items-start">
                    <svg class="w-6 h-6 text-red-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <div>
                        <h4 class="font-bold text-red-900 mb-2">Important Medical Notice</h4>
                        <p class="text-red-800">Certain medical treatments require specific preparation. Last-minute cancellations may affect your treatment timeline and optimal results. Please plan accordingly.</p>
                    </div>
                </div>
            </div>
            
            <div class="medical-alert info">
                <div class="flex items-start">
                    <svg class="w-6 h-6 text-blue-600 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h4 class="font-bold text-blue-900 mb-2">Rescheduling Priority</h4>
                        <p class="text-blue-800">When rescheduling, we prioritize finding you the earliest available appointment that fits your treatment timeline and our medical recommendations.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="text-center">
            <div class="cancellation-card max-w-4xl mx-auto">
                <div class="policy-icon mx-auto">
                    <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Questions About Cancellations?
                </h2>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Our patient care team is here to help you understand our policies and assist with any scheduling needs.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="contact.php" class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-redolence-blue hover:from-redolence-blue hover:to-redolence-green text-white px-8 py-4 rounded-xl font-bold text-lg transition-all hover:scale-105 shadow-lg">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Contact Patient Care
                    </a>
                    <a href="tel:+255781985757" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-green px-8 py-4 rounded-xl font-bold text-lg transition-all border-2 border-redolence-green/20 hover:border-redolence-green/40">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Call: +255 781 985 757
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary JavaScript -->
<script>
// Enhanced page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth entrance animations to cancellation cards
    const cancellationCards = document.querySelectorAll('.cancellation-card');
    cancellationCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.23, 1, 0.32, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });

    // Add entrance animations to timeline cards
    const timelineCards = document.querySelectorAll('.timeline-card');
    timelineCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateX(-50px)';

        setTimeout(() => {
            card.style.transition = 'all 0.8s cubic-bezier(0.23, 1, 0.32, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateX(0)';
        }, (index * 300) + 500);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Add highlight effect
                target.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    target.style.transform = 'scale(1)';
                }, 300);
            }
        });
    });

    // Add hover enhancements to policy icons
    const policyIcons = document.querySelectorAll('.policy-icon');
    policyIcons.forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Add interactive timeline point effects
    const timelinePoints = document.querySelectorAll('.timeline-point');
    timelinePoints.forEach(point => {
        point.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.5)';
            this.style.boxShadow = '0 0 25px rgba(73, 167, 92, 0.6)';
        });
        
        point.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = 'none';
        });
    });

    // Add fee item hover effects
    const feeItems = document.querySelectorAll('.fee-item');
    feeItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px)';
            this.style.background = 'rgba(73, 167, 92, 0.08)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
            this.style.background = 'transparent';
        });
    });

    // Add medical alert pulse effect for danger alerts
    const dangerAlerts = document.querySelectorAll('.medical-alert.danger');
    dangerAlerts.forEach(alert => {
        setInterval(() => {
            alert.style.transform = 'scale(1.02)';
            setTimeout(() => {
                alert.style.transform = 'scale(1)';
            }, 200);
        }, 3000);
    });
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>