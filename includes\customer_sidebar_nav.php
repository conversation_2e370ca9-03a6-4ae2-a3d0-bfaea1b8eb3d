<?php
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();

// Normalize current path for comparison
$normalizedCurrentPath = rtrim(parse_url($currentPath, PHP_URL_PATH), '/');
$customerBasePath = $basePath . '/customer';
$navigation = [
    [
        'name' => 'Dashboard',
        'href' => $basePath . '/customer',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />',
        'description' => 'Overview & quick actions'
    ],
    [
        'name' => 'Book Appointment',
        'href' => $basePath . '/customer/book',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />',
        'description' => 'Schedule new treatments'
    ],
    [
        'name' => 'My Bookings',
        'href' => $basePath . '/customer/bookings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />',
        'description' => 'View appointment history'
    ],
    [
        'name' => 'My Wishlist',
        'href' => $basePath . '/customer/wishlist',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />',
        'description' => 'Your saved treatments',
        'badge' => true
    ],
    [
        'name' => 'Points & Rewards',
        'href' => $basePath . '/customer/rewards',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />',
        'description' => 'Loyalty program & rewards'
    ],
    [
        'name' => 'Profile',
        'href' => $basePath . '/customer/profile',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />',
        'description' => 'Account settings'
    ]
];
?>

<style>
/* Mobile Navigation Styles */
.mobile-nav-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.25rem;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 0.5rem;
    border: 1px solid transparent;
}

.mobile-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.mobile-nav-item:hover::before {
    transform: scaleY(1);
}

.mobile-nav-item.active {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.15), rgba(37, 99, 235, 0.15));
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 4px 12px rgba(73, 167, 92, 0.2);
}

.mobile-nav-item.active::before {
    transform: scaleY(1);
}

.mobile-nav-item:hover {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.08), rgba(37, 99, 235, 0.08));
    border-color: rgba(73, 167, 92, 0.2);
    transform: translateX(4px);
}

.mobile-nav-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.mobile-nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 1rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.mobile-nav-item.active .mobile-nav-icon {
    color: #49a75c;
    transform: scale(1.1);
}

.mobile-nav-item:hover .mobile-nav-icon {
    color: #49a75c;
}

.mobile-nav-text {
    flex: 1;
}

.mobile-nav-title {
    font-weight: 600;
    font-size: 0.95rem;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.mobile-nav-description {
    font-size: 0.8rem;
    color: #64748b;
    line-height: 1.3;
}

.mobile-nav-item.active .mobile-nav-title {
    color: #49a75c;
}

.mobile-nav-item:hover .mobile-nav-title {
    color: #49a75c;
}

.mobile-nav-badge {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}
</style>

<nav class="space-y-1">
    <?php
    // Get wishlist count for badge
    $wishlistCount = 0;
    if (isset($_SESSION['user_id'])) {
        try {
            // Try to get wishlist count
            global $database;
            $result = $database->fetch("SELECT COUNT(*) as count FROM wishlist WHERE user_id = ?", [$_SESSION['user_id']]);
            $wishlistCount = $result ? $result['count'] : 0;
        } catch (Exception $e) {
            // Fallback if wishlist table doesn't exist
            $wishlistCount = 0;
        }
    }

    foreach ($navigation as $item):
        // Normalize item href for comparison
        $normalizedItemHref = rtrim($item['href'], '/');

        // Check if this menu item is active
        $isActive = false;

        // Exact match for all pages
        if ($normalizedCurrentPath === $normalizedItemHref) {
            $isActive = true;
        }
        // For non-dashboard items, check sub-pages
        elseif ($normalizedItemHref !== $customerBasePath && strpos($normalizedCurrentPath . '/', $normalizedItemHref . '/') === 0) {
            $isActive = true;
        }
    ?>
        <a href="<?= $item['href'] ?>" class="mobile-nav-item <?= $isActive ? 'active' : '' ?>">
            <div class="mobile-nav-content">
                <svg class="mobile-nav-icon <?= $isActive ? 'text-redolence-green' : 'text-gray-400' ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <?= $item['icon'] ?>
                </svg>
                <div class="mobile-nav-text">
                    <div class="mobile-nav-title"><?= $item['name'] ?></div>
                    <div class="mobile-nav-description"><?= $item['description'] ?></div>
                </div>
            </div>
            <?php if (isset($item['badge']) && $item['badge'] && $wishlistCount > 0): ?>
                <span class="mobile-nav-badge" id="mobile-wishlist-badge">
                    <?= $wishlistCount ?>
                </span>
            <?php endif; ?>
        </a>
    <?php endforeach; ?>
</nav>

<!-- Mobile Quick Actions -->
<div class="mt-8 pt-6 border-t border-gray-200">
    <h3 class="text-xs font-semibold text-redolence-green uppercase tracking-wider mb-4">Quick Actions</h3>
    <div class="space-y-3">
        <a href="<?= $basePath ?>/customer/book" class="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-redolence-green to-green-600 text-white hover:from-green-600 hover:to-green-700 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg">
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Book Appointment
        </a>

        <a href="<?= $basePath ?>/" class="w-full flex items-center justify-center px-4 py-3 border border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white rounded-xl text-sm font-medium transition-all duration-300">
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            View Website
        </a>
    </div>
</div>