-- Redolence Medi Aesthetics Database Setup
-- This script creates the database and imports the structure

-- Create the database
CREATE DATABASE IF NOT EXISTS `redolence` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Use the database
USE `redolence`;

-- Set SQL mode and other settings
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- Note: After running this script, you need to import the table structure and data
-- from redolence.sql file. You can do this by:
-- 1. Running this script first to create the database
-- 2. Then importing redolence.sql into the newly created 'redolence' database

-- Alternatively, you can use phpMyAdmin:
-- 1. Create a new database called 'redolence'
-- 2. Import the redolence.sql file into that database

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
