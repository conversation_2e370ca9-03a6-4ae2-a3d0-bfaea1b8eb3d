<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

// Initialize progressive report handler
$progressiveReport = new ProgressiveReport();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            // Check if searching by client_id
            $clientId = $_GET['client_id'] ?? '';

            if ($clientId) {
                // Get reports for specific client
                $report = $progressiveReport->getByClientId($clientId);
                $reports = $report ? [$report] : [];

                echo json_encode([
                    'success' => true,
                    'data' => [
                        'reports' => $reports,
                        'total' => count($reports),
                        'total_pages' => 1,
                        'current_page' => 1
                    ]
                ]);
            } else {
                // Get progressive reports with pagination and filters
                $page = max(1, intval($_GET['page'] ?? 1));
                $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
                $search = $_GET['search'] ?? '';
                $status = $_GET['status'] ?? 'all';

                $result = $progressiveReport->getAll($page, $limit, $search, $status);

                if ($result === false) {
                    throw new Exception('Failed to fetch progressive reports');
                }

                echo json_encode([
                    'success' => true,
                    'data' => $result
                ]);
            }
            break;
            
        case 'POST':
            // Create new progressive report
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                throw new Exception('Invalid JSON input');
            }
            
            // Validate required fields
            if (empty($input['client_id'])) {
                throw new Exception('Client ID is required');
            }
            
            $result = $progressiveReport->create(
                $input['client_id'],
                $input['title'] ?? null,
                $input['description'] ?? null,
                $_SESSION['user_id']
            );
            
            if ($result === false) {
                throw new Exception('Failed to create progressive report');
            }
            
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Progressive report created successfully'
            ]);
            break;
            
        case 'PUT':
            // Update progressive report
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || empty($input['id'])) {
                throw new Exception('Report ID is required');
            }
            
            // Check if report exists and user can access it
            $existingReport = $progressiveReport->getById($input['id']);
            if (!$existingReport) {
                throw new Exception('Progressive report not found');
            }
            
            $result = $progressiveReport->update($input['id'], $input);
            
            if ($result === false) {
                throw new Exception('Failed to update progressive report');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Progressive report updated successfully'
            ]);
            break;
            
        case 'DELETE':
            // Delete progressive report
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || empty($input['id'])) {
                throw new Exception('Report ID is required');
            }
            
            // Check if report exists
            $existingReport = $progressiveReport->getById($input['id']);
            if (!$existingReport) {
                throw new Exception('Progressive report not found');
            }
            
            $result = $progressiveReport->delete($input['id']);
            
            if ($result === false) {
                throw new Exception('Failed to delete progressive report');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Progressive report deleted successfully'
            ]);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
