<?php
/**
 * Debug Services Issues
 * Quick test to see what's happening with services
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Services Debug</h1>";

try {
    // Test 1: Basic services count
    echo "<h2>1. Basic Services Test</h2>";
    $basicCount = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
    echo "<p><strong>Active services in database:</strong> $basicCount</p>";
    
    if ($basicCount == 0) {
        echo "<p style='color: red;'>❌ No active services found! This is why services page is empty.</p>";
        echo "<p><strong>Solution:</strong> Add some services through the admin panel or check if services exist but are inactive.</p>";
        
        // Check if any services exist at all
        $totalCount = $database->fetch("SELECT COUNT(*) as count FROM services")['count'];
        echo "<p>Total services (including inactive): $totalCount</p>";
        
        if ($totalCount > 0) {
            echo "<p style='color: orange;'>⚠ You have services but they're all inactive. Check the is_active field.</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Found $basicCount active services</p>";
        
        // Test 2: Sample service data
        echo "<h2>2. Sample Service Data</h2>";
        $sampleService = $database->fetch("SELECT * FROM services WHERE is_active = 1 LIMIT 1");
        
        if ($sampleService) {
            echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px;'>";
            echo "<h3>Sample Service:</h3>";
            echo "<p><strong>ID:</strong> " . htmlspecialchars($sampleService['id']) . "</p>";
            echo "<p><strong>Name:</strong> " . htmlspecialchars($sampleService['name']) . "</p>";
            echo "<p><strong>Name (decoded):</strong> " . html_entity_decode($sampleService['name'], ENT_QUOTES, 'UTF-8') . "</p>";
            echo "<p><strong>Description (raw):</strong> " . htmlspecialchars(substr($sampleService['description'] ?? '', 0, 200)) . "...</p>";
            echo "<p><strong>Description (decoded):</strong> " . htmlspecialchars(substr(html_entity_decode($sampleService['description'] ?? '', ENT_QUOTES, 'UTF-8'), 0, 200)) . "...</p>";
            echo "<p><strong>Price:</strong> " . ($sampleService['price'] ?? 'NULL') . "</p>";
            echo "<p><strong>Duration:</strong> " . ($sampleService['duration'] ?? 'NULL') . "</p>";
            echo "<p><strong>Is Active:</strong> " . ($sampleService['is_active'] ? 'Yes' : 'No') . "</p>";
            echo "</div>";
            
            // Test 3: API endpoint
            echo "<h2>3. API Test</h2>";
            $apiUrl = getBasePath() . "/api/services/get.php?id=" . urlencode($sampleService['id']);
            echo "<p><a href='$apiUrl' target='_blank'>Test API for this service</a></p>";
            
            // Test 4: Modal test
            echo "<h2>4. Modal Test</h2>";
            echo "<button onclick='testModal()' style='background: #49a75c; color: white; padding: 10px 20px; border: none; border-radius: 8px; cursor: pointer;'>Test Modal</button>";
            echo "<div id='testResult' style='margin-top: 10px;'></div>";
        }
    }
    
    // Test 5: Check medical fields
    echo "<h2>5. Medical Fields Check</h2>";
    try {
        $testQuery = $database->fetch("SELECT featured, popular, new_treatment, session_frequency, technology_used, sort_order FROM services LIMIT 1");
        echo "<p style='color: green;'>✅ Medical fields exist in database</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Medical fields missing: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>Solution:</strong> Run the database migration to add medical treatment fields.</p>";
    }

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='" . getBasePath() . "/services.php'>Test Services Page</a></p>";
echo "<p><a href='" . getBasePath() . "/index.php'>Test Index Page</a></p>";
?>

<script>
function testModal() {
    const resultDiv = document.getElementById('testResult');
    resultDiv.innerHTML = 'Testing API...';
    
    // Get the first service ID from the page
    const serviceId = '<?= $sampleService['id'] ?? '' ?>';
    if (!serviceId) {
        resultDiv.innerHTML = '<p style="color: red;">No service ID available for testing</p>';
        return;
    }
    
    fetch('<?= getBasePath() ?>/api/services/get.php?id=' + serviceId)
        .then(response => response.json())
        .then(service => {
            if (service.error) {
                resultDiv.innerHTML = '<p style="color: red;">API Error: ' + service.error + '</p>';
                return;
            }
            
            resultDiv.innerHTML = `
                <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-top: 10px;">
                    <h4>✅ API Response Success</h4>
                    <p><strong>Name:</strong> ${service.name}</p>
                    <p><strong>Description Preview:</strong> ${service.description ? service.description.substring(0, 100) + '...' : 'No description'}</p>
                    <p><strong>Featured:</strong> ${service.featured ? 'Yes' : 'No'}</p>
                    <p><strong>Popular:</strong> ${service.popular ? 'Yes' : 'No'}</p>
                    <p><strong>New Treatment:</strong> ${service.new_treatment ? 'Yes' : 'No'}</p>
                </div>
            `;
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style="color: red;">Fetch Error: ' + error.message + '</p>';
        });
}
</script>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #49a75c;
}
</style>
