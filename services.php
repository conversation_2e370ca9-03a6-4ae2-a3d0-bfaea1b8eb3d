<?php
/**
 * Services Page - Medical Aesthetics Treatments
 * Displays all available medical treatments with filtering and search
 */

require_once __DIR__ . '/config/app.php';

// Get all medical treatments (with fallback for missing fields)
try {
    $allTreatments = $database->fetchAll(
        "SELECT *, 
                COALESCE(sort_order, 0) as sort_order,
                COALESCE(featured, 0) as featured,
                COALESCE(popular, 0) as popular,
                COALESCE(new_treatment, 0) as new_treatment,
                COALESCE(session_frequency, '') as session_frequency,
                COALESCE(technology_used, '') as technology_used
         FROM services WHERE is_active = 1 ORDER BY sort_order ASC, name ASC"
    );
} catch (Exception $e) {
    // Fallback for when medical fields don't exist yet
    error_log("Medical fields not found, using fallback query: " . $e->getMessage());
    try {
        $allTreatments = $database->fetchAll(
            "SELECT *, 
                    0 as sort_order,
                    0 as featured,
                    0 as popular,
                    0 as new_treatment,
                    '' as session_frequency,
                    '' as technology_used
             FROM services WHERE is_active = 1 ORDER BY name ASC"
        );
    } catch (Exception $e2) {
        // Ultimate fallback - just get basic services
        error_log("Even fallback query failed, using basic query: " . $e2->getMessage());
        $allTreatments = $database->fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name ASC");
        // Add missing fields manually
        foreach ($allTreatments as &$service) {
            $service['sort_order'] = 0;
            $service['featured'] = 0;
            $service['popular'] = 0;
            $service['new_treatment'] = 0;
            $service['session_frequency'] = '';
            $service['technology_used'] = '';
        }
    }
}

// Ensure we have an array
$allTreatments = $allTreatments ?: [];

// Get filter parameters
$searchQuery = sanitize($_GET['search'] ?? '');
$filterType = sanitize($_GET['filter'] ?? ''); // featured, popular, new
$minPrice = sanitize($_GET['min_price'] ?? '');
$maxPrice = sanitize($_GET['max_price'] ?? '');
$maxDuration = sanitize($_GET['max_duration'] ?? '');

// Pagination
$page = max(1, (int)($_GET['page'] ?? 1));
$servicesPerPage = 12;
$showAll = isset($_GET['show_all']) && $_GET['show_all'] === '1';

// Check if filters are applied
$hasFilters = $searchQuery || $filterType || $minPrice !== '' || $maxPrice !== '' || $maxDuration !== '';

// Determine which services to display
$displayServices = $allTreatments;

// Apply filters if any
if ($hasFilters) {
    $displayServices = array_filter($allTreatments, function($service) use ($searchQuery, $filterType, $minPrice, $maxPrice, $maxDuration) {
        // Search filter
        if ($searchQuery) {
            $searchMatch = stripos($service['name'], $searchQuery) !== false || 
                          stripos($service['description'] ?? '', $searchQuery) !== false;
            if (!$searchMatch) return false;
        }
        
        // Type filter
        if ($filterType === 'featured' && !$service['featured']) return false;
        if ($filterType === 'popular' && !$service['popular']) return false;
        if ($filterType === 'new' && !$service['new_treatment']) return false;
        
        // Price filters
        if ($minPrice !== '' && $service['price'] && $service['price'] < (float)$minPrice) return false;
        if ($maxPrice !== '' && $service['price'] && $service['price'] > (float)$maxPrice) return false;
        
        // Duration filter
        if ($maxDuration !== '' && $service['duration'] && $service['duration'] > (int)$maxDuration) return false;
        
        return true;
    });
}

// Apply pagination if not showing all
if (!$showAll) {
    $offset = ($page - 1) * $servicesPerPage;
    $displayServices = array_slice($displayServices, $offset, $servicesPerPage);
}

$totalServices = count($allTreatments);
$totalPages = ceil($totalServices / $servicesPerPage);

// Helper function to build pagination URLs
function buildPaginationUrl($pageNum, $search = '', $filter = '', $minPrice = '', $maxPrice = '', $maxDuration = '', $showAll = false) {
    $params = ['page' => $pageNum];
    if ($search) $params['search'] = $search;
    if ($filter) $params['filter'] = $filter;
    if ($minPrice !== '') $params['min_price'] = $minPrice;
    if ($maxPrice !== '') $params['max_price'] = $maxPrice;
    if ($maxDuration !== '') $params['max_duration'] = $maxDuration;
    if ($showAll) $params['show_all'] = '1';

    return getBasePath() . '/services.php?' . http_build_query($params);
}

$pageTitle = "Advanced Medical Aesthetics Services";
$pageDescription = "Discover our comprehensive range of advanced medical aesthetics treatments at Redolence Medi Aesthetics. Professional procedures with cutting-edge technology.";

include __DIR__ . '/includes/header.php';
?>

<style>
/* Treatment description styles */
.treatment-description h3 {
    color: #1f2937;
    font-weight: 700;
    font-size: 1.125rem;
    margin: 1rem 0 0.5rem 0;
}

.treatment-description h4 {
    color: #374151;
    font-weight: 600;
    font-size: 1rem;
    margin: 0.75rem 0 0.5rem 0;
}

.treatment-description ul {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0;
}

.treatment-description li {
    padding: 0.25rem 0;
    color: #4b5563;
}

.treatment-description p {
    color: #6b7280;
    line-height: 1.6;
    margin: 0.5rem 0;
}

.treatment-description strong {
    color: #1f2937;
    font-weight: 600;
}

/* Prose styling for modal */
.prose-redolence {
    color: #374151;
    line-height: 1.7;
}

.prose-redolence h1,
.prose-redolence h2,
.prose-redolence h3 {
    color: #49a75c;
    font-weight: 700;
    font-size: 1.25rem;
    margin: 1.5rem 0 0.75rem 0;
    line-height: 1.4;
}

.prose-redolence h4,
.prose-redolence h5,
.prose-redolence h6 {
    color: #2563eb;
    font-weight: 600;
    font-size: 1.125rem;
    margin: 1rem 0 0.5rem 0;
    line-height: 1.4;
}

.prose-redolence ul,
.prose-redolence ol {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.prose-redolence li {
    padding: 0.375rem 0;
    color: #374151;
    font-size: 0.95rem;
    line-height: 1.6;
}

.prose-redolence p {
    color: #4b5563;
    line-height: 1.7;
    margin: 0.75rem 0;
}

.prose-redolence strong,
.prose-redolence b {
    color: #1f2937;
    font-weight: 600;
}

.prose-redolence em,
.prose-redolence i {
    font-style: italic;
    color: #6b7280;
}

/* First paragraph styling */
.prose-redolence p:first-child {
    margin-top: 0;
}

.prose-redolence p:last-child {
    margin-bottom: 0;
}

/* Enhanced Filter Styles */
.filter-radio-custom {
    width: 16px;
    height: 16px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
}

.filter-radio-custom::after {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #49a75c;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.2s ease;
}

input[type="radio"]:checked + .filter-radio-custom {
    border-color: #49a75c;
}

input[type="radio"]:checked + .filter-radio-custom::after {
    transform: translate(-50%, -50%) scale(1);
}

.filter-checkbox-custom {
    width: 16px;
    height: 16px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.filter-checkbox-custom::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.2s ease;
}

input[type="checkbox"]:checked + .filter-checkbox-custom {
    background: #49a75c;
    border-color: #49a75c;
}

input[type="checkbox"]:checked + .filter-checkbox-custom::after {
    transform: translate(-50%, -50%) scale(1);
}

/* Price Range Slider */
.slider {
    -webkit-appearance: none;
    appearance: none;
    background: linear-gradient(to right, #49a75c 0%, #49a75c var(--value, 50%), #e5e7eb var(--value, 50%), #e5e7eb 100%);
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #49a75c;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(73, 167, 92, 0.4);
}

.slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #49a75c;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Search Suggestions */
.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;
}

.suggestion-item:hover {
    background-color: #f9fafb;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-highlight {
    background-color: #fef3c7;
    font-weight: 600;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    items-center;
    justify-content: center;
    z-index: 10;
    border-radius: 1rem;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #49a75c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Filter Animations */
.filter-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Active Filter Indicators */
.active-filter-tag {
    display: inline-flex;
    items-center;
    background: linear-gradient(135deg, #49a75c, #5894d2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin: 2px;
}

.active-filter-tag .remove-filter {
    margin-left: 6px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.active-filter-tag .remove-filter:hover {
    opacity: 1;
}
</style>

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-redolence-green/20">
    <!-- Background Image with Overlay -->
    <div class="absolute inset-0 z-0">
        <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=100" 
             alt="Medical Aesthetics Clinic" 
             class="w-full h-full object-cover opacity-30">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-900/80 via-gray-800/70 to-redolence-green/30"></div>
    </div>

    <!-- Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-6 text-center">
        <div class="mb-8">
            <div class="inline-flex items-center bg-white/10 text-white px-6 py-3 rounded-full text-sm font-semibold mb-6 backdrop-blur-sm border border-white/20">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
                Medical Aesthetics Services
            </div>
            <h1 class="text-4xl md:text-7xl font-bold text-white mb-6 leading-tight">
                Advanced
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Medical Treatments
                </span>
            </h1>
            <p class="text-xl md:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed mb-8">
                Discover our comprehensive range of cutting-edge medical aesthetics treatments designed to enhance your natural beauty with professional care and advanced technology.
            </p>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-redolence-green mb-2"><?= count($allTreatments) ?></div>
                <div class="text-white/80 text-sm md:text-base">Total Treatments</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-yellow-400 mb-2"><?= count(array_filter($allTreatments, fn($s) => $s['featured'])) ?></div>
                <div class="text-white/80 text-sm md:text-base">Featured</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-red-400 mb-2"><?= count(array_filter($allTreatments, fn($s) => $s['popular'])) ?></div>
                <div class="text-white/80 text-sm md:text-base">Popular</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-green-400 mb-2"><?= count(array_filter($allTreatments, fn($s) => $s['new_treatment'])) ?></div>
                <div class="text-white/80 text-sm md:text-base">New</div>
            </div>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#treatments" class="inline-flex items-center justify-center bg-redolence-green hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                Browse Treatments
            </a>
            <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
                Book Consultation
            </a>
        </div>
    </div>
</section>

<!-- Treatments Section -->
<section id="treatments" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Medical Aesthetics Treatments
                </span>
            </h2>
            <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Discover our comprehensive range of advanced medical aesthetics treatments designed to enhance your natural beauty with cutting-edge technology and expert care.
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-redolence-green to-redolence-blue mx-auto rounded-full mt-6"></div>
        </div>

        <!-- Enhanced Filter and Search Interface -->
        <div class="mb-12">
            <!-- Search Bar -->
            <div class="relative mb-8">
                <div class="max-w-2xl mx-auto">
                    <div class="relative">
                        <input type="text"
                               id="treatmentSearch"
                               placeholder="Search treatments by name or description..."
                               class="w-full pl-12 pr-16 py-4 text-lg border-2 border-gray-200 rounded-2xl focus:border-redolence-green focus:ring-4 focus:ring-redolence-green/20 transition-all duration-300 bg-white shadow-lg"
                               value="<?= htmlspecialchars($searchQuery) ?>">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                            <button type="button"
                                    id="clearSearch"
                                    class="text-gray-400 hover:text-gray-600 transition-colors <?= empty($searchQuery) ? 'hidden' : '' ?>">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <!-- Search Suggestions -->
                    <div id="searchSuggestions" class="absolute z-10 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl hidden max-h-64 overflow-y-auto">
                        <!-- Suggestions will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Filter Interface -->
            <div class="bg-gradient-to-r from-gray-50 to-white rounded-3xl p-8 shadow-xl border border-gray-100">
                <!-- Filter Header -->
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-redolence-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                        </svg>
                        <h3 class="text-xl font-bold text-gray-900">Filter Treatments</h3>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span id="resultsCounter" class="text-sm text-gray-600 font-medium">
                            <?= count($displayServices) ?> treatments found
                        </span>
                        <button type="button"
                                id="clearAllFilters"
                                class="text-redolence-blue hover:text-redolence-green font-medium transition-colors <?= !$hasFilters ? 'hidden' : '' ?>">
                            Clear all filters
                        </button>
                    </div>
                </div>

                <!-- Filter Options Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Treatment Type Filter -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Treatment Type</label>
                        <div class="space-y-2">
                            <label class="flex items-center cursor-pointer group">
                                <input type="radio" name="filterType" value="" class="sr-only" <?= empty($filterType) ? 'checked' : '' ?>>
                                <div class="filter-radio-custom mr-3"></div>
                                <span class="text-sm text-gray-700 group-hover:text-gray-900">All Treatments</span>
                            </label>
                            <label class="flex items-center cursor-pointer group">
                                <input type="radio" name="filterType" value="featured" class="sr-only" <?= $filterType === 'featured' ? 'checked' : '' ?>>
                                <div class="filter-radio-custom mr-3"></div>
                                <span class="text-sm text-gray-700 group-hover:text-gray-900">⭐ Featured</span>
                            </label>
                            <label class="flex items-center cursor-pointer group">
                                <input type="radio" name="filterType" value="popular" class="sr-only" <?= $filterType === 'popular' ? 'checked' : '' ?>>
                                <div class="filter-radio-custom mr-3"></div>
                                <span class="text-sm text-gray-700 group-hover:text-gray-900">🔥 Popular</span>
                            </label>
                            <label class="flex items-center cursor-pointer group">
                                <input type="radio" name="filterType" value="new" class="sr-only" <?= $filterType === 'new' ? 'checked' : '' ?>>
                                <div class="filter-radio-custom mr-3"></div>
                                <span class="text-sm text-gray-700 group-hover:text-gray-900">✨ New</span>
                            </label>
                        </div>
                    </div>

                    <!-- Price Range Filter -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Price Range (TZS)</label>
                        <div class="space-y-4">
                            <div class="relative">
                                <input type="range"
                                       id="priceRange"
                                       min="0"
                                       max="1000000"
                                       step="10000"
                                       value="<?= $maxPrice ?: '1000000' ?>"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider">
                                <div class="flex justify-between text-xs text-gray-500 mt-2">
                                    <span>0</span>
                                    <span>1M+</span>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-2">
                                <input type="number"
                                       id="minPrice"
                                       placeholder="Min"
                                       value="<?= $minPrice ?>"
                                       class="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:border-redolence-green focus:ring-2 focus:ring-redolence-green/20">
                                <input type="number"
                                       id="maxPrice"
                                       placeholder="Max"
                                       value="<?= $maxPrice ?>"
                                       class="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:border-redolence-green focus:ring-2 focus:ring-redolence-green/20">
                            </div>
                        </div>
                    </div>

                    <!-- Duration Filter -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Duration</label>
                        <div class="space-y-2">
                            <label class="flex items-center cursor-pointer group">
                                <input type="checkbox" value="30" class="duration-filter sr-only">
                                <div class="filter-checkbox-custom mr-3"></div>
                                <span class="text-sm text-gray-700 group-hover:text-gray-900">30 minutes</span>
                            </label>
                            <label class="flex items-center cursor-pointer group">
                                <input type="checkbox" value="60" class="duration-filter sr-only">
                                <div class="filter-checkbox-custom mr-3"></div>
                                <span class="text-sm text-gray-700 group-hover:text-gray-900">60 minutes</span>
                            </label>
                            <label class="flex items-center cursor-pointer group">
                                <input type="checkbox" value="90" class="duration-filter sr-only">
                                <div class="filter-checkbox-custom mr-3"></div>
                                <span class="text-sm text-gray-700 group-hover:text-gray-900">90+ minutes</span>
                            </label>
                        </div>
                    </div>

                    <!-- Technology Filter -->
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Technology</label>
                        <select id="technologyFilter" class="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:border-redolence-green focus:ring-2 focus:ring-redolence-green/20 bg-white">
                            <option value="">All Technologies</option>
                            <?php
                            // Get unique technologies from all treatments
                            $technologies = array_unique(array_filter(array_column($allTreatments, 'technology_used')));
                            foreach ($technologies as $tech):
                            ?>
                                <option value="<?= htmlspecialchars($tech) ?>"><?= htmlspecialchars($tech) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <!-- Apply Filters Button -->
                <div class="mt-6 text-center">
                    <button type="button"
                            id="applyFilters"
                            class="inline-flex items-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Apply Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Filter Indicator -->
        <?php if ($hasFilters): ?>
        <section class="py-8 bg-gradient-to-r from-redolence-green/5 to-redolence-blue/5 border-b border-gray-200 mb-12 rounded-2xl">
            <div class="max-w-7xl mx-auto px-6">
                <div class="filter-card rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gradient-to-r from-redolence-green to-redolence-blue rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">
                                    <?php if ($filterType === 'featured'): ?>
                                        Showing: <span class="gradient-text">⭐ Featured Treatments</span>
                                    <?php elseif ($filterType === 'popular'): ?>
                                        Showing: <span class="gradient-text">🔥 Popular Treatments</span>
                                    <?php elseif ($filterType === 'new'): ?>
                                        Showing: <span class="gradient-text">✨ New Treatments</span>
                                    <?php elseif ($searchQuery): ?>
                                        Search Results for: <span class="gradient-text">"<?= htmlspecialchars($searchQuery) ?>"</span>
                                    <?php else: ?>
                                        Filtered Results
                                    <?php endif; ?>
                                </h3>
                                <p class="text-gray-600 mt-2"><?= count($displayServices) ?> treatments found</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <a href="<?= getBasePath() ?>/services.php"
                               class="inline-flex items-center bg-white hover:bg-gray-50 text-redolence-blue border border-redolence-blue px-4 py-2 rounded-lg font-medium transition-all duration-300">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Clear Filters
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Treatments Display -->
        <?php if (empty($displayServices)): ?>
            <!-- No Treatments Available -->
            <div class="text-center py-20">
                <div class="w-32 h-32 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-8 shadow-lg">
                    <svg class="w-16 h-16 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Treatments Coming Soon</h2>
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                    We're currently updating our medical aesthetics treatments catalog. Please check back soon for our comprehensive range of professional services!
                </p>
                <a href="<?= getBasePath() ?>/contact"
                   class="inline-flex items-center bg-redolence-green hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                    Contact Our Specialists
                </a>
            </div>
        <?php else: ?>
            <!-- Display Medical Treatments -->
            <div class="mb-16">
                <!-- Medical Treatments Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($displayServices as $service): ?>
                        <?php include __DIR__ . '/includes/medical_service_card.php'; ?>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if (!$showAll && $totalPages > 1): ?>
            <div class="flex justify-center items-center space-x-4 mt-12">
                <?php if ($page > 1): ?>
                    <a href="<?= buildPaginationUrl($page - 1, $searchQuery, $filterType, $minPrice, $maxPrice, $maxDuration) ?>"
                       class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Previous
                    </a>
                <?php endif; ?>

                <div class="flex space-x-2">
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <a href="<?= buildPaginationUrl($i, $searchQuery, $filterType, $minPrice, $maxPrice, $maxDuration) ?>"
                           class="px-4 py-2 rounded-lg font-medium transition-colors <?= $i === $page ? 'bg-redolence-green text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300' ?>">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>
                </div>

                <?php if ($page < $totalPages): ?>
                    <a href="<?= buildPaginationUrl($page + 1, $searchQuery, $filterType, $minPrice, $maxPrice, $maxDuration) ?>"
                       class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                        Next
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</section>

<!-- Service Details Modal -->
<div id="serviceDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center rounded-t-2xl">
            <h2 id="modalServiceName" class="text-2xl font-bold text-gray-900"></h2>
            <button onclick="closeServiceModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <!-- Treatment Badges -->
            <div id="modalBadges" class="flex flex-wrap gap-2 mb-4"></div>

            <!-- Service Image -->
            <div id="modalImageContainer" class="mb-6"></div>

            <!-- Technology Used -->
            <div id="modalTechnology" class="mb-4"></div>

            <!-- Price and Duration -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="text-sm text-gray-500 mb-1">Price</div>
                    <div id="modalPrice" class="text-2xl font-bold text-redolence-green"></div>
                </div>
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="text-sm text-gray-500 mb-1">Duration</div>
                    <div id="modalDuration" class="text-xl font-semibold text-gray-900"></div>
                </div>
            </div>

            <!-- Session Frequency -->
            <div id="modalFrequency" class="mb-6"></div>

            <!-- Full Description -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Treatment Details</h3>
                <div id="modalDescription" class="prose prose-redolence max-w-none"></div>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-4 pt-4 border-t border-gray-200">
                <a id="modalBookButton" href="#" class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                    Book Appointment
                </a>
                <button onclick="closeServiceModal()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold transition-all duration-300">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced Filter and Search Functionality
class TreatmentFilter {
    constructor() {
        this.allTreatments = <?= json_encode($allTreatments) ?>;
        this.filteredTreatments = [...this.allTreatments];
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updatePriceSlider();
        this.showActiveFilters();
    }

    bindEvents() {
        // Search functionality
        const searchInput = document.getElementById('treatmentSearch');
        const clearSearch = document.getElementById('clearSearch');

        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.handleSearch(e.target.value);
            }, 300);
        });

        searchInput.addEventListener('focus', () => {
            this.showSearchSuggestions();
        });

        document.addEventListener('click', (e) => {
            if (!e.target.closest('#treatmentSearch') && !e.target.closest('#searchSuggestions')) {
                this.hideSearchSuggestions();
            }
        });

        clearSearch.addEventListener('click', () => {
            searchInput.value = '';
            this.handleSearch('');
            clearSearch.classList.add('hidden');
        });

        // Filter controls
        document.querySelectorAll('input[name="filterType"]').forEach(radio => {
            radio.addEventListener('change', () => this.applyFilters());
        });

        document.querySelectorAll('.duration-filter').forEach(checkbox => {
            checkbox.addEventListener('change', () => this.applyFilters());
        });

        document.getElementById('technologyFilter').addEventListener('change', () => this.applyFilters());
        document.getElementById('minPrice').addEventListener('input', () => this.applyFilters());
        document.getElementById('maxPrice').addEventListener('input', () => this.applyFilters());
        document.getElementById('priceRange').addEventListener('input', (e) => {
            document.getElementById('maxPrice').value = e.target.value;
            this.updatePriceSlider();
            this.applyFilters();
        });

        document.getElementById('applyFilters').addEventListener('click', () => this.applyFilters());
        document.getElementById('clearAllFilters').addEventListener('click', () => this.clearAllFilters());
    }

    handleSearch(query) {
        const clearBtn = document.getElementById('clearSearch');
        if (query.length > 0) {
            clearBtn.classList.remove('hidden');
            this.showSearchSuggestions(query);
        } else {
            clearBtn.classList.add('hidden');
            this.hideSearchSuggestions();
        }
        this.applyFilters();
    }

    showSearchSuggestions(query = '') {
        const suggestionsContainer = document.getElementById('searchSuggestions');

        if (query.length < 2) {
            this.hideSearchSuggestions();
            return;
        }

        const suggestions = this.allTreatments
            .filter(treatment =>
                treatment.name.toLowerCase().includes(query.toLowerCase()) ||
                (treatment.description && treatment.description.toLowerCase().includes(query.toLowerCase()))
            )
            .slice(0, 5)
            .map(treatment => ({
                name: treatment.name,
                highlight: this.highlightMatch(treatment.name, query)
            }));

        if (suggestions.length > 0) {
            suggestionsContainer.innerHTML = suggestions
                .map(suggestion => `
                    <div class="suggestion-item" data-name="${suggestion.name}">
                        ${suggestion.highlight}
                    </div>
                `).join('');

            suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => {
                    document.getElementById('treatmentSearch').value = item.dataset.name;
                    this.handleSearch(item.dataset.name);
                    this.hideSearchSuggestions();
                });
            });

            suggestionsContainer.classList.remove('hidden');
        } else {
            this.hideSearchSuggestions();
        }
    }

    hideSearchSuggestions() {
        document.getElementById('searchSuggestions').classList.add('hidden');
    }

    highlightMatch(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="suggestion-highlight">$1</span>');
    }

    applyFilters() {
        this.showLoading();

        setTimeout(() => {
            const searchQuery = document.getElementById('treatmentSearch').value.toLowerCase();
            const filterType = document.querySelector('input[name="filterType"]:checked')?.value || '';
            const minPrice = parseFloat(document.getElementById('minPrice').value) || 0;
            const maxPrice = parseFloat(document.getElementById('maxPrice').value) || Infinity;
            const selectedDurations = Array.from(document.querySelectorAll('.duration-filter:checked')).map(cb => parseInt(cb.value));
            const selectedTechnology = document.getElementById('technologyFilter').value;

            this.filteredTreatments = this.allTreatments.filter(treatment => {
                // Search filter
                if (searchQuery && !treatment.name.toLowerCase().includes(searchQuery) &&
                    !(treatment.description && treatment.description.toLowerCase().includes(searchQuery))) {
                    return false;
                }

                // Type filter
                if (filterType === 'featured' && !treatment.featured) return false;
                if (filterType === 'popular' && !treatment.popular) return false;
                if (filterType === 'new' && !treatment.new_treatment) return false;

                // Price filter
                if (treatment.price && (treatment.price < minPrice || treatment.price > maxPrice)) return false;

                // Duration filter
                if (selectedDurations.length > 0) {
                    if (!treatment.duration) return false;
                    const matchesDuration = selectedDurations.some(duration => {
                        if (duration === 90) return treatment.duration >= 90;
                        return Math.abs(treatment.duration - duration) <= 15;
                    });
                    if (!matchesDuration) return false;
                }

                // Technology filter
                if (selectedTechnology && treatment.technology_used !== selectedTechnology) return false;

                return true;
            });

            this.updateDisplay();
            this.showActiveFilters();
            this.hideLoading();
        }, 500);
    }

    updateDisplay() {
        const treatmentsGrid = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
        const resultsCounter = document.getElementById('resultsCounter');

        resultsCounter.textContent = `${this.filteredTreatments.length} treatments found`;

        if (this.filteredTreatments.length === 0) {
            treatmentsGrid.innerHTML = `
                <div class="col-span-full text-center py-20">
                    <div class="w-32 h-32 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-8 shadow-lg">
                        <svg class="w-16 h-16 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V4a2 2 0 00-2-2h-2a2 2 0 00-2 2v2.306z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">No treatments found</h3>
                    <p class="text-gray-600 mb-6">Try adjusting your filters or search terms to find more treatments.</p>
                    <button onclick="treatmentFilter.clearAllFilters()" class="bg-redolence-green hover:bg-green-600 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300">
                        Clear All Filters
                    </button>
                </div>
            `;
        } else {
            // Re-render treatment cards (simplified version)
            treatmentsGrid.innerHTML = this.filteredTreatments.map(service => this.renderTreatmentCard(service)).join('');
        }

        // Add fade-in animation
        treatmentsGrid.classList.add('filter-fade-in');
        setTimeout(() => treatmentsGrid.classList.remove('filter-fade-in'), 300);
    }

    renderTreatmentCard(service) {
        const decodedName = this.decodeHtml(service.name);
        const decodedDescription = this.decodeHtml(service.description || '');
        const shortDescription = this.stripTags(decodedDescription).substring(0, 120) + '...';

        // Prepare image source with enhanced fallbacks
        let imageSrc = '';
        if (service.image && service.image.trim() !== '') {
            if (service.image.startsWith('http')) {
                imageSrc = service.image;
            } else {
                imageSrc = `<?= getBasePath() ?>/uploads/${service.image}`;
            }
        } else {
            // Use treatment-specific placeholder images
            const placeholderImages = [
                'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
                'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
                'https://images.unsplash.com/photo-1559599101-f09722fb4948?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
                'https://images.unsplash.com/photo-**********-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
                'https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp'
            ];
            // Use service ID to consistently assign the same image to the same service
            const imageIndex = (service.id || 0) % placeholderImages.length;
            imageSrc = placeholderImages[imageIndex];
        }

        return `
            <div class="medical-service-card group rounded-3xl p-6 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 cursor-pointer bg-white border border-gray-100" onclick="openServiceModal('${service.id}')">
                ${service.featured || service.popular || service.new_treatment ? `
                    <div class="flex flex-wrap gap-2 mb-4">
                        ${service.featured ? '<span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold">⭐ Featured</span>' : ''}
                        ${service.popular ? '<span class="bg-gradient-to-r from-red-400 to-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">🔥 Popular</span>' : ''}
                        ${service.new_treatment ? '<span class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">✨ New</span>' : ''}
                    </div>
                ` : ''}

                <!-- Service Image -->
                <div class="relative mb-6 overflow-hidden rounded-2xl">
                    <img src="${imageSrc}"
                         alt="${decodedName}"
                         class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                         loading="lazy"
                         onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp'">

                    <!-- Overlay with quick info -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="flex justify-between items-end text-white">
                                <div>
                                    <div class="text-sm font-semibold">${service.duration ? service.duration + ' mins' : 'Variable'}</div>
                                    ${service.session_frequency ? `<div class="text-xs opacity-90">${service.session_frequency}</div>` : ''}
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold">${service.price ? new Intl.NumberFormat('en-TZ', { style: 'currency', currency: 'TZS', minimumFractionDigits: 0 }).format(service.price) : 'TSH'}</div>
                                    ${service.price ? '<div class="text-xs opacity-90">Starting from</div>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-redolence-green transition-colors duration-300">
                    ${decodedName}
                </h3>

                ${service.technology_used ? `
                    <div class="mb-3">
                        <div class="inline-flex items-center bg-redolence-blue/10 text-redolence-blue px-3 py-1 rounded-full text-xs font-semibold">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                            </svg>
                            ${service.technology_used}
                        </div>
                    </div>
                ` : ''}

                <div class="mb-4">
                    ${service.price ? `
                        <div class="text-2xl font-bold text-redolence-green">
                            ${new Intl.NumberFormat('en-TZ', { style: 'currency', currency: 'TZS', minimumFractionDigits: 0 }).format(service.price)}
                        </div>
                    ` : `
                        <div class="text-lg font-semibold text-redolence-blue">
                            TSH (To be discussed)
                        </div>
                    `}
                </div>

                <!-- Technology Used -->
                ${service.technology_used ? `
                    <div class="flex items-center text-sm text-redolence-blue bg-redolence-blue/10 px-3 py-2 rounded-lg mb-4">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                        ${service.technology_used}
                    </div>
                ` : ''}

                <!-- Service Description -->
                ${decodedDescription ? `
                    <div class="text-gray-600 leading-relaxed treatment-description mb-4">
                        ${shortDescription}
                    </div>
                ` : ''}

                <!-- Treatment Details -->
                <div class="grid grid-cols-2 gap-4 py-4 border-t border-gray-100 mb-4">
                    <div class="text-center">
                        <div class="text-sm text-gray-500">Duration</div>
                        <div class="font-semibold text-gray-900">${service.duration ? service.duration + ' mins' : 'Variable'}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-sm text-gray-500">Price</div>
                        <div class="font-semibold text-gray-900">${service.price ? new Intl.NumberFormat('en-TZ', { style: 'currency', currency: 'TZS', minimumFractionDigits: 0 }).format(service.price) : 'TSH'}</div>
                    </div>
                </div>

                <!-- Session Frequency -->
                ${service.session_frequency ? `
                    <div class="bg-gray-50 rounded-lg p-3 mb-4">
                        <div class="text-xs text-gray-500 mb-1">Recommended Frequency</div>
                        <div class="text-sm font-medium text-gray-900">${service.session_frequency}</div>
                    </div>
                ` : ''}

                <!-- Action Buttons -->
                <div class="flex gap-3 pt-4">
                    <button onclick="event.stopPropagation(); openServiceModal('${service.id}')"
                            class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        Learn More
                    </button>
                    <a href="<?= getBasePath() ?>/customer/book?service=${service.id}"
                       onclick="event.stopPropagation()"
                       class="bg-white hover:bg-redolence-blue text-redolence-blue hover:text-white border-2 border-redolence-blue py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        Book Now
                    </a>
                </div>
            </div>
        `;
    }

    showActiveFilters() {
        // Implementation for showing active filter tags
        const clearAllBtn = document.getElementById('clearAllFilters');
        const hasActiveFilters = this.hasActiveFilters();

        if (hasActiveFilters) {
            clearAllBtn.classList.remove('hidden');
        } else {
            clearAllBtn.classList.add('hidden');
        }
    }

    hasActiveFilters() {
        const searchQuery = document.getElementById('treatmentSearch').value;
        const filterType = document.querySelector('input[name="filterType"]:checked')?.value;
        const minPrice = document.getElementById('minPrice').value;
        const maxPrice = document.getElementById('maxPrice').value;
        const selectedDurations = document.querySelectorAll('.duration-filter:checked').length;
        const selectedTechnology = document.getElementById('technologyFilter').value;

        return searchQuery || filterType || minPrice || maxPrice || selectedDurations > 0 || selectedTechnology;
    }

    clearAllFilters() {
        document.getElementById('treatmentSearch').value = '';
        document.getElementById('clearSearch').classList.add('hidden');
        document.querySelector('input[name="filterType"][value=""]').checked = true;
        document.getElementById('minPrice').value = '';
        document.getElementById('maxPrice').value = '';
        document.getElementById('priceRange').value = '1000000';
        document.querySelectorAll('.duration-filter').forEach(cb => cb.checked = false);
        document.getElementById('technologyFilter').value = '';

        this.updatePriceSlider();
        this.applyFilters();
        this.hideSearchSuggestions();
    }

    updatePriceSlider() {
        const slider = document.getElementById('priceRange');
        const value = (slider.value - slider.min) / (slider.max - slider.min) * 100;
        slider.style.setProperty('--value', value + '%');
    }

    showLoading() {
        const treatmentsGrid = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
        if (!treatmentsGrid.querySelector('.loading-overlay')) {
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay';
            loadingOverlay.innerHTML = '<div class="loading-spinner"></div>';
            treatmentsGrid.style.position = 'relative';
            treatmentsGrid.appendChild(loadingOverlay);
        }
    }

    hideLoading() {
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }

    decodeHtml(html) {
        const txt = document.createElement('textarea');
        txt.innerHTML = html;
        return txt.value;
    }

    stripTags(html) {
        const tmp = document.createElement('div');
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || '';
    }
}

// Initialize the filter system
let treatmentFilter;
document.addEventListener('DOMContentLoaded', function() {
    treatmentFilter = new TreatmentFilter();
});

function openServiceModal(serviceId) {
    // Fetch service details
    fetch(`<?= getBasePath() ?>/api/services/get.php?id=${serviceId}`)
        .then(response => response.json())
        .then(service => {
            if (service.error) {
                alert('Error loading service details');
                return;
            }

            // Populate modal content
            document.getElementById('modalServiceName').textContent = service.name;

            // Treatment badges
            const badgesContainer = document.getElementById('modalBadges');
            badgesContainer.innerHTML = '';
            if (service.featured) {
                badgesContainer.innerHTML += '<span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold">⭐ Featured</span>';
            }
            if (service.popular) {
                badgesContainer.innerHTML += '<span class="bg-gradient-to-r from-red-400 to-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">🔥 Popular</span>';
            }
            if (service.new_treatment) {
                badgesContainer.innerHTML += '<span class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">✨ New</span>';
            }

            // Service image
            const imageContainer = document.getElementById('modalImageContainer');
            if (service.image) {
                const imageSrc = service.image.startsWith('http') ? service.image : `<?= getBasePath() ?>/uploads/${service.image}`;
                imageContainer.innerHTML = `<img src="${imageSrc}" alt="${service.name}" class="w-full h-64 object-cover rounded-xl">`;
            } else {
                imageContainer.innerHTML = '';
            }

            // Technology
            const technologyContainer = document.getElementById('modalTechnology');
            if (service.technology_used) {
                technologyContainer.innerHTML = `
                    <div class="inline-flex items-center bg-redolence-blue/10 text-redolence-blue px-4 py-2 rounded-lg">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                        ${service.technology_used}
                    </div>
                `;
            } else {
                technologyContainer.innerHTML = '';
            }

            // Price
            const priceContainer = document.getElementById('modalPrice');
            if (service.price) {
                priceContainer.textContent = new Intl.NumberFormat('en-TZ', {
                    style: 'currency',
                    currency: 'TZS',
                    minimumFractionDigits: 0
                }).format(service.price);
            } else {
                priceContainer.innerHTML = '<span class="text-redolence-blue">TSH (To be discussed)</span>';
            }

            // Duration
            const durationContainer = document.getElementById('modalDuration');
            if (service.duration) {
                durationContainer.textContent = `${service.duration} minutes`;
            } else {
                durationContainer.innerHTML = '<span class="text-gray-500">Variable duration</span>';
            }

            // Session frequency
            const frequencyContainer = document.getElementById('modalFrequency');
            if (service.session_frequency) {
                frequencyContainer.innerHTML = `
                    <div class="bg-blue-50 rounded-xl p-4">
                        <div class="text-sm text-blue-600 font-semibold mb-1">Recommended Frequency</div>
                        <div class="text-blue-900">${service.session_frequency}</div>
                    </div>
                `;
            } else {
                frequencyContainer.innerHTML = '';
            }

            // Description (display HTML properly)
            const descriptionContainer = document.getElementById('modalDescription');
            if (service.description) {
                descriptionContainer.innerHTML = service.description;
            } else {
                descriptionContainer.innerHTML = '<p class="text-gray-500">No detailed description available.</p>';
            }

            // Book button
            document.getElementById('modalBookButton').href = `<?= getBasePath() ?>/customer/book?service=${service.id}`;

            // Show modal
            document.getElementById('serviceDetailsModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading service details');
        });
}

function closeServiceModal() {
    document.getElementById('serviceDetailsModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('serviceDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeServiceModal();
    }
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
