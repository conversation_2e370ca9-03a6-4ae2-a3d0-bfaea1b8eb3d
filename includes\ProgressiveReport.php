<?php
/**
 * Progressive Report Class
 * Handles progressive treatment reports for medical aesthetics clients
 */
class ProgressiveReport {
    private $db;

    public function __construct() {
        global $database;
        $this->db = $database;
    }
    
    /**
     * Generate UUID for new records
     */
    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    /**
     * Create a new progressive report
     */
    public function create($clientId, $title = null, $description = null, $createdBy = null) {
        try {
            $id = $this->generateUUID();
            $title = $title ?: 'Progressive Treatment Report';
            $createdBy = $createdBy ?: $_SESSION['user_id'] ?? null;
            
            if (!$createdBy) {
                throw new Exception('Created by user ID is required');
            }
            
            $result = $this->db->execute("
                INSERT INTO progressive_reports (id, client_id, title, description, created_by)
                VALUES (?, ?, ?, ?, ?)
            ", [$id, $clientId, $title, $description, $createdBy]);

            if ($result) {
                return $this->getById($id);
            }

            return false;
        } catch (Exception $e) {
            error_log("Progressive Report creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get progressive report by ID
     */
    public function getById($id) {
        try {
            // Use direct table query instead of view
            $report = $this->db->fetch("
                SELECT pr.*, u.name as client_name, u.email as client_email, u.phone as client_phone,
                       creator.name as created_by_name, creator.role as created_by_role,
                       COUNT(pre.id) as total_entries, MAX(pre.entry_date) as last_entry_date
                FROM progressive_reports pr
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pr.created_by = creator.id
                LEFT JOIN progressive_report_entries pre ON pr.id = pre.report_id
                WHERE pr.id = ?
                GROUP BY pr.id
            ", [$id]);

            return $report;
        } catch (Exception $e) {
            error_log("Error fetching progressive report: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get progressive report by client ID
     */
    public function getByClientId($clientId) {
        try {
            // Use direct table query instead of view
            return $this->db->fetch("
                SELECT pr.*, u.name as client_name, u.email as client_email, u.phone as client_phone,
                       creator.name as created_by_name, creator.role as created_by_role,
                       COUNT(pre.id) as total_entries, MAX(pre.entry_date) as last_entry_date
                FROM progressive_reports pr
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pr.created_by = creator.id
                LEFT JOIN progressive_report_entries pre ON pr.id = pre.report_id
                WHERE pr.client_id = ?
                GROUP BY pr.id
                ORDER BY pr.created_at DESC
            ", [$clientId]);
        } catch (Exception $e) {
            error_log("Error fetching progressive report by client: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all progressive reports with pagination
     */
    public function getAll($page = 1, $limit = 20, $search = '', $status = 'all') {
        try {
            $offset = ($page - 1) * $limit;
            $conditions = [];
            $params = [];
            
            if (!empty($search)) {
                $conditions[] = "(u.name LIKE ? OR u.email LIKE ? OR pr.title LIKE ?)";
                $searchTerm = "%$search%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if ($status !== 'all') {
                $conditions[] = "status = ?";
                $params[] = $status;
            }
            
            $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
            
            $params[] = $limit;
            $params[] = $offset;

            $reports = $this->db->fetchAll("
                SELECT pr.*, u.name as client_name, u.email as client_email, u.phone as client_phone,
                       creator.name as created_by_name, creator.role as created_by_role,
                       COUNT(pre.id) as total_entries, MAX(pre.entry_date) as last_entry_date
                FROM progressive_reports pr
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pr.created_by = creator.id
                LEFT JOIN progressive_report_entries pre ON pr.id = pre.report_id
                $whereClause
                GROUP BY pr.id
                ORDER BY pr.updated_at DESC
                LIMIT ? OFFSET ?
            ", $params);

            // Get total count
            $countParams = array_slice($params, 0, -2); // Remove limit and offset
            $totalResult = $this->db->fetch("
                SELECT COUNT(DISTINCT pr.id) as total
                FROM progressive_reports pr
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pr.created_by = creator.id
                $whereClause
            ", $countParams);
            $total = $totalResult ? $totalResult['total'] : 0;
            
            return [
                'reports' => $reports,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($total / $limit)
            ];
        } catch (Exception $e) {
            error_log("Error fetching progressive reports: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update progressive report
     */
    public function update($id, $data) {
        try {
            $allowedFields = ['title', 'description', 'status'];
            $updateFields = [];
            $params = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return false;
            }
            
            $params[] = $id;
            
            return $this->db->execute("
                UPDATE progressive_reports
                SET " . implode(', ', $updateFields) . "
                WHERE id = ?
            ", $params);
        } catch (Exception $e) {
            error_log("Error updating progressive report: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete progressive report
     */
    public function delete($id) {
        try {
            return $this->db->execute("DELETE FROM progressive_reports WHERE id = ?", [$id]);
        } catch (Exception $e) {
            error_log("Error deleting progressive report: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get report entries
     */
    public function getEntries($reportId, $orderBy = 'entry_date DESC') {
        try {
            return $this->db->fetchAll("
                SELECT pre.*, pr.client_id, pr.title as report_title, u.name as client_name,
                       creator.name as created_by_name, creator.role as created_by_role,
                       b.date as appointment_date, b.start_time as appointment_start_time, b.status as appointment_status,
                       s.name as service_name, staff.name as staff_name
                FROM progressive_report_entries pre
                LEFT JOIN progressive_reports pr ON pre.report_id = pr.id
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pre.created_by = creator.id
                LEFT JOIN bookings b ON pre.appointment_id = b.id
                LEFT JOIN services s ON b.service_id = s.id
                LEFT JOIN users staff ON b.staff_id = staff.id
                WHERE pre.report_id = ?
                ORDER BY $orderBy
            ", [$reportId]);
        } catch (Exception $e) {
            error_log("Error fetching report entries: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if user can access report
     */
    public function canAccess($reportId, $userId, $userRole) {
        if ($userRole === 'ADMIN') {
            return true;
        }
        
        if ($userRole === 'STAFF') {
            // Staff can access reports for their appointments
            $result = $this->db->fetch("
                SELECT COUNT(*) as count
                FROM progressive_reports pr
                JOIN progressive_report_entries pre ON pr.id = pre.report_id
                JOIN bookings b ON pre.appointment_id = b.id
                WHERE pr.id = ? AND b.staff_id = ?
            ", [$reportId, $userId]);
            return $result && $result['count'] > 0;
        }
        
        return false;
    }
    
    /**
     * Get reports accessible by staff member
     */
    public function getByStaffId($staffId, $page = 1, $limit = 20) {
        try {
            $offset = ($page - 1) * $limit;
            
            return $this->db->fetchAll("
                SELECT DISTINCT pr.*, u.name as client_name, u.email as client_email, u.phone as client_phone,
                       creator.name as created_by_name, creator.role as created_by_role,
                       COUNT(pre.id) as total_entries, MAX(pre.entry_date) as last_entry_date
                FROM progressive_reports pr
                LEFT JOIN users u ON pr.client_id = u.id
                LEFT JOIN users creator ON pr.created_by = creator.id
                LEFT JOIN progressive_report_entries pre ON pr.id = pre.report_id
                JOIN bookings b ON pre.appointment_id = b.id
                WHERE b.staff_id = ?
                GROUP BY pr.id
                ORDER BY pr.updated_at DESC
                LIMIT ? OFFSET ?
            ", [$staffId, $limit, $offset]);
        } catch (Exception $e) {
            error_log("Error fetching staff progressive reports: " . $e->getMessage());
            return false;
        }
    }
}
