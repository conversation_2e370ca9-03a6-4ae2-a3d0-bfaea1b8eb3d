<?php
session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_status':
                    $appointmentId = $_POST['appointment_id'];
                    $status = $_POST['status'];
                    $notes = $_POST['notes'] ?? null;

                    updateAppointmentStatus($appointmentId, $_SESSION['user_id'], $status, $notes);
                    $message = 'Medical appointment status updated successfully!';
                    $messageType = 'success';
                    break;

                case 'update_notes':
                    $appointmentId = $_POST['appointment_id'];
                    $notes = $_POST['notes'] ?? '';

                    // Update notes in database
                    $database->query("
                        UPDATE bookings
                        SET notes = ?, updated_at = NOW()
                        WHERE id = ? AND staff_id = ?
                    ", [$notes, $appointmentId, $_SESSION['user_id']]);

                    $message = 'Medical notes updated successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get filters
$dateFilter = $_GET['date'] ?? '';  // Empty by default to show all dates
$statusFilter = $_GET['status'] ?? 'all';

// Get staff appointments
$staffId = $_SESSION['user_id'];
$profile = getStaffProfile($staffId);

// Get appointments with filters
global $database;
$conditions = ["b.staff_id = ?"];
$params = [$staffId];

// Only apply date filter if specifically provided
if ($dateFilter && $dateFilter !== '') {
    $conditions[] = "DATE(b.date) = ?";
    $params[] = $dateFilter;
}

if ($statusFilter !== 'all') {
    $conditions[] = "b.status = ?";
    $params[] = $statusFilter;
}

$whereClause = 'WHERE ' . implode(' AND ', $conditions);

$appointments = $database->fetchAll("
    SELECT
        b.*,
        u.name as customer_name,
        u.phone as customer_phone,
        u.email as customer_email,
        u.points as customer_points,
        s.name as service_name,
        s.duration as service_duration,
        s.price as service_price,
        CASE
            WHEN b.date >= CURDATE() THEN 0
            ELSE 1
        END as is_past
    FROM bookings b
    LEFT JOIN users u ON b.user_id = u.id
    LEFT JOIN services s ON b.service_id = s.id
    $whereClause
    ORDER BY is_past ASC, b.date ASC, b.start_time ASC
", $params);

$pageTitle = "Patient Appointments Management";
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Message Display -->
<?php if ($message): ?>
    <div class="medical-card mb-8 p-6 <?= $messageType === 'success' ? 'border-redolence-green/30 bg-gradient-to-r from-redolence-green/10 to-redolence-green/5' : 'border-red-500/30 bg-gradient-to-r from-red-500/10 to-red-500/5' ?>">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 <?= $messageType === 'success' ? 'text-redolence-green' : 'text-red-500' ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <?php if ($messageType === 'success'): ?>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    <?php else: ?>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    <?php endif; ?>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium <?= $messageType === 'success' ? 'text-redolence-green' : 'text-red-600' ?>">
                    <?= htmlspecialchars($message) ?>
                </p>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Medical Appointments Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-redolence-navy">Patient Appointments <span class="text-redolence-green">Management</span></h1>
            <p class="mt-2 text-lg text-gray-600">View and manage your assigned medical consultations and treatments</p>
            <div class="mt-3 flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                </svg>
                Professional Medical Appointment Management
            </div>
        </div>
        <div class="mt-6 sm:mt-0">
            <div class="flex items-center space-x-4">
                <div class="flex items-center text-sm text-gray-600">
                    <div class="w-2 h-2 bg-redolence-green rounded-full mr-2 animate-pulse"></div>
                    Medical System Active
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (!empty($message)): ?>
    <div class="medical-card mb-6 p-6 <?= $messageType === 'success' ? 'bg-green-50 border-l-4 border-green-400' : 'bg-red-50 border-l-4 border-red-400' ?>">
        <div class="flex items-center">
            <?php if ($messageType === 'success'): ?>
                <svg class="w-6 h-6 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="text-green-800 font-medium"><?= htmlspecialchars($message) ?></p>
            <?php else: ?>
                <svg class="w-6 h-6 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="text-red-800 font-medium"><?= htmlspecialchars($message) ?></p>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<!-- Medical Quick Filters -->
<div class="medical-card p-6 mb-6">
    <div class="flex flex-wrap gap-3">
        <span class="text-sm font-semibold text-redolence-navy mr-4 flex items-center">Medical Filters:</span>
        <button type="button" onclick="clearFilters()" class="medical-btn-primary px-4 py-2 text-sm">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
            </svg>
            All Patients
        </button>
        <button type="button" onclick="setDateFilter(0)" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors">
            Today's Patients
        </button>
        <button type="button" onclick="setDateFilter(1)" class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white text-sm rounded-xl transition-colors">
            Tomorrow's Schedule
        </button>
        <button type="button" onclick="setWeekFilter()" class="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white text-sm rounded-xl transition-colors">
            This Week
        </button>
        <button type="button" onclick="setStatusFilter('PENDING')" class="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white text-sm rounded-xl transition-colors">
            Pending
        </button>
        <button type="button" onclick="setStatusFilter('CONFIRMED')" class="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white text-sm rounded-xl transition-colors">
            Confirmed
        </button>
    </div>
</div>

<!-- Medical Advanced Filters -->
<div class="medical-card p-6 mb-8">
    <h3 class="text-xl font-bold text-redolence-navy mb-6">Advanced Medical Filters</h3>
    <form method="GET" class="flex flex-col sm:flex-row gap-6">
        <div class="flex-1">
            <label for="date" class="block text-sm font-semibold text-redolence-navy mb-3">Treatment Date (Optional)</label>
            <input type="date" id="date" name="date" value="<?= htmlspecialchars($dateFilter) ?>"
                   placeholder="Select date to filter..."
                   class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
        </div>
        <div>
            <label for="status" class="block text-sm font-semibold text-redolence-navy mb-3">Medical Status</label>
            <select id="status" name="status"
                    class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                <option value="all" <?= $statusFilter === 'all' ? 'selected' : '' ?>>All Medical Status</option>
                <option value="PENDING" <?= $statusFilter === 'PENDING' ? 'selected' : '' ?>>Pending Consultation</option>
                <option value="CONFIRMED" <?= $statusFilter === 'CONFIRMED' ? 'selected' : '' ?>>Confirmed Treatment</option>
                <option value="IN_PROGRESS" <?= $statusFilter === 'IN_PROGRESS' ? 'selected' : '' ?>>Treatment In Progress</option>
                <option value="COMPLETED" <?= $statusFilter === 'COMPLETED' ? 'selected' : '' ?>>Treatment Completed</option>
                <option value="CANCELLED" <?= $statusFilter === 'CANCELLED' ? 'selected' : '' ?>>Cancelled</option>
                <option value="NO_SHOW" <?= $statusFilter === 'NO_SHOW' ? 'selected' : '' ?>>Patient No Show</option>
            </select>
        </div>
        <div class="flex items-end gap-3">
            <button type="submit" class="medical-btn-primary px-6 py-3">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Filter Patients
            </button>
            <button type="button" onclick="clearFilters()" class="medical-btn-secondary px-6 py-3">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Clear Filters
            </button>
        </div>
    </form>
</div>

<!-- Medical Filter Summary -->
<div class="medical-card p-6 mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center space-x-4">
            <span class="text-redolence-navy font-semibold">
                Showing <?= count($appointments) ?> patient appointment<?= count($appointments) !== 1 ? 's' : '' ?>
            </span>
            <?php if ($dateFilter): ?>
                <span class="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full border border-blue-200">
                    Date: <?= date('M j, Y', strtotime($dateFilter)) ?>
                </span>
            <?php else: ?>
                <span class="text-sm bg-redolence-green/10 text-redolence-green px-3 py-1 rounded-full border border-redolence-green/20">
                    All Treatment Dates
                </span>
            <?php endif; ?>
            <?php if ($statusFilter !== 'all'): ?>
                <span class="text-sm bg-purple-100 text-purple-800 px-3 py-1 rounded-full border border-purple-200">
                    Status: <?= ucfirst(strtolower($statusFilter)) ?>
                </span>
            <?php endif; ?>
        </div>
        <?php if ($dateFilter || $statusFilter !== 'all'): ?>
            <button onclick="clearFilters()" class="mt-3 sm:mt-0 text-sm text-redolence-green hover:text-redolence-blue transition-colors flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Clear All Medical Filters
            </button>
        <?php endif; ?>
    </div>
</div>

<!-- Medical Appointments List -->
<div class="space-y-6">
    <?php if (empty($appointments)): ?>
        <div class="medical-card p-12 text-center">
            <div class="w-24 h-24 bg-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <h3 class="text-2xl font-bold text-redolence-navy mb-3">No Patient Appointments Found</h3>
            <p class="text-gray-600">No medical appointments match your current filters</p>
        </div>
    <?php else: ?>
        <?php foreach ($appointments as $appointment): ?>
            <div class="medical-card p-8 <?= $appointment['is_past'] ? 'opacity-75 bg-gray-50' : '' ?>">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex-1">
                        <div class="flex items-start gap-6">
                            <div class="w-16 h-16 rounded-full bg-gradient-to-r from-redolence-green to-redolence-blue flex items-center justify-center">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center gap-4 mb-3">
                                    <h3 class="text-xl font-bold text-redolence-navy"><?= htmlspecialchars($appointment['customer_name']) ?></h3>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                        <?php
                                        switch($appointment['status']) {
                                            case 'PENDING': echo 'bg-yellow-100 text-yellow-800 border border-yellow-200'; break;
                                            case 'CONFIRMED': echo 'bg-blue-100 text-blue-800 border border-blue-200'; break;
                                            case 'IN_PROGRESS': echo 'bg-purple-100 text-purple-800 border border-purple-200'; break;
                                            case 'COMPLETED': echo 'bg-green-100 text-green-800 border border-green-200'; break;
                                            case 'CANCELLED': echo 'bg-red-100 text-red-800 border border-red-200'; break;
                                            case 'NO_SHOW': echo 'bg-gray-100 text-gray-800 border border-gray-200'; break;
                                            default: echo 'bg-gray-100 text-gray-800 border border-gray-200';
                                        }
                                        ?>">
                                        <?= ucfirst(strtolower($appointment['status'])) ?>
                                    </span>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-sm mt-4">
                                    <div>
                                        <p class="text-gray-500 font-medium mb-1">Medical Treatment</p>
                                        <p class="text-redolence-navy font-semibold"><?= htmlspecialchars($appointment['service_name']) ?></p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 font-medium mb-1">Appointment Date & Time</p>
                                        <p class="text-redolence-navy font-semibold">
                                            <?= date('M j, Y', strtotime($appointment['date'])) ?>
                                            <?php if ($appointment['is_past']): ?>
                                                <span class="text-xs text-gray-500 ml-1">(Past Treatment)</span>
                                            <?php elseif ($appointment['date'] === date('Y-m-d')): ?>
                                                <span class="text-xs text-redolence-green ml-1 font-bold">(Today)</span>
                                            <?php endif; ?>
                                            <br>
                                            <span class="text-redolence-blue"><?= date('g:i A', strtotime($appointment['start_time'])) ?></span>
                                        </p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 font-medium mb-1">Duration & Cost</p>
                                        <p class="text-redolence-navy font-semibold">
                                            <?= $appointment['service_duration'] ?> minutes<br>
                                            <span class="text-redolence-green">TSH <?= number_format($appointment['total_amount'], 0) ?></span>
                                        </p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 font-medium mb-1">Patient Contact</p>
                                        <p class="text-redolence-navy font-semibold">
                                            <?= htmlspecialchars($appointment['customer_phone']) ?><br>
                                            <span class="text-xs text-gray-600"><?= htmlspecialchars($appointment['customer_email']) ?></span>
                                        </p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 font-medium mb-1">Treatment Status</p>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                            <?php
                                            switch($appointment['status']) {
                                                case 'PENDING':
                                                    echo 'bg-yellow-100 text-yellow-800 border border-yellow-200';
                                                    break;
                                                case 'CONFIRMED':
                                                    echo 'bg-blue-100 text-blue-800 border border-blue-200';
                                                    break;
                                                case 'IN_PROGRESS':
                                                    echo 'bg-purple-100 text-purple-800 border border-purple-200';
                                                    break;
                                                case 'COMPLETED':
                                                    echo 'bg-green-100 text-green-800 border border-green-200';
                                                    break;
                                                case 'CANCELLED':
                                                    echo 'bg-red-100 text-red-800 border border-red-200';
                                                    break;
                                                case 'NO_SHOW':
                                                    echo 'bg-gray-100 text-gray-800 border border-gray-200';
                                                    break;
                                                default:
                                                    echo 'bg-gray-100 text-gray-800 border border-gray-200';
                                            }
                                            ?>">
                                            <?php
                                            switch($appointment['status']) {
                                                case 'PENDING':
                                                    echo 'Pending Consultation';
                                                    break;
                                                case 'CONFIRMED':
                                                    echo 'Treatment Confirmed';
                                                    break;
                                                case 'IN_PROGRESS':
                                                    echo 'Treatment In Progress';
                                                    break;
                                                case 'COMPLETED':
                                                    echo 'Treatment Completed';
                                                    break;
                                                case 'CANCELLED':
                                                    echo 'Cancelled';
                                                    break;
                                                case 'NO_SHOW':
                                                    echo 'Patient No Show';
                                                    break;
                                                default:
                                                    echo ucfirst(strtolower($appointment['status']));
                                            }
                                            ?>
                                        </span>
                                    </div>
                                </div>

                                <?php if ($appointment['notes']): ?>
                                    <div class="mt-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
                                        <p class="text-gray-600 text-sm font-medium mb-1">Medical Notes:</p>
                                        <p class="text-gray-800 text-sm"><?= htmlspecialchars($appointment['notes']) ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 lg:mt-0 lg:ml-8">
                        <div class="flex flex-wrap gap-3">
                            <?php if ($appointment['status'] === 'PENDING'): ?>
                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'CONFIRMED')"
                                        class="medical-btn-primary px-4 py-2 text-sm">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Confirm Treatment
                                </button>
                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'CANCELLED')"
                                        class="px-4 py-2 bg-red-500 hover:bg-red-600 text-white text-sm rounded-xl transition-colors">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Cancel
                                </button>
                            <?php elseif ($appointment['status'] === 'CONFIRMED'): ?>
                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'IN_PROGRESS')"
                                        class="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white text-sm rounded-xl transition-colors">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Begin Treatment
                                </button>
                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'NO_SHOW')"
                                        class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm rounded-xl transition-colors">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                    </svg>
                                    Patient No Show
                                </button>
                            <?php elseif ($appointment['status'] === 'IN_PROGRESS'): ?>
                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'COMPLETED')"
                                        class="medical-btn-primary px-4 py-2 text-sm">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Complete Treatment
                                </button>
                            <?php endif; ?>

                            <button onclick="openNotesModal('<?= $appointment['id'] ?>', '<?= htmlspecialchars($appointment['notes'] ?? '') ?>')"
                                    class="medical-btn-secondary px-4 py-2 text-sm">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Medical Notes
                            </button>

                            <?php if ($appointment['status'] === 'COMPLETED'): ?>
                                <button onclick="addProgressiveReportEntry('<?= $appointment['id'] ?>', '<?= htmlspecialchars($appointment['customer_name']) ?>', '<?= $appointment['user_id'] ?>')"
                                        class="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white text-sm rounded-xl transition-colors">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Add Medical Report
                                </button>
                                <!-- Fallback Direct Link -->
                                <a href="<?= getBasePath() ?>/staff/progressive-reports/create.php?customer_id=<?= $appointment['user_id'] ?>&appointment_id=<?= $appointment['id'] ?>"
                                   class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white text-sm rounded-xl transition-colors inline-flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Create Report
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>
<!-- Medical Notes Modal -->
<div id="notesModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="medical-glass max-w-lg w-full" style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95)); backdrop-filter: blur(20px);">
            <div class="p-8">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-2xl font-bold text-redolence-navy">Medical Notes</h3>
                        <p class="text-gray-600 mt-1">Patient treatment notes and observations</p>
                    </div>
                    <button onclick="closeNotesModal()" class="text-gray-500 hover:text-redolence-green transition-colors p-2 rounded-lg hover:bg-redolence-green/10">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form id="notesForm" method="POST">
                    <input type="hidden" name="action" value="update_notes">
                    <input type="hidden" id="notesAppointmentId" name="appointment_id" value="">

                    <div class="mb-6">
                        <label for="notes" class="block text-sm font-semibold text-redolence-navy mb-3">Medical Treatment Notes</label>
                        <textarea id="notes" name="notes" rows="5"
                                  class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                                  placeholder="Add detailed medical notes about this patient treatment session..."></textarea>
                    </div>

                    <div class="flex items-center justify-end space-x-4">
                        <button type="button" onclick="closeNotesModal()"
                                class="medical-btn-secondary px-6 py-3">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Cancel
                        </button>
                        <button type="submit"
                                class="medical-btn-primary px-6 py-3">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Save Medical Notes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

    <script>
        console.log('JavaScript loaded successfully');

        function updateStatus(appointmentId, status) {
            console.log('updateStatus called with:', appointmentId, status);
            let confirmMessage = 'Are you sure you want to update this appointment status to ' + status + '?';

            if (confirm(confirmMessage)) {
                console.log('User confirmed, submitting form...');
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="appointment_id" value="${appointmentId}">
                    <input type="hidden" name="status" value="${status}">
                `;
                document.body.appendChild(form);
                form.submit();
            } else {
                console.log('User cancelled');
            }
        }

        function openNotesModal(appointmentId, currentNotes) {
            document.getElementById('notesAppointmentId').value = appointmentId;
            document.getElementById('notes').value = currentNotes;
            document.getElementById('notesModal').classList.remove('hidden');
        }

        function addProgressiveReportEntry(appointmentId, customerName, customerId) {
            console.log('addProgressiveReportEntry called with:', appointmentId, customerName, customerId);
            if (confirm(`Create medical report for ${customerName}?`)) {
                console.log('User confirmed, redirecting...');
                window.location.href = `<?= getBasePath() ?>/staff/progressive-reports/create.php?customer_id=${customerId}&appointment_id=${appointmentId}`;
            } else {
                console.log('User cancelled');
            }
        }

        function closeNotesModal() {
            document.getElementById('notesModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('notesModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeNotesModal();
            }
        });

        // Clear all filters function
        function clearFilters() {
            document.getElementById('date').value = '';
            document.getElementById('status').value = 'all';
            // Submit the form to reload with cleared filters
            document.querySelector('form').submit();
        }

        // Quick filter functions
        function setDateFilter(days) {
            const date = new Date();
            date.setDate(date.getDate() + days);
            document.getElementById('date').value = date.toISOString().split('T')[0];
            document.getElementById('status').value = 'all';
            document.querySelector('form').submit();
        }

        function setWeekFilter() {
            // Clear date filter to show all appointments for the week
            // This could be enhanced to show a date range, but for now we'll show all
            document.getElementById('date').value = '';
            document.getElementById('status').value = 'all';
            document.querySelector('form').submit();
        }

        function setStatusFilter(status) {
            document.getElementById('date').value = '';
            document.getElementById('status').value = status;
            document.querySelector('form').submit();
        }
    </script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
