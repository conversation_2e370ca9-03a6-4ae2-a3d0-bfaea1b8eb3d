# Redolence Medi Aesthetics - Database Setup Guide

## Quick Fix for Current Error

The error you're seeing is because the `redolence` database doesn't exist yet. Here are the steps to fix this:

### Option 1: Using phpMyAdmin (Recommended)

1. **Open phpMyAdmin** in your browser: `http://localhost/phpmyadmin`

2. **Create New Database:**
   - Click "New" in the left sidebar
   - Enter database name: `redolence`
   - Choose collation: `utf8mb4_general_ci`
   - Click "Create"

3. **Import Data:**
   - Select the newly created `redolence` database
   - Click "Import" tab
   - Click "Choose File" and select `redolence.sql` from your project folder
   - Click "Go" to import

### Option 2: Using MySQL Command Line

```bash
# 1. Create the database
mysql -u root -p -e "CREATE DATABASE redolence CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;"

# 2. Import the data
mysql -u root -p redolence < redolence.sql
```

### Option 3: Using XAMPP MySQL Console

1. Open XAMPP Control Panel
2. Click "Shell" button
3. Run these commands:
```bash
mysql -u root -p
CREATE DATABASE redolence CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE redolence;
SOURCE C:/xampp/htdocs/redolence/redolence.sql;
EXIT;
```

## Verification Steps

After setting up the database:

1. **Check Database Exists:**
   - In phpMyAdmin, you should see `redolence` in the database list
   - It should contain all the tables from the original system

2. **Test Website:**
   - Visit `http://localhost/redolence/`
   - The error should be resolved

3. **Check Admin Access:**
   - The admin credentials should be the same as the original system
   - Default admin login is usually in the `users` table

## Database Configuration

The system is already configured to use:
- **Database Name:** `redolence`
- **Username:** `root`
- **Password:** (empty for XAMPP default)
- **Host:** `localhost`

These settings are in `config/database.php` and `config/app.php`.

## Troubleshooting

### If you get "Access Denied" errors:
- Make sure XAMPP MySQL service is running
- Check if you need a password for root user
- Try connecting with phpMyAdmin first to verify credentials

### If tables are missing:
- Make sure the `redolence.sql` file imported completely
- Check for any error messages during import
- Verify all tables exist in phpMyAdmin

### If you want to start fresh:
```sql
DROP DATABASE IF EXISTS redolence;
CREATE DATABASE redolence CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE redolence;
-- Then import redolence.sql again
```

## Next Steps After Database Setup

1. ✅ Database created and imported
2. 🔄 Test website functionality
3. 🔄 Update admin credentials if needed
4. 🔄 Continue with remaining rebranding tasks

## Sample Data

The `redolence.sql` file contains:
- Clean database structure
- Sample services and categories
- Test user accounts
- Basic configuration data

All data has been prepared for the Redolence Medi Aesthetics brand.
