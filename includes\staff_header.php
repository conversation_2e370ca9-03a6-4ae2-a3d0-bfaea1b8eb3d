<?php
// Get staff profile for header
$staffProfile = [
    'name' => $_SESSION['user_name'] ?? 'Medical Staff',
    'email' => $_SESSION['user_email'] ?? '',
    'role' => $_SESSION['user_role'] ?? 'STAFF'
];

$basePath = getBasePath();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?? 'Medical Staff Panel' ?> - <?= APP_NAME ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= getBasePath() ?>/includes/flix_logo.png">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'redolence-green': '#49a75c',
                        'redolence-blue': '#5894d2',
                        'redolence-navy': '#1a2332',
                        'redolence-gray': '#f8fafc',
                        'accent-gold': '#f4d03f',
                        'deep-navy': '#1a2332',
                        'soft-gray': '#f8fafc',
                        'medical-white': '#ffffff',
                        primary: {
                            50: '#f0f9f4',
                            100: '#dcf2e3',
                            200: '#bce5ca',
                            300: '#8dd1a7',
                            400: '#5bb67d',
                            500: '#49a75c',
                            600: '#3a8549',
                            700: '#306a3c',
                            800: '#295532',
                            900: '#23462b',
                        },
                        secondary: {
                            50: '#f0f7ff',
                            100: '#e0efff',
                            200: '#b9dfff',
                            300: '#7cc5ff',
                            400: '#36a7ff',
                            500: '#5894d2',
                            600: '#4a7bb8',
                            700: '#3e6396',
                            800: '#35537c',
                            900: '#2f4666',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom Medical Staff CSS -->
    <style>
        :root {
            --background: #ffffff;
            --foreground: #1a2332;
            --primary-green: #49a75c;
            --primary-blue: #5894d2;
            --accent-gold: #f4d03f;
            --deep-navy: #1a2332;
            --soft-gray: #f8fafc;
            --medical-white: #ffffff;
            --shadow-primary: rgba(73, 167, 92, 0.15);
            --shadow-blue: rgba(88, 148, 210, 0.15);
            --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
            --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Medical Glass Effects */
        .medical-glass {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(73, 167, 92, 0.1);
        }

        .medical-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            position: relative;
            overflow: hidden;
        }

        .medical-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 3px;
            background: var(--gradient-primary);
            transition: left 0.6s ease;
        }

        .medical-card:hover::before {
            left: 100%;
        }

        .medical-card:hover {
            transform: translateY(-5px);
            border-color: rgba(73, 167, 92, 0.3);
            box-shadow: 0 20px 40px var(--shadow-primary);
        }

        /* Medical Buttons */
        .medical-btn-primary {
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .medical-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow-primary);
        }

        .medical-btn-secondary {
            background: white;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .medical-btn-secondary:hover {
            background: var(--primary-blue);
            color: white;
            transform: translateY(-2px);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(73, 167, 92, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-blue);
        }

        /* Selection */
        ::selection {
            background: var(--primary-green);
            color: white;
        }

        /* Notification styles */
        .medical-notification-dot {
            animation: medicalPulse 2s infinite;
        }

        @keyframes medicalPulse {
            0%, 100% {
                opacity: 1;
                box-shadow: 0 0 0 0 var(--primary-green);
            }
            50% {
                opacity: 0.7;
                box-shadow: 0 0 0 10px rgba(73, 167, 92, 0);
            }
        }

        .medical-dropdown {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(73, 167, 92, 0.1);
            border-radius: 16px;
            box-shadow: 0 20px 40px var(--shadow-primary);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .medical-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            pointer-events: auto;
        }

        .medical-dropdown.hidden {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            pointer-events: none;
        }

        /* Mobile menu */
        .medical-mobile-menu {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
            backdrop-filter: blur(20px);
            transform: translateX(-100%);
            transition: transform 0.3s ease-in-out;
        }

        .medical-mobile-menu.open {
            transform: translateX(0);
        }

        /* Focus styles for accessibility */
        a:focus, button:focus, input:focus, select:focus, textarea:focus {
            outline: 2px solid var(--primary-green);
            outline-offset: 2px;
        }

        /* Loading animation */
        .medical-loading {
            animation: medicalSpin 1s linear infinite;
        }

        @keyframes medicalSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Status indicators */
        .medical-status-online {
            background: var(--primary-green);
            box-shadow: 0 0 0 2px rgba(73, 167, 92, 0.3);
        }

        .medical-status-busy {
            background: var(--accent-gold);
            box-shadow: 0 0 0 2px rgba(244, 208, 63, 0.3);
        }

        .medical-status-offline {
            background: #6b7280;
            box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.3);
        }

        /* Medical filter buttons */
        .medical-filter-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            border-radius: 9999px;
            border: 1px solid rgba(73, 167, 92, 0.3);
            color: var(--primary-green);
            white-space: nowrap;
            transition: all 0.2s;
            background: transparent;
        }

        .medical-filter-btn:hover {
            color: white;
            background: var(--primary-green);
            border-color: var(--primary-green);
        }

        .medical-filter-btn.active {
            background: var(--gradient-primary);
            color: white;
            border-color: var(--primary-green);
        }

        /* Enhanced Mobile Responsive Design */
        @media (max-width: 640px) {
            .medical-card {
                border-radius: 16px;
                margin: 0 -0.5rem;
            }

            .medical-btn-primary,
            .medical-btn-secondary {
                padding: 14px 20px;
                font-size: 0.875rem;
                min-height: 48px;
                touch-action: manipulation;
            }

            .medical-dropdown {
                left: 0.5rem !important;
                right: 0.5rem !important;
                width: auto !important;
                max-width: none !important;
                transform: translateX(0) !important;
            }

            /* Ensure dropdown content is readable on mobile */
            .medical-dropdown a,
            .medical-dropdown button {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            /* Mobile-friendly touch targets */
            button, a, input, select {
                min-height: 44px;
                touch-action: manipulation;
            }

            /* Improved mobile navigation */
            .medical-mobile-menu {
                width: 85vw;
                max-width: 320px;
            }
        }

        @media (max-width: 768px) {
            .medical-card {
                padding: 1.5rem;
            }

            /* Stack elements vertically on mobile */
            .mobile-stack {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .mobile-stack > * {
                width: 100%;
            }
        }

        /* Enhanced tablet responsive design */
        @media (min-width: 641px) and (max-width: 1024px) {
            .medical-card {
                padding: 2rem;
            }

            .medical-dropdown {
                width: 24rem;
            }
        }

        /* Touch device optimizations */
        @media (hover: none) and (pointer: coarse) {
            .medical-card:hover {
                transform: none;
            }

            .medical-btn-primary:hover,
            .medical-btn-secondary:hover {
                transform: none;
            }

            /* Larger touch targets for mobile */
            .medical-btn-primary,
            .medical-btn-secondary {
                padding: 16px 24px;
                min-height: 48px;
            }
        }

        /* Medical Modal System */
        .medical-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .medical-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .medical-modal {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.2);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.9) translateY(20px);
            transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
        }

        .medical-modal-overlay.show .medical-modal {
            transform: scale(1) translateY(0);
        }

        .medical-modal-header {
            padding: 24px 24px 0 24px;
            border-bottom: 1px solid rgba(73, 167, 92, 0.1);
        }

        .medical-modal-body {
            padding: 24px;
        }

        .medical-modal-footer {
            padding: 0 24px 24px 24px;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .medical-modal-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }

        .medical-modal-icon.success {
            background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(73, 167, 92, 0.05));
            color: var(--primary-green);
        }

        .medical-modal-icon.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
            color: #ef4444;
        }

        .medical-modal-icon.warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
            color: #f59e0b;
        }

        .medical-modal-icon.info {
            background: linear-gradient(135deg, rgba(88, 148, 210, 0.1), rgba(88, 148, 210, 0.05));
            color: var(--primary-blue);
        }

        /* Medical Toast Notifications */
        .medical-toast-container {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .medical-toast {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.2);
            border-radius: 16px;
            padding: 16px 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            min-width: 300px;
            max-width: 400px;
            transform: translateX(100%);
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        }

        .medical-toast.show {
            transform: translateX(0);
        }

        .medical-toast.success {
            border-color: rgba(73, 167, 92, 0.3);
        }

        .medical-toast.error {
            border-color: rgba(239, 68, 68, 0.3);
        }

        .medical-toast.warning {
            border-color: rgba(245, 158, 11, 0.3);
        }

        .medical-toast.info {
            border-color: rgba(88, 148, 210, 0.3);
        }

        /* Medical Loading Spinner */
        .medical-loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(73, 167, 92, 0.1);
            border-top: 3px solid var(--primary-green);
            border-radius: 50%;
            animation: medicalSpin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes medicalSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="antialiased min-h-screen" style="background: var(--background);">
    <!-- Enhanced Medical Staff Header -->
    <header class="fixed top-0 left-0 right-0 z-50" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95)); backdrop-filter: blur(25px); border-bottom: 2px solid rgba(73, 167, 92, 0.2); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Enhanced Medical Logo and Brand -->
                <div class="flex items-center">
                    <button id="mobile-menu-toggle"
                            class="lg:hidden p-3 rounded-xl text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2 transition-all duration-300 transform hover:scale-105"
                            aria-label="Open medical navigation menu"
                            aria-expanded="false"
                            aria-controls="mobile-sidebar">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>

                    <div class="flex items-center ml-4 lg:ml-0">
                        <!-- Enhanced Medical Logo -->
                        <div class="relative mr-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-redolence-green via-redolence-blue to-redolence-green rounded-2xl flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-105">
                                <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                </svg>
                            </div>
                            <!-- Medical Pulse Animation -->
                            <div class="absolute inset-0 w-12 h-12 bg-redolence-green/20 rounded-2xl animate-ping"></div>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h1 class="text-lg sm:text-xl font-bold bg-gradient-to-r from-redolence-navy to-redolence-blue bg-clip-text text-transparent truncate"><?= APP_NAME ?></h1>
                            <p class="text-xs sm:text-sm text-redolence-green font-semibold flex items-center">
                                <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                                <span class="hidden sm:inline">Professional Medical Staff Portal</span>
                                <span class="sm:hidden">Medical Staff Portal</span>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Right Side - Medical User Menu -->
                <div class="flex items-center space-x-4">
                    <!-- Enhanced Medical Notifications -->
                    <div class="relative">
                        <button id="notificationButton"
                                class="relative p-3 rounded-xl text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2 transition-all duration-300 transform hover:scale-105"
                                aria-label="Medical notifications"
                                aria-describedby="notification-count"
                                aria-expanded="false"
                                aria-haspopup="true">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a1.5 1.5 0 010-2.12L20 8h-5V6a3 3 0 00-6 0v2H4l3.5 3.38a1.5 1.5 0 010 2.12L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <!-- Medical Notification Badge -->
                            <div id="notificationCounter"
                                 class="medical-notification-dot"
                                 aria-label="Unread notifications count"
                                 role="status"
                                 style="
                                position: absolute;
                                top: -8px;
                                right: -8px;
                                background: var(--primary-green);
                                color: white;
                                border-radius: 50%;
                                width: 22px;
                                height: 22px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 11px;
                                font-weight: bold;
                                z-index: 1000;
                                border: 2px solid white;
                                box-shadow: 0 2px 8px var(--shadow-primary);
                                visibility: visible;
                            ">...</div>
                        </button>

                        <!-- Quick access to notifications page -->
                        <a href="<?= $basePath ?>/staff/notifications/" class="hidden md:block ml-2 text-xs text-gray-600 hover:text-redolence-green transition-colors">
                            View All
                        </a>

                        <!-- Medical Notification Dropdown -->
                        <div id="notificationDropdown" class="medical-dropdown hidden absolute right-0 mt-2 w-80 sm:w-96 z-50 max-h-96 overflow-hidden" role="menu" aria-labelledby="notificationButton">
                            <div class="p-4 border-b border-redolence-green/20">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-redolence-navy">Medical Notifications</h3>
                                    <div class="flex space-x-2">
                                        <button id="markAllReadBtn" class="text-xs text-redolence-green hover:text-redolence-blue transition-colors">Mark all read</button>
                                        <button id="clearAllBtn" class="text-xs text-red-500 hover:text-red-600 transition-colors">Clear all</button>
                                    </div>
                                </div>

                                <!-- Category Filter -->
                                <div class="mt-3 flex space-x-2 overflow-x-auto">
                                    <button class="medical-filter-btn active" data-category="all">All</button>
                                    <button class="medical-filter-btn" data-category="BOOKING">Appointments</button>
                                    <button class="medical-filter-btn" data-category="STAFF">Admin Updates</button>
                                    <button class="medical-filter-btn" data-category="SYSTEM">System</button>
                                </div>
                            </div>

                            <div id="notificationsList" class="max-h-64 overflow-y-auto medical-scrollbar">
                                <div class="p-4 text-center text-gray-600">
                                    <svg class="mx-auto h-8 w-8 mb-2 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a1.5 1.5 0 010-2.12L20 8h-5V6a3 3 0 00-6 0v2H4l3.5 3.38a1.5 1.5 0 010 2.12L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                    </svg>
                                    <p>Loading medical notifications...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Time -->
                    <div class="hidden lg:block text-right">
                        <p class="text-sm text-gray-600"><?= date('l, F j, Y') ?></p>
                        <p class="text-sm font-semibold text-redolence-green"><?= date('g:i A') ?></p>
                    </div>

                    <!-- Medical User Profile Dropdown -->
                    <div class="relative">
                        <button id="user-menu-toggle"
                                class="flex items-center space-x-3 p-3 rounded-xl text-gray-600 hover:text-redolence-navy hover:bg-redolence-green/10 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2 transition-all duration-300 transform hover:scale-105"
                                aria-label="User menu for <?= htmlspecialchars($staffProfile['name']) ?>"
                                aria-expanded="false"
                                aria-haspopup="true">
                            <!-- Enhanced Medical Staff Avatar -->
                            <div class="relative">
                                <div class="h-10 w-10 rounded-full bg-gradient-to-br from-redolence-green via-redolence-blue to-redolence-green flex items-center justify-center shadow-lg" aria-hidden="true">
                                    <span class="text-sm font-bold text-white">
                                        <?= strtoupper(substr($staffProfile['name'], 0, 2)) ?>
                                    </span>
                                </div>
                                <!-- Online Status Indicator -->
                                <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-semibold text-redolence-navy"><?= htmlspecialchars($staffProfile['name']) ?></p>
                                <p class="text-xs text-redolence-green font-medium flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                    Medical Professional
                                </p>
                            </div>
                            <svg class="h-4 w-4 text-gray-500 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Medical Dropdown Menu -->
                        <div id="user-menu" class="medical-dropdown hidden absolute right-0 mt-2 w-48 py-2 z-50" role="menu" aria-labelledby="user-menu-toggle">
                            <a href="<?= $basePath ?>/staff" class="block px-4 py-2 text-sm text-gray-700 hover:bg-redolence-green/10 hover:text-redolence-green transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />
                                </svg>
                                Medical Dashboard
                            </a>
                            <a href="<?= $basePath ?>/staff/schedule" class="block px-4 py-2 text-sm text-gray-700 hover:bg-redolence-green/10 hover:text-redolence-green transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                My Schedule
                            </a>
                            <div class="border-t border-redolence-green/20 my-1"></div>
                            <a href="<?= $basePath ?>/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-redolence-blue/10 hover:text-redolence-blue transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                View Website
                            </a>
                            <a href="<?= $basePath ?>/auth/logout.php" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Enhanced Medical Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden transition-opacity duration-300">
        <div class="fixed inset-0 bg-black/60 backdrop-blur-md"></div>
    </div>

    <!-- Enhanced Medical Mobile Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-80 lg:hidden transform -translate-x-full transition-transform duration-300 ease-in-out"
         style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95)); backdrop-filter: blur(25px); border-right: 2px solid rgba(73, 167, 92, 0.2); box-shadow: 8px 0 32px rgba(0, 0, 0, 0.1);">

        <!-- Enhanced Mobile Header -->
        <div class="flex items-center justify-between p-6 border-b-2 border-gradient-to-r from-redolence-green/30 to-redolence-blue/30">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-br from-redolence-green to-redolence-blue rounded-xl flex items-center justify-center mr-3">
                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                    </svg>
                </div>
                <div>
                    <h2 class="text-lg font-bold text-redolence-navy">Medical Navigation</h2>
                    <p class="text-xs text-redolence-green font-medium">Professional Portal</p>
                </div>
            </div>
            <button id="mobile-sidebar-close"
                    class="p-2 rounded-xl text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10 focus:outline-none focus:ring-2 focus:ring-redolence-green transition-all duration-300 transform hover:scale-105"
                    aria-label="Close medical navigation menu">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Enhanced Mobile Navigation -->
        <nav class="flex-1 overflow-y-auto p-4">
            <?php include __DIR__ . '/staff_sidebar_nav.php'; ?>
        </nav>

        <!-- Mobile Footer -->
        <div class="p-4 border-t border-redolence-green/20">
            <div class="text-center">
                <p class="text-xs text-gray-500 flex items-center justify-center">
                    <svg class="w-3 h-3 mr-1 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    HIPAA Compliant Medical System
                </p>
            </div>
        </div>
    </div>

    <!-- Medical Modal System -->
    <div id="medicalModalOverlay" class="medical-modal-overlay" role="dialog" aria-modal="true" aria-hidden="true">
        <div class="medical-modal" role="document">
            <div class="medical-modal-header">
                <div id="medicalModalIcon" class="medical-modal-icon"></div>
                <h3 id="medicalModalTitle" class="text-xl font-bold text-redolence-navy text-center"></h3>
            </div>
            <div class="medical-modal-body">
                <p id="medicalModalMessage" class="text-gray-700 text-center leading-relaxed"></p>
                <div id="medicalModalInput" class="mt-4 hidden">
                    <input type="text" id="medicalModalInputField" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all" placeholder="">
                </div>
            </div>
            <div class="medical-modal-footer">
                <button id="medicalModalCancel" class="medical-btn-secondary px-6 py-3 hidden">Cancel</button>
                <button id="medicalModalConfirm" class="medical-btn-primary px-6 py-3">OK</button>
            </div>
        </div>
    </div>

    <!-- Medical Toast Container -->
    <div id="medicalToastContainer" class="medical-toast-container" aria-live="polite"></div>

    <script>
        // Medical Modal System
        class MedicalModal {
            constructor() {
                this.overlay = document.getElementById('medicalModalOverlay');
                this.modal = this.overlay.querySelector('.medical-modal');
                this.icon = document.getElementById('medicalModalIcon');
                this.title = document.getElementById('medicalModalTitle');
                this.message = document.getElementById('medicalModalMessage');
                this.inputContainer = document.getElementById('medicalModalInput');
                this.inputField = document.getElementById('medicalModalInputField');
                this.cancelBtn = document.getElementById('medicalModalCancel');
                this.confirmBtn = document.getElementById('medicalModalConfirm');

                this.setupEventListeners();
            }

            setupEventListeners() {
                // Close modal on overlay click
                this.overlay.addEventListener('click', (e) => {
                    if (e.target === this.overlay) {
                        this.hide();
                    }
                });

                // Close modal on Escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.overlay.classList.contains('show')) {
                        this.hide();
                    }
                });

                // Cancel button
                this.cancelBtn.addEventListener('click', () => {
                    this.hide();
                    if (this.onCancel) this.onCancel();
                });

                // Confirm button
                this.confirmBtn.addEventListener('click', () => {
                    const inputValue = this.inputField.value;
                    this.hide();
                    if (this.onConfirm) this.onConfirm(inputValue);
                });
            }

            show(options = {}) {
                const {
                    type = 'info',
                    title = 'Medical System',
                    message = '',
                    showCancel = false,
                    confirmText = 'OK',
                    cancelText = 'Cancel',
                    showInput = false,
                    inputPlaceholder = '',
                    onConfirm = null,
                    onCancel = null
                } = options;

                // Set icon based on type
                this.icon.className = `medical-modal-icon ${type}`;
                this.icon.innerHTML = this.getIconSVG(type);

                // Set content
                this.title.textContent = title;
                this.message.textContent = message;
                this.confirmBtn.textContent = confirmText;
                this.cancelBtn.textContent = cancelText;

                // Show/hide elements
                this.cancelBtn.classList.toggle('hidden', !showCancel);
                this.inputContainer.classList.toggle('hidden', !showInput);

                if (showInput) {
                    this.inputField.placeholder = inputPlaceholder;
                    this.inputField.value = '';
                }

                // Set callbacks
                this.onConfirm = onConfirm;
                this.onCancel = onCancel;

                // Show modal
                this.overlay.classList.add('show');
                this.overlay.setAttribute('aria-hidden', 'false');

                // Focus management
                setTimeout(() => {
                    if (showInput) {
                        this.inputField.focus();
                    } else {
                        this.confirmBtn.focus();
                    }
                }, 100);
            }

            hide() {
                this.overlay.classList.remove('show');
                this.overlay.setAttribute('aria-hidden', 'true');
                this.onConfirm = null;
                this.onCancel = null;
            }

            getIconSVG(type) {
                const icons = {
                    success: '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',
                    error: '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>',
                    warning: '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>',
                    info: '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
                };
                return icons[type] || icons.info;
            }
        }

        // Medical Toast System
        class MedicalToast {
            constructor() {
                this.container = document.getElementById('medicalToastContainer');
            }

            show(message, type = 'info', duration = 5000) {
                const toast = document.createElement('div');
                toast.className = `medical-toast ${type}`;

                const icon = this.getIconSVG(type);
                toast.innerHTML = `
                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0 mt-0.5">
                            ${icon}
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-redolence-navy">${message}</p>
                        </div>
                        <button class="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                `;

                this.container.appendChild(toast);

                // Show toast
                setTimeout(() => toast.classList.add('show'), 100);

                // Auto remove
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 400);
                }, duration);
            }

            getIconSVG(type) {
                const icons = {
                    success: '<svg class="w-5 h-5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',
                    error: '<svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>',
                    warning: '<svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>',
                    info: '<svg class="w-5 h-5 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
                };
                return icons[type] || icons.info;
            }
        }

        // Initialize systems
        const medicalModal = new MedicalModal();
        const medicalToast = new MedicalToast();

        // Replace native JavaScript dialogs
        window.medicalAlert = function(message, title = 'Medical System Alert') {
            return new Promise((resolve) => {
                medicalModal.show({
                    type: 'info',
                    title: title,
                    message: message,
                    onConfirm: resolve
                });
            });
        };

        window.medicalConfirm = function(message, title = 'Medical System Confirmation') {
            return new Promise((resolve) => {
                medicalModal.show({
                    type: 'warning',
                    title: title,
                    message: message,
                    showCancel: true,
                    confirmText: 'Confirm',
                    cancelText: 'Cancel',
                    onConfirm: () => resolve(true),
                    onCancel: () => resolve(false)
                });
            });
        };

        window.medicalPrompt = function(message, defaultValue = '', title = 'Medical System Input') {
            return new Promise((resolve) => {
                medicalModal.show({
                    type: 'info',
                    title: title,
                    message: message,
                    showCancel: true,
                    showInput: true,
                    inputPlaceholder: defaultValue,
                    confirmText: 'Submit',
                    cancelText: 'Cancel',
                    onConfirm: (value) => resolve(value),
                    onCancel: () => resolve(null)
                });
            });
        };

        window.medicalSuccess = function(message) {
            medicalToast.show(message, 'success');
        };

        window.medicalError = function(message) {
            medicalToast.show(message, 'error');
        };

        window.medicalWarning = function(message) {
            medicalToast.show(message, 'warning');
        };

        window.medicalInfo = function(message) {
            medicalToast.show(message, 'info');
        };

        // Enhanced Mobile menu toggle with smooth animations
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
        const mobileSidebarClose = document.getElementById('mobile-sidebar-close');

        function openMobileSidebar() {
            // Show elements
            mobileSidebar.classList.remove('hidden');
            mobileSidebarOverlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Trigger animations
            setTimeout(() => {
                mobileSidebar.style.transform = 'translateX(0)';
                mobileSidebarOverlay.style.opacity = '1';
            }, 10);

            mobileMenuToggle.setAttribute('aria-expanded', 'true');

            // Focus management - focus close button for better UX
            setTimeout(() => {
                mobileSidebarClose.focus();
            }, 300);
        }

        function closeMobileSidebar() {
            // Trigger exit animations
            mobileSidebar.style.transform = 'translateX(-100%)';
            mobileSidebarOverlay.style.opacity = '0';

            // Hide elements after animation
            setTimeout(() => {
                mobileSidebar.classList.add('hidden');
                mobileSidebarOverlay.classList.add('hidden');
                document.body.style.overflow = '';
            }, 300);

            mobileMenuToggle.setAttribute('aria-expanded', 'false');
            mobileMenuToggle.focus(); // Return focus to toggle button
        }

        if (mobileMenuToggle && mobileSidebar && mobileSidebarOverlay && mobileSidebarClose) {
            mobileMenuToggle.addEventListener('click', openMobileSidebar);
            mobileSidebarClose.addEventListener('click', closeMobileSidebar);
            mobileSidebarOverlay.addEventListener('click', closeMobileSidebar);

            // Enhanced keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !mobileSidebar.classList.contains('hidden')) {
                    closeMobileSidebar();
                }
            });

            // Touch gesture support for mobile
            let startX = 0;
            let currentX = 0;
            let isDragging = false;

            mobileSidebar.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
                isDragging = true;
            });

            mobileSidebar.addEventListener('touchmove', function(e) {
                if (!isDragging) return;
                currentX = e.touches[0].clientX;
                const diffX = currentX - startX;

                if (diffX < 0) {
                    const translateX = Math.max(diffX, -mobileSidebar.offsetWidth);
                    mobileSidebar.style.transform = `translateX(${translateX}px)`;
                }
            });

            mobileSidebar.addEventListener('touchend', function(e) {
                if (!isDragging) return;
                isDragging = false;

                const diffX = currentX - startX;
                if (diffX < -100) { // Swipe threshold
                    closeMobileSidebar();
                } else {
                    mobileSidebar.style.transform = 'translateX(0)';
                }
            });
        }

        // Function to adjust dropdown position to prevent cutoff
        function adjustDropdownPosition(dropdown) {
            // Reset any previous transforms
            dropdown.style.transform = '';
            dropdown.style.left = '';
            dropdown.style.right = '';

            // Get current position
            const rect = dropdown.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const dropdownWidth = rect.width;
            const padding = 10; // Minimum padding from screen edge

            // For mobile devices (width < 640px), use full-width positioning
            if (viewportWidth < 640) {
                dropdown.style.left = `${padding}px`;
                dropdown.style.right = `${padding}px`;
                dropdown.style.width = 'auto';
                dropdown.style.transform = 'translateX(0)';
                return;
            }

            // For larger screens, check if dropdown extends beyond right edge
            if (rect.right > viewportWidth - padding) {
                const overflow = rect.right - (viewportWidth - padding);
                dropdown.style.transform = `translateX(-${overflow}px)`;
            } else if (rect.left < padding) {
                // Check if dropdown extends beyond left edge
                const underflow = padding - rect.left;
                dropdown.style.transform = `translateX(${underflow}px)`;
            } else {
                dropdown.style.transform = 'translateX(0)';
            }
        }

        // Initialize dropdown states
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure all dropdowns start hidden
            document.querySelectorAll('.medical-dropdown').forEach(dropdown => {
                dropdown.classList.add('hidden');
                dropdown.classList.remove('show');
            });
        });

        // Handle window resize to readjust dropdown positions
        window.addEventListener('resize', function() {
            document.querySelectorAll('.medical-dropdown.show').forEach(dropdown => {
                adjustDropdownPosition(dropdown);
            });
        });

        // Medical User menu toggle with proper accessibility
        const userMenuToggle = document.getElementById('user-menu-toggle');
        const userMenu = document.getElementById('user-menu');

        if (userMenuToggle && userMenu) {
            userMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const isOpen = userMenu.classList.contains('show');

                // Close all other dropdowns first
                document.querySelectorAll('.medical-dropdown.show').forEach(dropdown => {
                    if (dropdown !== userMenu) {
                        dropdown.classList.remove('show');
                        dropdown.classList.add('hidden');
                    }
                });

                if (isOpen) {
                    userMenu.classList.remove('show');
                    userMenu.classList.add('hidden');
                    userMenuToggle.setAttribute('aria-expanded', 'false');
                } else {
                    userMenu.classList.remove('hidden');
                    userMenu.classList.add('show');
                    userMenuToggle.setAttribute('aria-expanded', 'true');

                    // Adjust dropdown position to prevent cutoff
                    adjustDropdownPosition(userMenu);

                    // Focus first menu item
                    const firstMenuItem = userMenu.querySelector('a');
                    if (firstMenuItem) {
                        setTimeout(() => firstMenuItem.focus(), 100);
                    }
                }
            });

            // Close user menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!userMenuToggle.contains(event.target) && !userMenu.contains(event.target)) {
                    userMenu.classList.remove('show');
                    userMenu.classList.add('hidden');
                    userMenuToggle.setAttribute('aria-expanded', 'false');
                }
            });

            // Keyboard navigation for user menu
            userMenu.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    userMenu.classList.remove('show');
                    userMenu.classList.add('hidden');
                    userMenuToggle.setAttribute('aria-expanded', 'false');
                    userMenuToggle.focus();
                }
            });
        }

        // Medical Notification system with proper accessibility
        const notificationButton = document.getElementById('notificationButton');
        const notificationDropdown = document.getElementById('notificationDropdown');

        if (notificationButton && notificationDropdown) {
            notificationButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const isOpen = notificationDropdown.classList.contains('show');

                // Close all other dropdowns first
                document.querySelectorAll('.medical-dropdown.show').forEach(dropdown => {
                    if (dropdown !== notificationDropdown) {
                        dropdown.classList.remove('show');
                        dropdown.classList.add('hidden');
                    }
                });

                if (isOpen) {
                    notificationDropdown.classList.remove('show');
                    notificationDropdown.classList.add('hidden');
                    notificationButton.setAttribute('aria-expanded', 'false');
                } else {
                    notificationDropdown.classList.remove('hidden');
                    notificationDropdown.classList.add('show');
                    notificationButton.setAttribute('aria-expanded', 'true');

                    // Adjust dropdown position to prevent cutoff
                    adjustDropdownPosition(notificationDropdown);

                    loadNotifications();
                }
            });

            // Close notification dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!notificationButton.contains(event.target) && !notificationDropdown.contains(event.target)) {
                    notificationDropdown.classList.remove('show');
                    notificationDropdown.classList.add('hidden');
                    notificationButton.setAttribute('aria-expanded', 'false');
                }
            });

            // Keyboard navigation for notification dropdown
            notificationDropdown.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    notificationDropdown.classList.remove('show');
                    notificationDropdown.classList.add('hidden');
                    notificationButton.setAttribute('aria-expanded', 'false');
                    notificationButton.focus();
                }
            });
        }

        // Helper functions for badge classes
        function getCategoryBadgeClass(category) {
            switch (category) {
                case 'BOOKING':
                    return 'bg-blue-500/20 text-blue-400';
                case 'STAFF':
                    return 'bg-purple-500/20 text-purple-400';
                case 'SYSTEM':
                    return 'bg-gray-500/20 text-gray-400';
                default:
                    return 'bg-gray-500/20 text-gray-400';
            }
        }

        function getPriorityBadgeClass(priority) {
            switch (priority) {
                case 'HIGH':
                    return 'bg-red-500/20 text-red-400';
                case 'MEDIUM':
                    return 'bg-yellow-500/20 text-yellow-400';
                case 'LOW':
                    return 'bg-green-500/20 text-green-400';
                default:
                    return 'bg-gray-500/20 text-gray-400';
            }
        }

        // Load notifications
        function loadNotifications() {
            const notificationsList = document.getElementById('notificationsList');
            notificationsList.innerHTML = '<div class="p-4 text-center text-gray-400"><svg class="mx-auto h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a1.5 1.5 0 010-2.12L20 8h-5V6a3 3 0 00-6 0v2H4l3.5 3.38a1.5 1.5 0 010 2.12L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /></svg><p>Loading notifications...</p></div>';

            fetch('<?= getBasePath() ?>/api/staff/notifications.php')
                .then(response => {
                    console.log('📡 Notifications API response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📊 Notifications API data:', data);

                    if (data.error) {
                        console.error('❌ API Error:', data.error);
                        notificationsList.innerHTML = `<div class="p-4 text-center text-red-400">${data.error}</div>`;
                        return;
                    }

                    const notifications = data.data.notifications || [];

                    if (notifications.length === 0) {
                        notificationsList.innerHTML = '<div class="p-4 text-center text-gray-400"><p>No notifications</p></div>';
                        return;
                    }

                    notificationsList.innerHTML = notifications.map(notification => `
                        <div class="p-4 border-b border-secondary-700 hover:bg-secondary-700/50 transition-colors">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryBadgeClass(notification.category)}">
                                            ${notification.category}
                                        </span>
                                        ${notification.priority !== 'MEDIUM' ? `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityBadgeClass(notification.priority)}">
                                                ${notification.priority}
                                            </span>
                                        ` : ''}
                                    </div>
                                    <p class="mt-1 text-sm font-medium text-white">${notification.title}</p>
                                    <p class="mt-1 text-sm text-gray-300">${notification.message}</p>
                                    ${notification.action_url ? `
                                        <a href="${notification.action_url}" class="mt-2 text-sm text-salon-gold hover:text-gold-light transition-colors">
                                            View Details →
                                        </a>
                                    ` : ''}
                                </div>
                                <div class="ml-4 flex-shrink-0 flex items-center space-x-2">
                                    <span class="text-xs text-gray-400">${notification.time_ago}</span>
                                    ${!notification.is_read ? `
                                        <button onclick="markAsRead('${notification.id}')" class="p-1 text-gray-400 hover:text-white transition-colors" title="Mark as read">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </button>
                                    ` : ''}
                                    <button onclick="deleteNotification('${notification.id}')" class="p-1 text-gray-400 hover:text-red-500 transition-colors" title="Delete">
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('');
                })
                .catch(error => {
                    console.error('❌ Error loading notifications:', error);
                    notificationsList.innerHTML = '<div class="p-4 text-center text-red-400">Failed to load notifications</div>';
                });
        }

        // Store previous count for comparison
        let previousNotificationCount = 0;

        // INSTANT notification counter update
        function updateNotificationCounter() {
            console.log('🔄 INSTANT: Updating notification counter...');

            const counter = document.getElementById('notificationCounter');
            if (!counter) {
                console.error('❌ INSTANT: Counter element not found');
                return;
            }

            // Create AbortController for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

            // Use the SUPER FAST count API endpoint with timeout
            fetch('<?= getBasePath() ?>/api/staff/quick-count.php', {
                signal: controller.signal,
                cache: 'no-cache',
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    clearTimeout(timeoutId);
                    console.log('📡 FAST: API responded in time');
                    return response.json();
                })
                .then(data => {
                    console.log('📊 FAST: API response:', data);

                    if (!data.success) {
                        console.error('❌ FAST: API Error:', data.error);
                        // Show error state
                        counter.textContent = '!';
                        counter.style.backgroundColor = '#ff8800';
                        counter.style.visibility = 'visible';
                        return;
                    }

                    const unreadCount = parseInt(data.count) || 0;
                    console.log('📬 FAST: Unread count:', unreadCount);

                    // Check for new notifications
                    if (unreadCount > previousNotificationCount && previousNotificationCount >= 0) {
                        console.log('🔔 FAST: New notifications detected!');
                        const button = document.getElementById('notificationButton');
                        if (button) {
                            button.style.animation = 'bounce 1s';
                            setTimeout(() => button.style.animation = '', 1000);
                        }
                    }

                    previousNotificationCount = unreadCount;

                    // UPDATE BADGE: Show immediately, hide only if 0
                    const displayText = unreadCount > 99 ? '99+' : unreadCount.toString();

                    // Save to localStorage for next page load
                    localStorage.setItem('notificationCount', displayText);

                    if (unreadCount > 0) {
                        counter.textContent = displayText;
                        counter.style.visibility = 'visible';
                        counter.style.display = 'flex';
                        counter.style.backgroundColor = '#ef4444';
                        console.log('✅ FAST: SHOWING badge:', unreadCount);
                    } else {
                        counter.style.visibility = 'hidden';
                        counter.style.display = 'none';
                        localStorage.setItem('notificationCount', '0');
                        console.log('✅ FAST: HIDING badge: no notifications');
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    console.error('❌ INSTANT: API failed:', error);

                    // Don't show error - use cached count instead
                    const cachedCount = localStorage.getItem('notificationCount');
                    if (cachedCount && cachedCount !== '0') {
                        counter.textContent = cachedCount;
                        counter.style.backgroundColor = '#ef4444';
                        counter.style.visibility = 'visible';
                        counter.style.display = 'flex';
                        console.log('⚡ INSTANT: Using cached count due to API error');
                    } else {
                        // Only show error if no cached count
                        counter.textContent = '0';
                        counter.style.visibility = 'hidden';
                        console.log('⚡ INSTANT: No cached count, hiding badge');
                    }
                });
        }

        // Mark notification as read
        function markAsRead(notificationId) {
            console.log('📖 REAL-TIME: Marking notification as read:', notificationId);

            // Update counter immediately (optimistic update)
            const counter = document.getElementById('notificationCounter');
            if (counter && counter.textContent !== '!' && counter.textContent !== '...') {
                const currentCount = parseInt(counter.textContent) || 0;
                if (currentCount > 0) {
                    const newCount = currentCount - 1;
                    counter.textContent = newCount > 0 ? (newCount > 99 ? '99+' : newCount.toString()) : '0';
                    if (newCount === 0) {
                        counter.style.visibility = 'hidden';
                    }
                    localStorage.setItem('notificationCount', newCount.toString());
                    console.log('⚡ REAL-TIME: Counter updated immediately to:', newCount);
                }
            }

            fetch('<?= getBasePath() ?>/api/staff/notifications.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: notificationId,
                    is_read: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('❌ Error marking as read:', data.error);
                    // Revert optimistic update
                    updateNotificationCounter();
                    return;
                }
                console.log('✅ REAL-TIME: Notification marked as read, syncing...');
                // Refresh notifications and counter to sync
                if (typeof loadNotifications === 'function') loadNotifications();
                setTimeout(updateNotificationCounter, 500); // Sync after 500ms
            })
            .catch(error => {
                console.error('❌ Error marking notification as read:', error);
                // Revert optimistic update
                updateNotificationCounter();
            });
        }

        // Delete notification
        function deleteNotification(notificationId) {
            console.log('🗑️ REAL-TIME: Deleting notification:', notificationId);

            // Update counter immediately (optimistic update)
            const counter = document.getElementById('notificationCounter');
            if (counter && counter.textContent !== '!' && counter.textContent !== '...') {
                const currentCount = parseInt(counter.textContent) || 0;
                if (currentCount > 0) {
                    const newCount = currentCount - 1;
                    counter.textContent = newCount > 0 ? (newCount > 99 ? '99+' : newCount.toString()) : '0';
                    if (newCount === 0) {
                        counter.style.visibility = 'hidden';
                    }
                    localStorage.setItem('notificationCount', newCount.toString());
                    console.log('⚡ REAL-TIME: Counter updated immediately to:', newCount);
                }
            }

            fetch(`<?= getBasePath() ?>/api/staff/notifications.php?id=${notificationId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('❌ Error deleting notification:', data.error);
                    // Revert optimistic update
                    updateNotificationCounter();
                    return;
                }
                console.log('✅ REAL-TIME: Notification deleted, syncing...');
                // Refresh notifications and counter to sync
                if (typeof loadNotifications === 'function') loadNotifications();
                setTimeout(updateNotificationCounter, 500); // Sync after 500ms
            })
            .catch(error => {
                console.error('❌ Error deleting notification:', error);
                // Revert optimistic update
                updateNotificationCounter();
            });
        }

        // Request notification permission on page load
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Global functions for real-time updates
        window.refreshNotificationCounter = function() {
            console.log('🔄 Manual refresh triggered');
            updateNotificationCounter();
        };

        // Decrease counter immediately (for real-time updates)
        window.decreaseNotificationCounter = function() {
            const counter = document.getElementById('notificationCounter');
            if (counter && counter.textContent !== '!' && counter.textContent !== '...') {
                const currentCount = parseInt(counter.textContent) || 0;
                if (currentCount > 0) {
                    const newCount = currentCount - 1;
                    counter.textContent = newCount > 0 ? (newCount > 99 ? '99+' : newCount.toString()) : '0';
                    if (newCount === 0) {
                        counter.style.visibility = 'hidden';
                    }
                    localStorage.setItem('notificationCount', newCount.toString());
                    console.log('⚡ REAL-TIME: Counter decreased to:', newCount);
                }
            }
        };

        // Set counter to specific value
        window.setNotificationCounter = function(count) {
            const counter = document.getElementById('notificationCounter');
            if (counter) {
                const displayText = count > 99 ? '99+' : count.toString();
                counter.textContent = displayText;
                if (count > 0) {
                    counter.style.visibility = 'visible';
                    counter.style.display = 'flex';
                    counter.style.backgroundColor = '#ef4444';
                } else {
                    counter.style.visibility = 'hidden';
                }
                localStorage.setItem('notificationCount', displayText);
                console.log('⚡ REAL-TIME: Counter set to:', count);
            }
        };



        // Initialize counter when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const counter = document.getElementById('notificationCounter');
            if (!counter) return;

            // Try to get cached count from localStorage
            const cachedCount = localStorage.getItem('notificationCount');
            if (cachedCount && cachedCount !== '0') {
                counter.textContent = cachedCount;
                counter.style.visibility = 'visible';
                counter.style.display = 'flex';
                counter.style.backgroundColor = '#ef4444';
                console.log('✅ FAST: Badge shown immediately with cached count:', cachedCount);
            } else {
                // Show loading state
                counter.textContent = '...';
                counter.style.visibility = 'visible';
                counter.style.display = 'flex';
                counter.style.backgroundColor = '#666';
                console.log('✅ FAST: Badge shown with loading state');
            }

            // Update with real data IMMEDIATELY (no delay)
            console.log('🚀 INSTANT: Starting immediate API call...');

            // Set a backup timer - if API doesn't respond in 2 seconds, keep cached value
            const backupTimer = setTimeout(() => {
                if (counter.textContent === '...') {
                    const cachedCount = localStorage.getItem('notificationCount');
                    if (cachedCount && cachedCount !== '0') {
                        counter.textContent = cachedCount;
                        counter.style.backgroundColor = '#ef4444';
                        console.log('⚡ INSTANT: Using cached count due to slow API');
                    }
                }
            }, 2000);

            updateNotificationCounter();
        });

        // Update counter every 10 seconds (faster sync)
        setInterval(updateNotificationCounter, 10000);
    </script>

<div class="min-h-screen medical-staff-page pt-20" style="background: var(--background);">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/staff_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
