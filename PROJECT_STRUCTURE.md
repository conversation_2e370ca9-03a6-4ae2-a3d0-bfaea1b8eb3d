# Project Structure

This document provides an overview of the main directories and files in the project, describing their purpose and contents.

```
/ (root directory)
│
├── admin/                  # Admin panel backend and management interface
│   ├── ajax/               # AJAX handlers for admin panel
│   ├── auth/               # Admin authentication related files
│   ├── blog/               # Admin blog management
│   ├── bookings/           # Admin booking management
│   ├── categories/         # Service categories management
│   ├── cms/                # Content management system files
│   ├── contact-messages/   # Admin contact message management
│   ├── customers/          # Customer management
│   ├── earnings/           # Earnings and financial reports
│   ├── faq/                # FAQ management
│   ├── notifications/      # Admin notifications
│   ├── offers/             # Offers management
│   ├── packages/           # Service packages management
│   ├── profile/            # Admin profile management
│   ├── rewards/            # Rewards management
│   ├── services/           # Services management
│   ├── settings/           # Admin settings
│   ├── staff/              # Staff management
│   ├── system/             # System related admin files
│   ├── add_sample_notifications.php
│   ├── booking-reminders.php
│   ├── debug_session_2fa.php
│   ├── email-logs.php
│   ├── error_log
│   ├── fix_backup_codes.php
│   ├── index.php           # Admin dashboard entry point
│   ├── reviews.php
│   ├── setup_admin_logs.php
│   └── sync-staff.php
│
├── api/                    # API endpoints for various functionalities
│   ├── admin/
│   ├── customer/
│   ├── payments/
│   ├── staff/
│   ├── error_log
│   ├── newsletter.php
│   ├── reviews.php
│   ├── services.php
│   └── wishlist.php
│
├── assets/                 # Static assets like CSS, JS, images, models, textures
│   ├── css/
│   ├── environments/
│   ├── js/
│   ├── models/
│   └── textures/
│
├── auth/                   # Authentication related pages and scripts
│   ├── error_log
│   ├── forgot-password.php
│   ├── login.php
│   ├── logout.php
│   ├── register.php
│   ├── reset-password.php
│   └── verify-otp.php
│
├── config/                 # Configuration files
│   ├── app.php
│   ├── app.php.example
│   ├── database.php.example
│   └── email_config_gmail.php
│
├── cron/                   # Scheduled tasks and cron job scripts
│   ├── send_reminders.php
│   └── cron script change user name  and php version.txt
│
├── customer/               # Customer-facing pages and functionality
│   ├── book/
│   ├── bookings/
│   ├── error_log
│   ├── index.php
│   ├── payments/
│   ├── profile/
│   ├── rewards/
│   └── wishlist.php
│
├── database/               # Database schema, migrations, seeds, and SQL scripts
│   ├── migrations/
│   ├── seeds/
│   ├── add_admin_notification_types.sql
│   ├── add_indexes.sql
│   ├── add_manual_services_column.sql
│   ├── add_otp_fields.php
│   ├── add_otp_fields.sql
│   ├── add_package_duration.sql
│   ├── create_2fa_tables.sql
│   ├── create_admin_logs_table.sql
│   ├── create_blog_posts_table.sql
│   ├── create_booking_reminders_table.sql
│   ├── create_contact_messages_table.sql
│   ├── create_customer_tiers_table.sql
│   ├── create_faq_table.sql
│   ├── create_rewards_table.sql
│   ├── custom_services_migration.sql
│   ├── deploy_2fa_live_server.sql
│   ├── fix_packages_table.sql
│   ├── fix_staff_tables.sql
│   ├── flix_salonce2.sql
│   ├── migrate_prices_to_integer.sql
│   ├── migrate_reviews.php
│   ├── migrations.sql
│   ├── simple_categories_migration.sql
│   ├── update_booking_system.sql
│   ├── update_payments_dpo.sql
│   ├── update_payments_table.sql
│   └── verify_2fa_compatibility.sql
│
├── docs/                   # Documentation files and guides
│   ├── 2FA_Implementation_Guide.md
│   ├── 2FA_BACKUP_CODES_FIX.md
│   ├── 2FA_IMPLEMENTATION_SUMMARY.md
│   ├── 2FA_Live_Server_Deployment.md
│   ├── 2FA_QUICK_SETUP_GUIDE.md
│   └── 2FA_SYSTEM_DOCUMENTATION.md
│
├── includes/               # PHP include files for functions, UI components, and utilities
│   ├── admin_2fa_functions.php
│   ├── admin_booking_functions.php
│   ├── admin_footer.php
│   ├── admin_header.php
│   ├── admin_profile_functions.php
│   ├── admin_sidebar.php
│   ├── admin_sidebar_nav.php
│   ├── Auth_backup.php
│   ├── auth.php
│   ├── blog_functions.php
│   ├── booking_expiration.php
│   ├── booking_functions.php
│   ├── booking_reminder_functions.php
│   ├── cms_functions.php
│   ├── contact_functions.php
│   ├── customer_footer.php
│   ├── customer_functions.php
│   ├── customer_header.php
│   ├── customer_panel_functions.php
│   ├── customer_sidebar.php
│   ├── customer_sidebar_nav.php
│   ├── dpo_functions.php
│   ├── earnings_functions.php
│   ├── email_functions.php
│   ├── footer.php
│   ├── forgot_password_functions.php
│   ├── functions.php
│   ├── header.php
│   ├── mobile-menu.php
│   ├── notification_triggers.php
│   ├── offer_functions.php
│   ├── package_functions.php
│   ├── payment_functions.php
│   ├── reviews_functions.php
│   ├── rewards_functions.php
│   ├── service_card.php
│   ├── service_category_functions.php
│   ├── service_functions.php
│   ├── service_variation_functions.php
│   ├── staff_footer.php
│   ├── staff_functions.php
│   ├── staff_header.php
│   ├── staff_panel_functions.php
│   ├── staff_schedule_functions.php
│   ├── staff_sidebar.php
│   ├── staff_sidebar_nav.php
│   ├── subcategory_functions.php
│   ├── upload_functions.php
│   └── wishlist_functions.php
│
├── staff/                  # Staff-facing pages and functionality
│   ├── appointments/
│   ├── earnings/
│
├── uploads/                # Uploaded files and media
│
├── .gitignore              # Git ignore file
├── .htaccess               # Apache configuration file
├── about.php               # About page
├── add_sample_reviews.php  # Sample data script
├── ajax_process_reminders.php
├── booking-policy.php
├── cancellation-policy.php
├── careers.php
├── composer.json           # PHP dependencies and project metadata
├── contact.php             # Contact page
├── email_troubleshoot.php
├── error_log
├── faq.php
├── gallery.php
├── gift-cards.php
├── GITHUB_SETUP.md
├── index.php               # Main entry point of the website
├── initialize_reminder_system.php
├── offers.php
├── our-team.php
├── packages.php
├── payment_verify.php
├── PRICING_VISIBILITY_MANAGEMENT.md
├── privacy.php
├── production-test-suite.php
├── reviews.php
├── services.php
├── setup.php
├── terms.php
├── verify-staff-structure.php
└── various other PHP scripts and assets
```

This structure reflects a modular and organized PHP web application with separate areas for admin, API, customer, staff, authentication, and shared includes.
