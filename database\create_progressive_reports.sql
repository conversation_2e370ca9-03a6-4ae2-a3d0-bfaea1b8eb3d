-- Progressive Reports Database Schema
-- Flix Salonce - Medical Aesthetics Progressive Reports System
-- Execute this script to add progressive reports functionality

USE flix_salonce;

-- Create progressive_reports table
CREATE TABLE IF NOT EXISTS progressive_reports (
    id VARCHAR(36) PRIMARY KEY,
    client_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) DEFAULT 'Progressive Treatment Report',
    description TEXT,
    status ENUM('ACTIVE', 'COMPLETED', 'ARCHIVED') DEFAULT 'ACTIVE',
    created_by VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (client_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes for performance
    INDEX idx_progressive_reports_client_id (client_id),
    INDEX idx_progressive_reports_created_by (created_by),
    INDEX idx_progressive_reports_status (status),
    INDEX idx_progressive_reports_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create progressive_report_entries table
CREATE TABLE IF NOT EXISTS progressive_report_entries (
    id VARCHAR(36) PRIMARY KEY,
    report_id VARCHAR(36) NOT NULL,
    appointment_id VARCHAR(36),
    entry_date DATE NOT NULL,
    treatment VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    notes TEXT,
    images JSON COMMENT 'Array of image URLs for before/after photos',
    created_by VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (report_id) REFERENCES progressive_reports(id) ON DELETE CASCADE,
    FOREIGN KEY (appointment_id) REFERENCES bookings(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes for performance
    INDEX idx_progressive_entries_report_id (report_id),
    INDEX idx_progressive_entries_appointment_id (appointment_id),
    INDEX idx_progressive_entries_created_by (created_by),
    INDEX idx_progressive_entries_entry_date (entry_date),
    INDEX idx_progressive_entries_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create view for easy report access with client information
CREATE OR REPLACE VIEW progressive_reports_view AS
SELECT 
    pr.id,
    pr.client_id,
    pr.title,
    pr.description,
    pr.status,
    pr.created_by,
    pr.created_at,
    pr.updated_at,
    u.name as client_name,
    u.email as client_email,
    u.phone as client_phone,
    creator.name as created_by_name,
    creator.role as created_by_role,
    COUNT(pre.id) as total_entries,
    MAX(pre.entry_date) as last_entry_date
FROM progressive_reports pr
LEFT JOIN users u ON pr.client_id = u.id
LEFT JOIN users creator ON pr.created_by = creator.id
LEFT JOIN progressive_report_entries pre ON pr.id = pre.report_id
GROUP BY pr.id, pr.client_id, pr.title, pr.description, pr.status, pr.created_by, pr.created_at, pr.updated_at,
         u.name, u.email, u.phone, creator.name, creator.role;

-- Create view for detailed report entries with appointment information
CREATE OR REPLACE VIEW progressive_report_entries_view AS
SELECT 
    pre.id,
    pre.report_id,
    pre.appointment_id,
    pre.entry_date,
    pre.treatment,
    pre.description,
    pre.notes,
    pre.images,
    pre.created_by,
    pre.created_at,
    pre.updated_at,
    pr.client_id,
    pr.title as report_title,
    u.name as client_name,
    creator.name as created_by_name,
    creator.role as created_by_role,
    b.date as appointment_date,
    b.start_time as appointment_start_time,
    b.status as appointment_status,
    s.name as service_name,
    staff.name as staff_name
FROM progressive_report_entries pre
LEFT JOIN progressive_reports pr ON pre.report_id = pr.id
LEFT JOIN users u ON pr.client_id = u.id
LEFT JOIN users creator ON pre.created_by = creator.id
LEFT JOIN bookings b ON pre.appointment_id = b.id
LEFT JOIN services s ON b.service_id = s.id
LEFT JOIN users staff ON b.staff_id = staff.id;

-- Insert sample data (optional - remove in production)
-- This is for testing purposes only
INSERT IGNORE INTO progressive_reports (id, client_id, title, description, created_by) 
SELECT 
    UUID() as id,
    u.id as client_id,
    'Progressive Treatment Report' as title,
    'Comprehensive treatment progress tracking for aesthetic procedures' as description,
    admin.id as created_by
FROM users u
CROSS JOIN (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1) admin
WHERE u.role = 'CUSTOMER' 
LIMIT 1;
