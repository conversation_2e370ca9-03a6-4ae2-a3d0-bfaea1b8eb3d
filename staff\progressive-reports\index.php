<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php');
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();
$progressiveReportEntry = new ProgressiveReportEntry();

// Get staff ID
$staffId = $_SESSION['user_id'];

// Get reports accessible by this staff member
$reports = $progressiveReport->getByStaffId($staffId, 1, 50);

$pageTitle = "Medical Treatment Progress Reports";
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Treatment Progress Reports Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center">
                <div class="flex-shrink-0 mr-6">
                    <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-2xl flex items-center justify-center">
                        <svg class="w-8 h-8 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                    </div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-redolence-navy">Medical Treatment <span class="text-redolence-green">Progress Reports</span></h1>
                    <p class="mt-2 text-lg text-gray-600">Comprehensive patient treatment progress tracking and documentation</p>
                    <div class="mt-3 flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Professional Medical Progress Documentation
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-6 sm:mt-0">
            <a href="<?= getBasePath() ?>/staff/appointments"
               class="medical-btn-secondary px-6 py-3">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                View Patient Appointments
            </a>
        </div>
    </div>
</div>

<!-- Medical Progress Reports List -->
<div class="space-y-6">
    <?php if (empty($reports)): ?>
        <div class="medical-card p-12 text-center">
            <div class="w-24 h-24 bg-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
            </div>
            <h3 class="text-2xl font-bold text-redolence-navy mb-3">No Medical Progress Reports</h3>
            <p class="text-gray-600 mb-8">You haven't created any medical treatment progress report entries yet.</p>
            <a href="<?= getBasePath() ?>/staff/appointments"
               class="medical-btn-primary px-8 py-4">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Add Medical Report Entry
            </a>
        </div>
    <?php else: ?>
        <?php foreach ($reports as $report): ?>
            <div class="medical-card p-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-16 w-16">
                            <div class="h-16 w-16 rounded-full bg-gradient-to-r from-redolence-green to-redolence-blue flex items-center justify-center">
                                <span class="text-lg font-bold text-white">
                                    <?= strtoupper(substr($report['client_name'], 0, 2)) ?>
                                </span>
                            </div>
                        </div>
                        <div class="ml-6">
                            <h3 class="text-xl font-bold text-redolence-navy">
                                <?= htmlspecialchars($report['title']) ?>
                            </h3>
                            <p class="text-redolence-green font-semibold text-lg">
                                Patient: <?= htmlspecialchars($report['client_name']) ?>
                            </p>
                            <div class="flex items-center space-x-6 text-sm text-gray-600 mt-2">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <?= number_format($report['total_entries']) ?> medical entries
                                </span>
                                <?php if ($report['last_entry_date']): ?>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Last updated: <?= date('M j, Y', strtotime($report['last_entry_date'])) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium
                            <?php if ($report['status'] === 'ACTIVE'): ?>
                                bg-green-100 text-green-800 border border-green-200
                            <?php elseif ($report['status'] === 'COMPLETED'): ?>
                                bg-blue-100 text-blue-800 border border-blue-200
                            <?php else: ?>
                                bg-gray-100 text-gray-800 border border-gray-200
                            <?php endif; ?>">
                            <?= ucfirst(strtolower($report['status'])) ?> Treatment
                        </span>
                        <a href="<?= getBasePath() ?>/staff/progressive-reports/view.php?id=<?= $report['id'] ?>"
                           class="medical-btn-secondary px-6 py-3">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View Medical Report
                        </a>
                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

<!-- Medical Help Section -->
<div class="medical-card mt-8 p-8">
    <h3 class="text-2xl font-bold text-redolence-navy mb-6">How to Use Medical Progress Reports</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 text-sm">
        <div>
            <h4 class="font-semibold text-redolence-green mb-4 text-lg">Adding Medical Entries</h4>
            <ul class="space-y-3 text-gray-700">
                <li class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Complete a medical appointment with a patient
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Go to your appointments and click "Add Medical Report"
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Document the medical treatment and patient progress
                </li>
            </ul>
        </div>
        <div>
            <h4 class="font-semibold text-redolence-blue mb-4 text-lg">Viewing Medical Reports</h4>
            <ul class="space-y-3 text-gray-700">
                <li class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    View all medical reports you've contributed to
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    See the complete medical treatment timeline
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 mr-3 mt-0.5 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Edit your own medical entries if needed
                </li>
            </ul>
        </div>
    </div>
</div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
