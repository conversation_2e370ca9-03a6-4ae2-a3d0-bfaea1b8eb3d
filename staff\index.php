<?php
session_start();
require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_appointment_status':
                    $appointmentId = $_POST['appointment_id'];
                    $status = $_POST['status'];
                    $notes = $_POST['notes'] ?? null;

                    updateAppointmentStatus($appointmentId, $_SESSION['user_id'], $status, $notes);
                    $message = 'Appointment status updated successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Medical status badge function
function getMedicalStatusBadgeClass($status) {
    switch (strtoupper($status)) {
        case 'PENDING':
            return 'bg-gradient-to-r from-yellow-100 to-yellow-50 text-yellow-800 border border-yellow-200';
        case 'CONFIRMED':
            return 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border border-blue-200';
        case 'IN_PROGRESS':
            return 'bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 border border-purple-200';
        case 'COMPLETED':
            return 'bg-gradient-to-r from-green-100 to-green-50 text-green-800 border border-green-200';
        case 'CANCELLED':
            return 'bg-gradient-to-r from-red-100 to-red-50 text-red-800 border border-red-200';
        default:
            return 'bg-gradient-to-r from-gray-100 to-gray-50 text-gray-800 border border-gray-200';
    }
}

// Get staff dashboard data
try {
    $staffId = $_SESSION['user_id'];
    $dashboardData = getStaffDashboardData($staffId);
} catch (Exception $e) {
    $message = 'Error loading dashboard data: ' . $e->getMessage();
    $messageType = 'error';
    $dashboardData = [
        'profile' => ['name' => $_SESSION['user_name'] ?? 'Medical Staff'],
        'todaySchedule' => ['totalAppointments' => 0, 'appointments' => [], 'isWorkingDay' => false],
        'upcomingAppointments' => [],
        'todayEarnings' => ['commissionEarned' => 0],
        'monthlyStats' => ['completedAppointments' => 0],
        'performanceMetrics' => ['completionRate' => 0, 'totalBookings' => 0, 'completedBookings' => 0, 'avgServiceValue' => 0, 'totalRevenue' => 0, 'monthlyGrowth' => 0],
        'recentCustomers' => []
    ];
}

$pageTitle = "Staff Dashboard";
include __DIR__ . '/../includes/staff_header.php';
?>

<!-- Medical Message Display -->
<?php if ($message): ?>
    <div class="medical-card mb-8 p-6 <?= $messageType === 'success' ? 'border-redolence-green/30 bg-gradient-to-r from-redolence-green/10 to-redolence-green/5' : 'border-red-500/30 bg-gradient-to-r from-red-500/10 to-red-500/5' ?>">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 <?= $messageType === 'success' ? 'text-redolence-green' : 'text-red-500' ?>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <?php if ($messageType === 'success'): ?>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    <?php else: ?>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    <?php endif; ?>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium <?= $messageType === 'success' ? 'text-redolence-green' : 'text-red-600' ?>">
                    <?= htmlspecialchars($message) ?>
                </p>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Medical Dashboard Header -->
<div class="medical-card mb-8 p-6 sm:p-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mobile-stack">
        <div class="flex-1">
            <h1 class="text-2xl sm:text-3xl font-bold text-redolence-navy">Medical Staff <span class="text-redolence-green">Dashboard</span></h1>
            <p class="mt-2 text-base sm:text-lg text-gray-600">Welcome back, Dr. <?= htmlspecialchars($dashboardData['profile']['name']) ?>!</p>
            <div class="mt-3 flex items-center text-xs sm:text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                <span class="break-words">Medical Professional Dashboard - HIPAA Compliant</span>
            </div>
        </div>
        <div class="mt-6 sm:mt-0 flex-shrink-0">
            <div class="text-center sm:text-right">
                <p class="text-sm text-gray-600"><?= date('l, F j, Y') ?></p>
                <p class="text-lg sm:text-xl font-semibold text-redolence-green" data-time><?= date('g:i A') ?></p>
                <div class="mt-2 flex items-center justify-center sm:justify-end text-xs text-gray-500">
                    <div class="w-2 h-2 bg-redolence-green rounded-full mr-2 animate-pulse"></div>
                    System Online
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Medical Stats Overview -->
<div class="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <!-- Today's Patient Appointments -->
    <div class="medical-card p-4 sm:p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-xl sm:rounded-2xl flex items-center justify-center">
                    <svg class="h-6 w-6 sm:h-8 sm:w-8 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
            </div>
            <div class="ml-4 sm:ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-xs sm:text-sm font-medium text-gray-600 truncate">Today's Patient Appointments</dt>
                    <dd class="text-2xl sm:text-3xl font-bold text-redolence-navy" id="today-appointments"><?= $dashboardData['todaySchedule']['totalAppointments'] ?></dd>
                    <dd class="text-xs text-redolence-green font-medium">Medical Consultations</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Today's Medical Earnings -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-2xl flex items-center justify-center">
                    <svg class="h-8 w-8 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Today's Medical Commission</dt>
                    <dd class="text-3xl font-bold text-redolence-blue" id="today-earnings"><?= formatCurrency($dashboardData['todayEarnings']['commissionEarned']) ?></dd>
                    <dd class="text-xs text-redolence-blue font-medium">Professional Earnings</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Monthly Treatment Completions -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-2xl flex items-center justify-center">
                    <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Monthly Treatment Completions</dt>
                    <dd class="text-3xl font-bold text-purple-600" id="monthly-bookings"><?= number_format($dashboardData['monthlyStats']['completedAppointments']) ?></dd>
                    <dd class="text-xs text-purple-600 font-medium">Successful Treatments</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Medical Success Rate -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-gradient-to-br from-emerald-500/20 to-emerald-500/10 rounded-2xl flex items-center justify-center">
                    <svg class="h-8 w-8 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Medical Success Rate</dt>
                    <dd class="text-3xl font-bold text-emerald-600" id="completion-rate"><?= $dashboardData['performanceMetrics']['completionRate'] ?>%</dd>
                    <dd class="text-xs text-emerald-600 font-medium">Treatment Completion</dd>
                </dl>
            </div>
        </div>
    </div>
</div>

<!-- Today's Medical Schedule -->
<div class="medical-card mb-8">
    <div class="px-6 py-5 border-b border-redolence-green/20">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-xl font-semibold text-redolence-navy">Today's Medical Schedule</h3>
                <p class="mt-1 text-sm text-gray-600">Your patient appointments and treatment schedule for today</p>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-redolence-green rounded-full animate-pulse"></div>
                <span class="text-sm text-redolence-green font-medium">Live Schedule</span>
            </div>
        </div>
    </div>
    <?php if (!$dashboardData['todaySchedule']['isWorkingDay']): ?>
        <div class="text-center py-12">
            <div class="w-20 h-20 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="h-10 w-10 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
            <h4 class="text-xl font-semibold text-redolence-navy mb-2">Medical Day Off</h4>
            <p class="text-gray-600">You're not scheduled for medical consultations today</p>
            <div class="mt-4 inline-flex items-center px-4 py-2 bg-redolence-blue/10 rounded-full">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707"></path>
                </svg>
                <span class="text-sm text-redolence-blue font-medium">Rest & Recovery Day</span>
            </div>
        </div>
    <?php elseif (empty($dashboardData['todaySchedule']['appointments'])): ?>
        <div class="text-center py-12">
            <div class="w-20 h-20 bg-gradient-to-br from-gray-400/20 to-gray-400/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="h-10 w-10 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
            <h4 class="text-xl font-semibold text-redolence-navy mb-2">No Patient Appointments</h4>
            <p class="text-gray-600">No medical consultations scheduled for today</p>
            <div class="mt-4 bg-redolence-green/10 rounded-lg p-4 max-w-sm mx-auto">
                <p class="text-sm text-redolence-green font-medium">
                    Medical Hours: <?= $dashboardData['todaySchedule']['workingHours']['start_time'] ?? 'N/A' ?> -
                    <?= $dashboardData['todaySchedule']['workingHours']['end_time'] ?? 'N/A' ?>
                </p>
            </div>
        </div>
    <?php else: ?>
        <div class="divide-y divide-redolence-green/10">
            <?php foreach ($dashboardData['todaySchedule']['appointments'] as $appointment): ?>
                <div class="px-6 py-5 hover:bg-redolence-green/5 transition-colors duration-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-12 w-12">
                                <div class="h-12 w-12 rounded-full bg-gradient-to-r from-redolence-green to-redolence-blue flex items-center justify-center">
                                    <span class="text-sm font-medium text-white">
                                        <?= strtoupper(substr($appointment['customer_name'], 0, 2)) ?>
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-base font-semibold text-redolence-navy">
                                    <?= htmlspecialchars($appointment['customer_name']) ?>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <?= htmlspecialchars($appointment['service_name']) ?>
                                </div>
                                <div class="text-xs text-redolence-green font-medium mt-1">
                                    Medical Treatment Session
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <div class="text-sm font-semibold text-redolence-navy">
                                    <?= date('g:i A', strtotime($appointment['start_time'])) ?>
                                </div>
                                <div class="text-sm text-redolence-blue font-medium">
                                    <?= formatCurrency($appointment['total_amount'] ?? 0) ?>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <?= getMedicalStatusBadgeClass($appointment['status']) ?>">
                                <?= ucfirst(strtolower($appointment['status'])) ?>
                            </span>
                        </div>
                    </div>

                    <?php if ($appointment['status'] === 'CONFIRMED'): ?>
                        <div class="mt-4 flex items-center space-x-3">
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="update_appointment_status">
                                <input type="hidden" name="appointment_id" value="<?= $appointment['id'] ?>">
                                <input type="hidden" name="status" value="IN_PROGRESS">
                                <button type="submit" class="medical-btn-secondary text-xs px-4 py-2">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Begin Treatment
                                </button>
                            </form>
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="update_appointment_status">
                                <input type="hidden" name="appointment_id" value="<?= $appointment['id'] ?>">
                                <input type="hidden" name="status" value="COMPLETED">
                                <button type="submit" class="medical-btn-primary text-xs px-4 py-2">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Complete Treatment
                                </button>
                            </form>
                        </div>
                    <?php elseif ($appointment['status'] === 'IN_PROGRESS'): ?>
                        <div class="mt-4">
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="update_appointment_status">
                                <input type="hidden" name="appointment_id" value="<?= $appointment['id'] ?>">
                                <input type="hidden" name="status" value="COMPLETED">
                                <button type="submit" class="medical-btn-primary text-xs px-4 py-2">
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Complete Treatment
                                    </button>
                                </form>
                            </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    <div class="bg-gradient-to-r from-redolence-green/5 to-redolence-blue/5 px-6 py-4 border-t border-redolence-green/10">
        <div class="flex justify-between items-center">
            <div class="text-sm text-gray-600">
                <svg class="w-4 h-4 inline mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Medical Schedule Management
            </div>
            <a href="<?= getBasePath() ?>/staff/appointments" class="text-sm font-medium text-redolence-green hover:text-redolence-blue transition-colors flex items-center">
                View All Patient Appointments
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>
</div>

<!-- Medical Quick Actions -->
<div class="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3">
    <a href="<?= getBasePath() ?>/staff/appointments"
       class="medical-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300 touch-action-manipulation focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2"
       aria-label="Navigate to patient appointments management">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:from-redolence-green/30 group-hover:to-redolence-green/20 transition-all duration-300" aria-hidden="true">
                    <svg class="h-5 w-5 sm:h-6 sm:w-6 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
            </div>
            <div class="ml-3 sm:ml-4 flex-1">
                <h3 class="text-sm sm:text-base font-semibold text-redolence-navy group-hover:text-redolence-green transition-colors">Patient Appointments</h3>
                <p class="text-xs sm:text-sm text-gray-600">Manage medical consultations</p>
            </div>
        </div>
    </a>

    <a href="<?= getBasePath() ?>/staff/schedule" class="medical-card p-6 group hover:scale-105 transition-all duration-300">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-xl flex items-center justify-center group-hover:from-redolence-blue/30 group-hover:to-redolence-blue/20 transition-all duration-300">
                    <svg class="h-6 w-6 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h3 class="text-base font-semibold text-redolence-navy group-hover:text-redolence-blue transition-colors">Medical Schedule</h3>
                <p class="text-sm text-gray-600">Treatment calendar & hours</p>
            </div>
        </div>
    </a>

    <a href="<?= getBasePath() ?>/staff/progressive-reports" class="medical-card p-6 group hover:scale-105 transition-all duration-300">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-xl flex items-center justify-center group-hover:from-purple-500/30 group-hover:to-purple-500/20 transition-all duration-300">
                    <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h3 class="text-base font-semibold text-redolence-navy group-hover:text-purple-600 transition-colors">Treatment Progress</h3>
                <p class="text-sm text-gray-600">Patient progress reports</p>
            </div>
        </div>
    </a>

    <a href="<?= getBasePath() ?>/staff/notifications" class="medical-card p-6 group hover:scale-105 transition-all duration-300">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-500/20 to-orange-500/10 rounded-xl flex items-center justify-center group-hover:from-orange-500/30 group-hover:to-orange-500/20 transition-all duration-300">
                    <svg class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h3 class="text-base font-semibold text-redolence-navy group-hover:text-orange-600 transition-colors">Medical Notifications</h3>
                <p class="text-sm text-gray-600">System alerts & updates</p>
            </div>
        </div>
    </a>

    <a href="<?= getBasePath() ?>/staff/earnings" class="medical-card p-6 group hover:scale-105 transition-all duration-300">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-emerald-500/20 to-emerald-500/10 rounded-xl flex items-center justify-center group-hover:from-emerald-500/30 group-hover:to-emerald-500/20 transition-all duration-300">
                    <svg class="h-6 w-6 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                </div>
            </div>
            <div class="ml-4">
                <h3 class="text-base font-semibold text-redolence-navy group-hover:text-emerald-600 transition-colors">Medical Earnings</h3>
                <p class="text-sm text-gray-600">Financial performance</p>
            </div>
        </div>
    </a>
</div>
<?php include __DIR__ . '/../includes/staff_footer.php'; ?>
