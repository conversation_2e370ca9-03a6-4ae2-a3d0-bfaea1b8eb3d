<?php
require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/customer_panel_functions.php';
require_once __DIR__ . '/../includes/booking_expiration.php';

// Run expiration check if needed
runExpirationCheckIfNeeded();

// Fix currency symbol if corrupted in database
try {
    $currentSymbol = getSetting('business', 'currency_symbol', null);
    if ($currentSymbol === '262145' || $currentSymbol === 262145) {
        setSetting('business', 'currency_symbol', 'TSH');
        error_log("Customer portal: Fixed corrupted currency_symbol (was 262145, now TSH)");
    }
} catch (Exception $e) {
    error_log("Customer portal: Error checking currency_symbol: " . $e->getMessage());
}

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'cancel_booking':
                    $bookingId = $_POST['booking_id'];
                    cancelCustomerBooking($_SESSION['user_id'], $bookingId);
                    $message = 'Booking cancelled successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get customer dashboard data
$customerId = $_SESSION['user_id'];
$dashboardData = getCustomerDashboardData($customerId);

$pageTitle = "Customer Dashboard";

// Include customer header
include __DIR__ . '/../includes/customer_header.php';
?>

<style>
/* Modern Dashboard Styles - Matching Profile Design */
.dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.welcome-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.3);
}

.welcome-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.welcome-avatar::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #49a75c, #2563eb, #49a75c);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2563eb);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.appointment-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.appointment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.appointment-card:hover::before {
    transform: scaleY(1);
}

.appointment-card:hover {
    transform: translateX(5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1e293b;
}

.status-confirmed {
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    color: white;
}

.status-completed {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-cancelled {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.status-in-progress {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.btn-primary {
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(73, 167, 92, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.notification {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.notification.success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #15803d;
}

.notification.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #dc2626;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    border-radius: 2px;
}

.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    animation: float 6s ease-in-out infinite;
}

.floating-circle:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-circle:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 10%;
    animation-delay: 2s;
}

.floating-circle:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.floating-circle:nth-child(4) {
    width: 100px;
    height: 100px;
    bottom: 30%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.quick-action-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: block;
    position: relative;
    overflow: hidden;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2563eb);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.quick-action-card:hover::before {
    transform: scaleX(1);
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.empty-state {
    text-align: center;
    padding: 3rem 2rem;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .dashboard-card {
        border-radius: 16px;
        margin-bottom: 1rem;
    }
    
    .welcome-avatar {
        width: 80px;
        height: 80px;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .appointment-card {
        padding: 1rem;
    }
    
    .btn-primary {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}
</style>

<!-- Floating Background Elements -->
<div class="floating-elements">
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
</div>

<div class="dashboard-container">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Welcome Header -->
        <div class="dashboard-card p-8 mb-8">
            <div class="flex flex-col lg:flex-row items-center lg:items-start gap-8">
                <!-- Avatar Section -->
                <div class="flex flex-col items-center">
                    <div class="welcome-avatar mb-4">
                        <?php if ($dashboardData['profile']['avatar']): ?>
                            <img src="<?= htmlspecialchars($dashboardData['profile']['avatar']) ?>" alt="Profile Picture" />
                        <?php else: ?>
                            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <div class="text-center">
                        <div class="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            Account Active
                        </div>
                    </div>
                </div>

                <!-- Welcome Info -->
                <div class="flex-1 text-center lg:text-left">
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                        Welcome back,
                        <span class="bg-gradient-to-r from-redolence-green to-blue-600 bg-clip-text text-transparent">
                            <?= htmlspecialchars(explode(' ', $dashboardData['profile']['name'])[0]) ?>
                        </span>
                    </h1>
                    <p class="text-xl text-gray-600 mb-6">
                        Ready for your next beauty transformation? Let's make it happen.
                    </p>
                    <div class="flex flex-wrap justify-center lg:justify-start gap-4 text-sm text-gray-500">
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9a1 1 0 01-1 1H5a1 1 0 01-1-1V7z" clip-rule="evenodd"/>
                            </svg>
                            Member since <?= date('F Y', strtotime($dashboardData['profile']['created_at'])) ?>
                        </div>
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                            <?= $dashboardData['loyaltyTier']['name'] ?> Member
                        </div>
                    </div>
                </div>

                <!-- CTA Button -->
                <div class="flex flex-col items-center lg:items-end gap-4">
                    <a href="<?= getBasePath() ?>/customer/book" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Book Appointment
                    </a>
                    <p class="text-sm text-gray-500">Start your beauty journey today</p>
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="notification <?= $messageType ?>">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <?php if ($messageType === 'success'): ?>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    <?php else: ?>
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    <?php endif; ?>
                </svg>
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card">
                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9a1 1 0 01-1 1H5a1 1 0 01-1-1V7z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2"><?= $dashboardData['stats']['completedBookings'] ?></div>
                <div class="text-sm text-gray-600">Total Visits</div>
            </div>

            <div class="stat-card">
                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">TSH <?= number_format($dashboardData['stats']['totalSpent'], 0) ?></div>
                <div class="text-sm text-gray-600">Total Spent</div>
            </div>

            <div class="stat-card">
                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2"><?= $dashboardData['loyaltyTier']['name'] ?></div>
                <div class="text-sm text-gray-600">Loyalty Tier</div>
            </div>

            <div class="stat-card">
                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">TSH <?= number_format($dashboardData['stats']['avgBookingValue'] ?? 0, 0) ?></div>
                <div class="text-sm text-gray-600">Avg. Booking Value</div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            
            <!-- Upcoming Appointments -->
            <div class="dashboard-card p-8">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="section-title">Upcoming Appointments</h3>
                    <a href="<?= getBasePath() ?>/customer/bookings" class="text-redolence-green hover:text-green-600 text-sm font-medium transition-colors duration-200">
                        View All →
                    </a>
                </div>

                <?php if (empty($dashboardData['upcomingBookings'])): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <h4 class="text-xl font-semibold text-gray-900 mb-2">No upcoming appointments</h4>
                        <p class="text-gray-600 mb-6">Ready to book your next beauty treatment?</p>
                        <a href="<?= getBasePath() ?>/customer/book" class="btn-primary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            Book Your First Appointment
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($dashboardData['upcomingBookings'] as $booking): ?>
                            <div class="appointment-card">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-lg"><?= htmlspecialchars($booking['service_name'] ?? 'Service Booking') ?></h4>
                                        <p class="text-sm text-gray-600">with <?= htmlspecialchars($booking['staff_name'] ?? 'Staff Member') ?></p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-semibold text-redolence-green"><?= date('M j, Y', strtotime($booking['date'])) ?></p>
                                        <p class="text-sm text-gray-600"><?= date('g:i A', strtotime($booking['start_time'])) ?></p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <span class="status-badge status-<?= strtolower($booking['status']) ?>">
                                        <?= $booking['status'] ?>
                                    </span>

                                    <?php if ($booking['status'] === 'PENDING' || $booking['status'] === 'CONFIRMED'): ?>
                                        <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to cancel this appointment?')">
                                            <input type="hidden" name="action" value="cancel_booking">
                                            <input type="hidden" name="booking_id" value="<?= $booking['id'] ?>">
                                            <button type="submit" class="btn-secondary">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                </svg>
                                                Cancel
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Bookings -->
            <div class="dashboard-card p-8">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="section-title">Recent Bookings</h3>
                    <a href="<?= getBasePath() ?>/customer/bookings" class="text-redolence-green hover:text-green-600 text-sm font-medium transition-colors duration-200">
                        View All →
                    </a>
                </div>

                <?php if (empty($dashboardData['recentBookings'])): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h4 class="text-xl font-semibold text-gray-900 mb-2">No booking history yet</h4>
                        <p class="text-gray-600">Your completed appointments will appear here</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach (array_slice($dashboardData['recentBookings'], 0, 5) as $booking): ?>
                            <div class="appointment-card">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900"><?= htmlspecialchars($booking['service_name'] ?? 'Service Booking') ?></h4>
                                        <p class="text-sm text-gray-600"><?= date('M j, Y', strtotime($booking['date'])) ?> • <?= htmlspecialchars($booking['staff_name'] ?? 'Staff Member') ?></p>
                                    </div>
                                    <div class="text-right ml-4">
                                        <p class="text-sm font-semibold text-redolence-green">TSH <?= number_format($booking['total_amount'], 0) ?></p>
                                        <span class="status-badge status-<?= strtolower($booking['status']) ?>">
                                            <?= $booking['status'] ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-card p-8">
            <h3 class="section-title">Quick Actions</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <a href="<?= getBasePath() ?>/customer/book" class="quick-action-card group">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-redolence-green to-green-600 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 group-hover:text-redolence-green transition-colors">Book Appointment</h4>
                            <p class="text-sm text-gray-600">Schedule your next treatment</p>
                        </div>
                    </div>
                </a>

                <a href="<?= getBasePath() ?>/customer/bookings" class="quick-action-card group">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">View Bookings</h4>
                            <p class="text-sm text-gray-600">Check your appointments</p>
                        </div>
                    </div>
                </a>

                <a href="<?= getBasePath() ?>/customer/profile" class="quick-action-card group">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">Manage Profile</h4>
                            <p class="text-sm text-gray-600">Update your information</p>
                        </div>
                    </div>
                </a>

                <a href="<?= getBasePath() ?>/customer/wishlist.php" class="quick-action-card group">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-pink-500 to-pink-600 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 group-hover:text-pink-600 transition-colors">Wishlist</h4>
                            <p class="text-sm text-gray-600">Your saved treatments</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced dashboard interactions
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll for notifications
    const notification = document.querySelector('.notification');
    if (notification) {
        notification.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Auto-hide notification after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }
    
    // Enhanced hover effects for cards
    const cards = document.querySelectorAll('.dashboard-card, .stat-card, .appointment-card, .quick-action-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = this.classList.contains('stat-card') ? 'translateY(-5px)' : 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Smooth animations on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all dashboard cards
    document.querySelectorAll('.dashboard-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
    
    // Form submission confirmation
    const cancelForms = document.querySelectorAll('form[onsubmit*="cancel"]');
    cancelForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Cancelling...
                `;
            }
        });
    });
    
    // Add loading state to book appointment buttons
    const bookButtons = document.querySelectorAll('a[href*="book"]');
    bookButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Add subtle loading effect
            this.style.opacity = '0.8';
            this.style.transform = 'scale(0.98)';
        });
    });
});

// Modern toast notification system (reused from profile)
function showToast(message, type = 'success') {
    const existingToast = document.getElementById('toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    const toast = document.createElement('div');
    toast.id = 'toast';
    toast.className = `fixed top-4 right-4 z-50 flex items-center p-4 rounded-xl shadow-lg transform translate-x-full transition-all duration-300 ${
        type === 'success' 
            ? 'bg-green-500 text-white' 
            : 'bg-red-500 text-white'
    }`;
    
    toast.innerHTML = `
        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
            ${type === 'success' 
                ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>'
                : '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>'
            }
        </svg>
        <span class="font-medium">${message}</span>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(full)';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}
</script>

<?php include __DIR__ . '/../includes/customer_footer.php'; ?>