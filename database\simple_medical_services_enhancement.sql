-- Simple Medical Services Enhancement for Redolence Medi Aesthetics
-- This migration adds medical treatment fields without breaking existing structure

USE redolence;

-- Add new fields to existing services table (non-breaking changes)
ALTER TABLE services 
ADD COLUMN IF NOT EXISTS session_frequency VARCHAR(255) DEFAULT NULL COMMENT 'e.g., "Once a week for 4 weeks"',
ADD COLUMN IF NOT EXISTS technology_used VARCHAR(255) DEFAULT NULL COMMENT 'Technology/equipment used in treatment',
ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE COMMENT 'Featured treatment badge',
ADD COLUMN IF NOT EXISTS popular BOOLEAN DEFAULT FALSE COMMENT 'Popular treatment badge', 
ADD COLUMN IF NOT EXISTS new_treatment BOOLEAN DEFAULT FALSE COMMENT 'New treatment badge',
ADD COLUMN IF NOT EXISTS sort_order INT DEFAULT 0 COMMENT 'Display order for treatments';

-- Add indexes for the new fields
ALTER TABLE services 
ADD INDEX IF NOT EXISTS idx_services_featured (featured),
ADD INDEX IF NOT EXISTS idx_services_popular (popular),
ADD INDEX IF NOT EXISTS idx_services_new_treatment (new_treatment),
ADD INDEX IF NOT EXISTS idx_services_sort_order (sort_order);

-- Make price and duration optional for flexible pricing (TSH - To be discussed)
ALTER TABLE services 
MODIFY COLUMN price DECIMAL(10,2) DEFAULT NULL COMMENT 'Price (can be NULL for TSH pricing)',
MODIFY COLUMN duration INT DEFAULT NULL COMMENT 'Duration in minutes (can be NULL for variable duration)';

-- Update the example service with the new structure
INSERT INTO services (
    id,
    name,
    description,
    price,
    duration,
    session_frequency,
    technology_used,
    featured,
    popular,
    is_active,
    sort_order,
    created_at,
    updated_at
) VALUES (
    UUID(),
    'DEFINE & TONE BODY RF EMSCULPT',
    '<h3>HIFEM WITH RF</h3>
<p>The HIFEM energy stimulates rapid muscle contractions that build muscle to improve muscle tone and strength.</p>
<p>The Radio Frequency delivers heat to the underlying tissues and warms the muscle up in preparation for the intense contractions, which is a great tool to heat and melt fat permanently.</p>
<h4>Benefits:</h4>
<ul>
<li>✔️ Loose Inches</li>
<li>✔️ Get ABS</li>
<li>✔️ Tighten Skin</li>
</ul>
<p><strong>Only 30 mins per session</strong></p>',
    1200.00,
    30,
    'Once a week for 4 weeks',
    'HIFEM with RF Technology',
    TRUE,
    TRUE,
    TRUE,
    1,
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    description = VALUES(description),
    session_frequency = VALUES(session_frequency),
    technology_used = VALUES(technology_used),
    featured = VALUES(featured),
    popular = VALUES(popular);

-- Clean up old category data (optional - can be run later)
-- UPDATE services SET category = NULL, category_id = NULL, subcategory_id = NULL WHERE 1=1;

-- Create a view for easy medical treatment management
CREATE OR REPLACE VIEW medical_treatments AS
SELECT 
    id,
    name,
    description,
    price,
    duration,
    session_frequency,
    technology_used,
    featured,
    popular,
    new_treatment,
    sort_order,
    image,
    is_active,
    created_at,
    updated_at,
    CASE 
        WHEN price IS NULL THEN 'TSH'
        ELSE CONCAT('$', FORMAT(price, 2))
    END as display_price,
    CASE 
        WHEN duration IS NULL THEN 'Variable'
        ELSE CONCAT(duration, ' mins')
    END as display_duration
FROM services 
WHERE is_active = 1
ORDER BY sort_order ASC, name ASC;

-- Add some sample medical treatments for testing
INSERT INTO services (
    id, name, description, price, duration, session_frequency, technology_used, 
    featured, popular, new_treatment, sort_order, is_active
) VALUES 
(
    UUID(),
    'HYDRAFACIAL MD',
    '<h3>Advanced Skin Resurfacing Treatment</h3>
<p>A non-invasive, multi-step treatment that combines the benefits of next-level hydradermabrasion, a chemical peel, automated painless extractions and a special delivery of antioxidants, hyaluronic acid and peptides.</p>
<h4>Benefits:</h4>
<ul>
<li>✔️ Instant Glow</li>
<li>✔️ Deep Cleansing</li>
<li>✔️ Hydration Boost</li>
<li>✔️ Fine Line Reduction</li>
</ul>
<p><strong>No downtime required</strong></p>',
    250.00,
    60,
    'Monthly for optimal results',
    'HydraFacial MD Technology',
    TRUE,
    TRUE,
    FALSE,
    2,
    TRUE
),
(
    UUID(),
    'LASER HAIR REMOVAL',
    '<h3>Permanent Hair Reduction</h3>
<p>Advanced laser technology for safe and effective permanent hair reduction on all skin types.</p>
<h4>Benefits:</h4>
<ul>
<li>✔️ Permanent Results</li>
<li>✔️ All Skin Types</li>
<li>✔️ Minimal Discomfort</li>
<li>✔️ Quick Sessions</li>
</ul>
<p><strong>Sessions vary by area</strong></p>',
    NULL,
    NULL,
    'Every 6-8 weeks',
    'Diode Laser Technology',
    FALSE,
    TRUE,
    FALSE,
    3,
    TRUE
),
(
    UUID(),
    'BOTOX COSMETIC',
    '<h3>Wrinkle Reduction Treatment</h3>
<p>FDA-approved neurotoxin treatment for reducing fine lines and wrinkles.</p>
<h4>Benefits:</h4>
<ul>
<li>✔️ Smooth Wrinkles</li>
<li>✔️ Prevent New Lines</li>
<li>✔️ Natural Results</li>
<li>✔️ Quick Procedure</li>
</ul>
<p><strong>Results last 3-4 months</strong></p>',
    NULL,
    15,
    'Every 3-4 months',
    'Botulinum Toxin Type A',
    TRUE,
    FALSE,
    FALSE,
    4,
    TRUE
);

-- Update the rebranding progress
-- This helps track what we've accomplished
INSERT INTO admin_notes (note, created_at) VALUES 
('Medical Services Enhancement: Added rich text support, treatment badges, and medical-specific fields without breaking existing structure', NOW())
ON DUPLICATE KEY UPDATE note = VALUES(note), created_at = NOW();
