<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php');
}

// Get report ID
$reportId = $_GET['id'] ?? '';
if (!$reportId) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();
$progressiveReportEntry = new ProgressiveReportEntry();

// Get report details
$report = $progressiveReport->getById($reportId);
if (!$report) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Check if staff can access this report
$staffId = $_SESSION['user_id'];
if (!$progressiveReport->canAccess($reportId, $staffId, 'STAFF')) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Get report entries
$entries = $progressiveReportEntry->getByReportId($reportId);

$pageTitle = "Medical Progress Report - " . $report['client_name'];
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Progress Report Back Navigation -->
<div class="mb-6">
    <a href="<?= getBasePath() ?>/staff/progressive-reports"
       class="inline-flex items-center text-sm font-medium text-redolence-green hover:text-redolence-blue transition-colors focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2 rounded-lg px-3 py-2">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        Back to Medical Progress Reports
    </a>
</div>

<!-- Medical Progress Report Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between mobile-stack">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <div class="w-20 h-20 bg-gradient-to-r from-redolence-green to-redolence-blue rounded-full flex items-center justify-center">
                    <span class="text-2xl font-bold text-white">
                        <?= strtoupper(substr($report['client_name'], 0, 2)) ?>
                    </span>
                </div>
            </div>
            <div class="ml-6 flex-1">
                <h1 class="text-3xl font-bold text-redolence-navy">
                    <?= htmlspecialchars($report['title']) ?>
                </h1>
                <p class="mt-2 text-xl text-redolence-green font-semibold">
                    Patient: <?= htmlspecialchars($report['client_name']) ?>
                </p>
                <div class="mt-4 flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-2 sm:space-y-0 text-sm text-gray-600">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        <span class="break-all"><?= htmlspecialchars($report['client_email']) ?></span>
                    </div>
                    <?php if ($report['client_phone']): ?>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <?= htmlspecialchars($report['client_phone']) ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if ($report['description']): ?>
                    <div class="mt-4 p-4 bg-redolence-green/5 rounded-xl border border-redolence-green/20">
                        <h4 class="text-sm font-semibold text-redolence-navy mb-2">Medical Treatment Description:</h4>
                        <p class="text-gray-700"><?= htmlspecialchars($report['description']) ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="mt-6 sm:mt-0 flex flex-col gap-4">
            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium border
                <?php if ($report['status'] === 'ACTIVE'): ?>
                    bg-green-100 text-green-800 border-green-200
                <?php elseif ($report['status'] === 'COMPLETED'): ?>
                    bg-blue-100 text-blue-800 border-blue-200
                <?php else: ?>
                    bg-gray-100 text-gray-800 border-gray-200
                <?php endif; ?>">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <?= ucfirst(strtolower($report['status'])) ?> Treatment
            </span>
            <div class="text-xs text-gray-500 text-center">
                <svg class="w-4 h-4 mr-1 inline text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                HIPAA Compliant Medical Record
            </div>
        </div>
    </div>
</div>

<!-- Medical Progress Statistics -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <div class="text-3xl font-bold text-redolence-navy"><?= number_format($report['total_entries']) ?></div>
                <div class="text-sm text-gray-600 font-medium">Medical Progress Entries</div>
            </div>
        </div>
    </div>
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <div class="text-3xl font-bold text-redolence-navy">
                    <?= $report['last_entry_date'] ? date('M j', strtotime($report['last_entry_date'])) : 'N/A' ?>
                </div>
                <div class="text-sm text-gray-600 font-medium">Last Medical Entry</div>
            </div>
        </div>
    </div>
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <div class="text-lg font-bold text-redolence-navy"><?= htmlspecialchars($report['created_by_name']) ?></div>
                <div class="text-sm text-gray-600 font-medium">Medical Professional</div>
            </div>
        </div>
    </div>
</div>

<!-- Medical Treatment Timeline -->
<div class="medical-card p-8">
    <div class="flex items-center justify-between mb-8">
        <h2 class="text-2xl font-bold text-redolence-navy">Medical Treatment Timeline</h2>
        <div class="text-sm text-gray-500 flex items-center">
            <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            HIPAA Compliant Medical Records
        </div>
    </div>

    <?php if (empty($entries)): ?>
        <div class="text-center py-16">
            <div class="w-20 h-20 bg-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-10 h-10 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
            </div>
            <h3 class="text-xl font-bold text-redolence-navy mb-3">No Medical Entries Yet</h3>
            <p class="text-gray-600">No medical treatment entries have been added to this patient's progress report.</p>
        </div>
    <?php else: ?>
        <div class="space-y-8">
            <?php foreach ($entries as $entry): ?>
                <div class="relative">
                    <!-- Medical Timeline Connector -->
                    <div class="absolute left-6 top-12 bottom-0 w-0.5 bg-gradient-to-b from-redolence-green to-redolence-blue"></div>

                    <div class="flex items-start">
                        <!-- Medical Timeline Icon -->
                        <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-redolence-green to-redolence-blue rounded-full flex items-center justify-center z-10">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>

                        <!-- Medical Entry Content -->
                        <div class="ml-6 flex-1 bg-white border border-redolence-green/20 rounded-xl p-6 shadow-lg">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-redolence-navy mb-3">
                                        <?= htmlspecialchars($entry['treatment']) ?>
                                    </h3>
                                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                            </svg>
                                            <?= date('F j, Y', strtotime($entry['entry_date'])) ?>
                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                            </svg>
                                            Dr. <?= htmlspecialchars($entry['created_by_name']) ?>
                                        </div>
                                            <?php if ($entry['appointment_id']): ?>
                                                <span>•</span>
                                                <span>Appointment: <?= date('M j', strtotime($entry['appointment_date'])) ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="prose prose-sm max-w-none">
                                            <p class="text-gray-300"><?= nl2br(htmlspecialchars($entry['description'])) ?></p>
                                            <?php if ($entry['notes']): ?>
                                                <div class="mt-3 p-3 bg-secondary-700 rounded-lg border-l-4 border-blue-500">
                                                    <p class="text-sm text-salon-gold font-medium mb-1">Additional Notes:</p>
                                                    <p class="text-sm text-gray-300"><?= nl2br(htmlspecialchars($entry['notes'])) ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php if ($entry['created_by'] === $staffId): ?>
                                        <div class="ml-4">
                                            <a href="<?= getBasePath() ?>/staff/progressive-reports/edit-entry.php?id=<?= $entry['id'] ?>"
                                               class="inline-flex items-center px-3 py-1 border border-salon-gold text-salon-gold hover:bg-salon-gold hover:text-black rounded text-sm transition-all duration-300">
                                                Edit
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
