<?php
session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Get notifications with pagination and filters
$page = $_GET['page'] ?? 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$category = $_GET['category'] ?? 'all';
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';
$sort = $_GET['sort'] ?? 'newest';

try {
    $notifications = getStaffNotifications($_SESSION['user_id'], [
        'limit' => $limit,
        'offset' => $offset,
        'category' => $category,
        'status' => $status,
        'search' => $search,
        'sort' => $sort
    ]);

    $totalCount = getStaffNotificationsCount($_SESSION['user_id'], [
        'category' => $category,
        'status' => $status,
        'search' => $search
    ]);

    $totalPages = ceil($totalCount / $limit);

    // Get category counts
    $categoryCounts = getNotificationCategoryCounts($_SESSION['user_id']);
} catch (Exception $e) {
    $error = $e->getMessage();
    $notifications = [];
    $totalCount = 0;
    $totalPages = 0;
    $categoryCounts = [
        'total' => 0,
        'unread' => 0,
        'read' => 0,
        'categories' => []
    ];
}

// Medical helper functions for badge classes
function getMedicalCategoryBadgeClass($category) {
    switch ($category) {
        case 'BOOKING':
            return 'bg-blue-100 text-blue-800 border border-blue-200';
        case 'STAFF':
            return 'bg-purple-100 text-purple-800 border border-purple-200';
        case 'SYSTEM':
            return 'bg-gray-100 text-gray-800 border border-gray-200';
        default:
            return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
}

function getMedicalPriorityBadgeClass($priority) {
    switch ($priority) {
        case 'HIGH':
            return 'bg-red-100 text-red-800 border border-red-200';
        case 'MEDIUM':
            return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
        case 'LOW':
            return 'bg-green-100 text-green-800 border border-green-200';
        default:
            return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
}

$pageTitle = "Medical Notifications Center";
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Notifications Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-redolence-navy">Medical Notification <span class="text-redolence-green">Center</span></h1>
            <p class="mt-2 text-lg text-gray-600">Manage and organize your medical system notifications and alerts</p>
            <div class="mt-3 flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                </svg>
                Professional Medical Notification Management
            </div>
        </div>
        <div class="mt-6 sm:mt-0 flex space-x-4">
            <button onclick="markAllAsRead()" class="medical-btn-primary px-6 py-3">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Mark All Read
            </button>
            <button onclick="clearAllNotifications()" class="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl transition-colors">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Clear All
            </button>
        </div>
    </div>
</div>

<!-- Medical Notification Stats Overview -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <!-- Total Medical Notifications -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-2xl flex items-center justify-center">
                    <svg class="h-8 w-8 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Total Medical Notifications</dt>
                    <dd class="text-3xl font-bold text-redolence-navy"><?= number_format($totalCount) ?></dd>
                    <dd class="text-xs text-redolence-green font-medium">System Alerts</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Unread Medical Notifications -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-gradient-to-br from-red-500/20 to-red-500/10 rounded-2xl flex items-center justify-center">
                    <svg class="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                    </svg>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Unread Medical Alerts</dt>
                    <dd class="text-3xl font-bold text-red-500"><?= number_format($categoryCounts['unread'] ?? 0) ?></dd>
                    <dd class="text-xs text-red-500 font-medium">Requires Attention</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Read Medical Notifications -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500/20 to-green-500/10 rounded-2xl flex items-center justify-center">
                    <svg class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Read Medical Alerts</dt>
                    <dd class="text-3xl font-bold text-green-500"><?= number_format($categoryCounts['read'] ?? 0) ?></dd>
                    <dd class="text-xs text-green-500 font-medium">Acknowledged</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Medical Categories -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-2xl flex items-center justify-center">
                    <svg class="h-8 w-8 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Medical Categories</dt>
                    <dd class="text-3xl font-bold text-redolence-blue"><?= count($categoryCounts['categories'] ?? []) ?></dd>
                    <dd class="text-xs text-redolence-blue font-medium">Alert Types</dd>
                </dl>
            </div>
        </div>
    </div>
</div>

<!-- Medical Filters and Search -->
<div class="medical-card p-8 mb-8">
    <h3 class="text-xl font-bold text-redolence-navy mb-6">Medical Notification Filters</h3>
    <form id="filterForm" class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <!-- Medical Search -->
        <div class="relative">
            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                   class="w-full bg-white border-2 border-gray-200 rounded-xl px-4 py-3 text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                   placeholder="Search medical notifications...">
            <button type="submit" class="absolute right-3 top-3.5 text-gray-500 hover:text-redolence-green transition-colors">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </button>
        </div>

        <!-- Medical Category Filter -->
        <div>
            <select name="category" class="w-full bg-white border-2 border-gray-200 rounded-xl px-4 py-3 text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                <option value="all" <?= $category === 'all' ? 'selected' : '' ?>>All Medical Categories</option>
                <option value="BOOKING" <?= $category === 'BOOKING' ? 'selected' : '' ?>>Patient Appointments</option>
                <option value="STAFF" <?= $category === 'STAFF' ? 'selected' : '' ?>>Medical Staff Updates</option>
                <option value="SYSTEM" <?= $category === 'SYSTEM' ? 'selected' : '' ?>>Medical System</option>
            </select>
        </div>

        <!-- Medical Status Filter -->
        <div>
            <select name="status" class="w-full bg-white border-2 border-gray-200 rounded-xl px-4 py-3 text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Medical Status</option>
                <option value="unread" <?= $status === 'unread' ? 'selected' : '' ?>>Unread Alerts</option>
                <option value="read" <?= $status === 'read' ? 'selected' : '' ?>>Read Alerts</option>
            </select>
        </div>

        <!-- Medical Sort -->
        <div>
            <select name="sort" class="w-full bg-white border-2 border-gray-200 rounded-xl px-4 py-3 text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                <option value="newest" <?= $sort === 'newest' ? 'selected' : '' ?>>Newest Medical Alerts</option>
                <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>Oldest Medical Alerts</option>
                <option value="priority" <?= $sort === 'priority' ? 'selected' : '' ?>>Medical Priority</option>
            </select>
        </div>
    </form>
</div>

<!-- Medical Notifications List -->
<div class="medical-card mb-8">
    <!-- Medical Bulk Actions -->
    <div class="p-6 border-b border-redolence-green/20">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <input type="checkbox" id="selectAll" class="rounded border-redolence-green/30 text-redolence-green focus:ring-redolence-green w-5 h-5">
                <label for="selectAll" class="text-sm font-medium text-redolence-navy">Select All Medical Notifications</label>
            </div>
            <div class="flex space-x-3">
                <button onclick="bulkMarkAsRead()" class="medical-btn-primary px-4 py-2 text-sm">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Mark Selected as Read
                </button>
                <button onclick="bulkDelete()" class="px-4 py-2 text-sm bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete Selected
                </button>
            </div>
        </div>
    </div>

    <!-- Medical Notifications -->
    <div class="divide-y divide-redolence-green/10">
        <?php if (empty($notifications)): ?>
            <div class="p-12 text-center">
                <div class="w-24 h-24 bg-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-redolence-navy mb-2">No Medical Notifications Found</h3>
                <p class="text-gray-600">Try adjusting your medical filters or search terms</p>
            </div>
        <?php else: ?>
            <?php foreach ($notifications as $notification): ?>
                <div class="notification-item p-6 hover:bg-redolence-green/5 transition-colors <?= !$notification['is_read'] ? 'bg-blue-50 border-l-4 border-redolence-green' : '' ?>" data-notification-id="<?= $notification['id'] ?>">
                    <div class="flex items-start space-x-6">
                        <!-- Medical Checkbox -->
                        <div class="flex-shrink-0">
                            <input type="checkbox" value="<?= $notification['id'] ?>"
                                   class="notification-checkbox rounded border-redolence-green/30 text-redolence-green focus:ring-redolence-green w-5 h-5">
                        </div>

                        <!-- Notification Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <?= getMedicalCategoryBadgeClass($notification['category']) ?>">
                                        <?= $notification['category'] ?>
                                    </span>
                                    <?php if ($notification['priority'] !== 'MEDIUM'): ?>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <?= getMedicalPriorityBadgeClass($notification['priority']) ?>">
                                            <?= $notification['priority'] ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-400"><?= $notification['time_ago'] ?></span>
                                    <div class="flex space-x-1">
                                        <?php if (!$notification['is_read']): ?>
                                            <button onclick="markAsRead('<?= $notification['id'] ?>')" 
                                                    class="p-1 text-gray-400 hover:text-white transition-colors" 
                                                    title="Mark as read">
                                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </button>
                                        <?php endif; ?>
                                        <button onclick="deleteNotification('<?= $notification['id'] ?>')" 
                                                class="p-1 text-gray-400 hover:text-red-500 transition-colors" 
                                                title="Delete">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <h3 class="mt-1 text-sm font-medium text-white"><?= htmlspecialchars($notification['title']) ?></h3>
                            <p class="mt-1 text-sm text-gray-300"><?= htmlspecialchars($notification['message']) ?></p>
                            <?php if ($notification['action_url']): ?>
                                <div class="mt-2">
                                    <a href="<?= getBasePath() . $notification['action_url'] ?>" 
                                       class="text-sm text-salon-gold hover:text-gold-light transition-colors">
                                        View Details →
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <div class="px-4 py-3 border-t border-secondary-700">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?= $page - 1 ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                           class="relative inline-flex items-center px-4 py-2 border border-secondary-700 text-sm font-medium rounded-md text-gray-400 bg-secondary-800 hover:bg-secondary-700">
                            Previous
                        </a>
                    <?php endif; ?>
                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?= $page + 1 ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                           class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-700 text-sm font-medium rounded-md text-gray-400 bg-secondary-800 hover:bg-secondary-700">
                            Next
                        </a>
                    <?php endif; ?>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-400">
                            Showing <span class="font-medium"><?= ($offset + 1) ?></span> to 
                            <span class="font-medium"><?= min($offset + $limit, $totalCount) ?></span> of 
                            <span class="font-medium"><?= $totalCount ?></span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-700 bg-secondary-800 text-sm font-medium text-gray-400 hover:bg-secondary-700">
                                    <span class="sr-only">Previous</span>
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                    </svg>
                                </a>
                            <?php endif; ?>

                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);

                            for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                                <a href="?page=<?= $i ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-secondary-700 text-sm font-medium <?= $i === $page ? 'bg-salon-gold text-black' : 'bg-secondary-800 text-gray-400 hover:bg-secondary-700' ?>">
                                    <?= $i ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= $page + 1 ?>&category=<?= $category ?>&status=<?= $status ?>&search=<?= urlencode($search) ?>&sort=<?= $sort ?>" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-700 bg-secondary-800 text-sm font-medium text-gray-400 hover:bg-secondary-700">
                                    <span class="sr-only">Next</span>
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Notification Details Modal -->
<div id="notificationModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-secondary-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="modal-title">
                            Notification Details
                        </h3>
                        <div class="mt-4">
                            <div id="modalContent" class="text-sm text-gray-300"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-secondary-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-salon-gold text-base font-medium text-black hover:bg-gold-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Notification Management Functions
function markAsRead(notificationId) {
    // IMMEDIATE UI UPDATE: Update notification appearance
    const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
    if (notificationElement) {
        notificationElement.style.opacity = '0.7';
        const markButton = notificationElement.querySelector('button[onclick*="markAsRead"]');
        if (markButton) {
            markButton.style.display = 'none';
        }
        console.log('⚡ IMMEDIATE: Notification marked as read in UI');
    }

    // IMMEDIATE COUNTER UPDATE: Decrease header counter
    if (typeof window.decreaseNotificationCounter === 'function') {
        window.decreaseNotificationCounter();
        console.log('⚡ IMMEDIATE: Header counter decreased');
    }

    const basePath = '<?= getBasePath() ?>';

    makeRequest(`${basePath}/api/staff/notifications.php`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            id: notificationId,
            is_read: true
        })
    })
    .then(() => {
        console.log('✅ API: Notification marked as read successfully');
        showNotification('Notification marked as read', 'success');

        // Update header counter via API call
        if (typeof window.refreshNotificationCounter === 'function') {
            window.refreshNotificationCounter();
        }

        refreshNotifications();
    })
    .catch(error => {
        console.error('Failed to mark notification as read:', error);
        showNotification('Failed to mark notification as read', 'error');

        // REVERT UI CHANGES on error
        if (notificationElement) {
            notificationElement.style.opacity = '1';
            const markButton = notificationElement.querySelector('button[onclick*="markAsRead"]');
            if (markButton) {
                markButton.style.display = 'block';
            }
        }

        // Revert counter
        if (typeof window.refreshNotificationCounter === 'function') {
            window.refreshNotificationCounter();
        }
    });
}

function deleteNotification(notificationId) {
    if (!confirm('Are you sure you want to delete this notification?')) {
        return;
    }

    // IMMEDIATE UI UPDATE: Remove notification from DOM instantly
    const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
    if (notificationElement) {
        notificationElement.style.opacity = '0.5';
        notificationElement.style.pointerEvents = 'none';
        console.log('⚡ IMMEDIATE: Notification hidden from UI');
    }

    // IMMEDIATE COUNTER UPDATE: Decrease header counter if notification was unread
    const isUnread = notificationElement && notificationElement.querySelector('.bg-red-500');
    if (isUnread && typeof window.decreaseNotificationCounter === 'function') {
        window.decreaseNotificationCounter();
        console.log('⚡ IMMEDIATE: Header counter decreased');
    }

    const basePath = '<?= getBasePath() ?>';

    makeRequest(`${basePath}/api/staff/notifications.php?id=${notificationId}`, {
        method: 'DELETE'
    })
    .then(() => {
        console.log('✅ API: Notification deleted successfully');
        showNotification('Notification deleted', 'success');

        // Remove from DOM completely
        if (notificationElement) {
            notificationElement.remove();
        }

        // Update header counter via API call
        if (typeof window.refreshNotificationCounter === 'function') {
            window.refreshNotificationCounter();
        }

        // Refresh the page data
        refreshNotifications();
    })
    .catch(error => {
        console.error('Failed to delete notification:', error);
        showNotification('Failed to delete notification', 'error');

        // REVERT UI CHANGES on error
        if (notificationElement) {
            notificationElement.style.opacity = '1';
            notificationElement.style.pointerEvents = 'auto';
        }

        // Revert counter if it was decreased
        if (isUnread && typeof window.refreshNotificationCounter === 'function') {
            window.refreshNotificationCounter();
        }
    });
}

function bulkMarkAsRead() {
    const selectedIds = getSelectedNotificationIds();
    if (selectedIds.length === 0) {
        showNotification('Please select notifications to mark as read', 'info');
        return;
    }

    const basePath = '<?= getBasePath() ?>';
    
    Promise.all(selectedIds.map(id =>
        makeRequest(`${basePath}/api/staff/notifications.php`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: id,
                is_read: true
            })
        })
    ))
    .then(() => {
        showNotification('Selected notifications marked as read', 'success');
        refreshNotifications();
    })
    .catch(error => {
        console.error('Failed to mark notifications as read:', error);
        showNotification('Failed to mark notifications as read', 'error');
    });
}

async function bulkDelete() {
    const selectedIds = getSelectedNotificationIds();
    if (selectedIds.length === 0) {
        showNotification('Please select notifications to delete', 'info');
        return;
    }

    const confirmed = await medicalConfirm(
        `Are you sure you want to delete ${selectedIds.length} medical notification(s)? This action cannot be undone and will permanently remove these alerts from your medical system.`,
        'Delete Medical Notifications'
    );

    if (!confirmed) {
        return;
    }

    const basePath = '<?= getBasePath() ?>';
    
    Promise.all(selectedIds.map(id =>
        makeRequest(`${basePath}/api/staff/notifications.php?id=${id}`, {
            method: 'DELETE'
        })
    ))
    .then(() => {
        showNotification('Selected notifications deleted', 'success');
        refreshNotifications();
    })
    .catch(error => {
        console.error('Failed to delete notifications:', error);
        showNotification('Failed to delete notifications', 'error');
    });
}

function getSelectedNotificationIds() {
    return Array.from(document.querySelectorAll('.notification-checkbox:checked'))
        .map(checkbox => checkbox.value);
}

// Select All Checkbox
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.notification-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
});

// Filter Form
document.getElementById('filterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const params = new URLSearchParams(formData);
    window.location.href = '?' + params.toString();
});

// Modal Functions
function showModal(content) {
    const modal = document.getElementById('notificationModal');
    const modalContent = document.getElementById('modalContent');
    modalContent.innerHTML = content;
    modal.classList.remove('hidden');
}

function closeModal() {
    const modal = document.getElementById('notificationModal');
    modal.classList.add('hidden');
}

// Refresh notifications function
function refreshNotifications() {
    console.log('🔄 Refreshing notifications page...');
    // For now, just reload the page - can be optimized later
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Close modal when clicking outside
    document.getElementById('notificationModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Close modal when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
});

// Function to mark all notifications as read
async function markAllAsRead() {
    const confirmed = await medicalConfirm(
        'Are you sure you want to mark all medical notifications as read? This will update the status of all unread alerts in your medical system.',
        'Mark All Medical Notifications as Read'
    );

    if (!confirmed) return;
    
    fetch('<?= getBasePath() ?>/api/staff/notifications.php', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ mark_all_read: true })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            medicalError(`Medical System Error: ${data.error}`);
            return;
        }
        medicalSuccess('All medical notifications have been marked as read successfully.');
        // Reload the page to show updated status
        setTimeout(() => window.location.reload(), 1500);
    })
    .catch(error => {
        console.error('Error:', error);
        medicalError('Failed to mark medical notifications as read. Please try again.');
    });
}

// Function to clear all notifications
async function clearAllNotifications() {
    const confirmed = await medicalConfirm(
        'Are you sure you want to delete all medical notifications? This action cannot be undone and will permanently remove all alerts from your medical system.',
        'Delete All Medical Notifications'
    );

    if (!confirmed) return;
    
    fetch('<?= getBasePath() ?>/api/staff/notifications.php', {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(data.error);
            return;
        }
        // Reload the page to show updated status
        window.location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to delete notifications');
    });
}
</script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?> 