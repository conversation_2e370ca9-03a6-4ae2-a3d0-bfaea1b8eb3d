<?php
/**
 * Database Setup Script for Redolence Medi Aesthetics
 * Run this script to create the database and check connectivity
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'redolence';

echo "<h1>Redolence Medi Aesthetics - Database Setup</h1>";

try {
    // First, connect without specifying database to create it
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Connected to MySQL server successfully!</p>";
    
    // Create database if it doesn't exist
    $sql = "CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
    $pdo->exec($sql);
    
    echo "<p>✅ Database '$database' created successfully!</p>";
    
    // Now connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Connected to '$database' database successfully!</p>";
    
    // Check if tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p>⚠️ Database is empty. You need to import the redolence.sql file.</p>";
        echo "<h3>Next Steps:</h3>";
        echo "<ol>";
        echo "<li>Open phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
        echo "<li>Select the 'redolence' database</li>";
        echo "<li>Click 'Import' tab</li>";
        echo "<li>Choose the 'redolence.sql' file from your project folder</li>";
        echo "<li>Click 'Go' to import</li>";
        echo "</ol>";
    } else {
        echo "<p>✅ Database contains " . count($tables) . " tables:</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        // Test a basic query
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            echo "<p>✅ Database is working! Found {$result['count']} users in the system.</p>";
            
            echo "<h3>🎉 Setup Complete!</h3>";
            echo "<p>Your database is ready. You can now:</p>";
            echo "<ul>";
            echo "<li><a href='index.php'>Visit the homepage</a></li>";
            echo "<li><a href='admin/'>Access the admin panel</a></li>";
            echo "</ul>";
            
        } catch (Exception $e) {
            echo "<p>⚠️ Database exists but may need data import: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL service is running</li>";
    echo "<li>Check if MySQL is running on port 3306</li>";
    echo "<li>Verify your database credentials</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><small>Database Setup Script for Redolence Medi Aesthetics</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
}
h1 {
    color: #49a75c;
    border-bottom: 2px solid #49a75c;
    padding-bottom: 10px;
}
p {
    margin: 10px 0;
}
ul, ol {
    margin: 10px 0 10px 20px;
}
a {
    color: #5894d2;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
