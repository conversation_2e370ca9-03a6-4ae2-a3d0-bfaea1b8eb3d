<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php');
}

// Get report ID
$reportId = $_GET['report_id'] ?? '';
if (!$reportId) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();
$progressiveReportEntry = new ProgressiveReportEntry();

// Get report details
$report = $progressiveReport->getById($reportId);
if (!$report) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Check if staff can access this report
$staffId = $_SESSION['user_id'];
if (!$progressiveReport->canAccess($reportId, $staffId, 'STAFF')) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Get appointments for this client (for dropdown selection)
$appointments = $database->fetchAll("
    SELECT b.*, s.name as service_name
    FROM bookings b
    LEFT JOIN services s ON b.service_id = s.id
    WHERE b.user_id = ? AND b.staff_id = ?
    ORDER BY b.date DESC, b.start_time DESC
", [$report['client_id'], $_SESSION['user_id']]);

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $treatment = trim($_POST['treatment'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    $entryDate = $_POST['entry_date'] ?? '';
    $appointmentId = $_POST['appointment_id'] ?? null;

    $errors = [];

    // Validation
    if (empty($treatment)) {
        $errors[] = 'Medical treatment description is required.';
    }

    if (empty($notes)) {
        $errors[] = 'Medical progress notes are required.';
    }

    if (empty($entryDate)) {
        $errors[] = 'Treatment date is required.';
    } elseif (!strtotime($entryDate)) {
        $errors[] = 'Please enter a valid treatment date.';
    }

    // Create entry if no errors
    if (empty($errors)) {
        try {
            $entryId = generateUUID();
            $database->query("
                INSERT INTO progressive_report_entries (id, report_id, appointment_id, treatment, description, notes, entry_date, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ", [$entryId, $reportId, $appointmentId, $treatment, $treatment, $notes, $entryDate, $_SESSION['user_id']]);

            $message = 'Medical progress entry added successfully!';
            $messageType = 'success';

            // Redirect to view the report
            header('Location: ' . getBasePath() . '/staff/progressive-reports/view.php?id=' . $reportId);
            exit;
        } catch (Exception $e) {
            $errors[] = 'Failed to add medical progress entry. Please try again.';
            error_log("Progressive Report Entry Error: " . $e->getMessage());
        }
    }

    if (!empty($errors)) {
        $message = implode('<br>', $errors);
        $messageType = 'error';
    }
}

$pageTitle = "Add Medical Progress Entry - " . $report['client_name'];
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Progress Report Back Navigation -->
<div class="mb-6">
    <a href="<?= getBasePath() ?>/staff/progressive-reports/view.php?id=<?= $reportId ?>"
       class="inline-flex items-center text-sm font-medium text-redolence-green hover:text-redolence-blue transition-colors focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2 rounded-lg px-3 py-2">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        Back to Medical Progress Report
    </a>
</div>

<!-- Medical Progress Entry Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <div class="w-16 h-16 bg-gradient-to-r from-redolence-green to-redolence-blue rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
            </div>
        </div>
        <div class="ml-6">
            <h1 class="text-3xl font-bold text-redolence-navy">Add Medical Progress <span class="text-redolence-green">Entry</span></h1>
            <p class="mt-2 text-xl text-gray-600">Document treatment progress for <?= htmlspecialchars($report['client_name']) ?></p>
            <div class="mt-3 flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                Secure Medical Documentation
            </div>
        </div>
    </div>
</div>

<!-- Patient Information -->
<div class="medical-card p-6 mb-8">
    <h3 class="text-lg font-bold text-redolence-navy mb-4">Patient Information</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Patient Name</label>
            <div class="text-lg font-semibold text-redolence-navy"><?= htmlspecialchars($report['client_name']) ?></div>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Report Title</label>
            <div class="text-lg font-semibold text-redolence-navy"><?= htmlspecialchars($report['title']) ?></div>
        </div>
    </div>
</div>

<!-- Alert Messages -->
<?php if ($message): ?>
    <div class="mb-6">
        <div class="medical-card p-4 <?= $messageType === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50' ?>">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 <?= $messageType === 'error' ? 'text-red-500' : 'text-green-500' ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <?php if ($messageType === 'error'): ?>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    <?php else: ?>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    <?php endif; ?>
                </svg>
                <span class="<?= $messageType === 'error' ? 'text-red-700' : 'text-green-700' ?>"><?= $message ?></span>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Medical Progress Entry Form -->
<div class="medical-card p-8">
    <form method="POST" class="space-y-8">
        <!-- Appointment Selection -->
        <?php if (!empty($appointments)): ?>
        <div>
            <label for="appointment_id" class="block text-sm font-bold text-redolence-navy mb-3">
                Related Appointment (Optional)
            </label>
            <select name="appointment_id"
                    id="appointment_id"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
                <option value="">Select an appointment (optional)</option>
                <?php foreach ($appointments as $appointment): ?>
                    <option value="<?= $appointment['id'] ?>" <?= ($_POST['appointment_id'] ?? '') == $appointment['id'] ? 'selected' : '' ?>>
                        <?= date('M j, Y', strtotime($appointment['date'])) ?> - <?= htmlspecialchars($appointment['service_name']) ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <p class="mt-2 text-sm text-gray-600">Link this entry to a specific appointment if applicable.</p>
        </div>
        <?php endif; ?>

        <!-- Treatment Date -->
        <div>
            <label for="entry_date" class="block text-sm font-bold text-redolence-navy mb-3">
                Treatment Date *
            </label>
            <input type="date"
                   name="entry_date"
                   id="entry_date"
                   required
                   value="<?= htmlspecialchars($_POST['entry_date'] ?? date('Y-m-d')) ?>"
                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
            <p class="mt-2 text-sm text-gray-600">Date when the treatment was performed.</p>
        </div>

        <!-- Medical Treatment -->
        <div>
            <label for="treatment" class="block text-sm font-bold text-redolence-navy mb-3">
                Medical Treatment *
            </label>
            <input type="text"
                   name="treatment"
                   id="treatment"
                   required
                   value="<?= htmlspecialchars($_POST['treatment'] ?? '') ?>"
                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                   placeholder="e.g., Botox injection, Facial treatment, Laser therapy">
            <p class="mt-2 text-sm text-gray-600">Specify the medical treatment or procedure performed.</p>
        </div>

        <!-- Medical Progress Notes -->
        <div>
            <label for="notes" class="block text-sm font-bold text-redolence-navy mb-3">
                Medical Progress Notes *
            </label>
            <textarea name="notes"
                      id="notes"
                      rows="8"
                      required
                      class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all resize-none"
                      placeholder="Document patient progress, treatment outcomes, observations, side effects, patient response, and any follow-up recommendations..."><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
            <p class="mt-2 text-sm text-gray-600">Detailed medical notes about patient progress, treatment response, and clinical observations.</p>
        </div>
        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-8 border-t border-gray-200">
            <a href="<?= getBasePath() ?>/staff/progressive-reports/view.php?id=<?= $reportId ?>"
               class="medical-btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
                Cancel
            </a>
            <button type="submit"
                    class="medical-btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                Add Medical Entry
            </button>
        </div>
    </form>
</div>

<!-- Medical Documentation Guidelines -->
<div class="medical-card p-6 mt-8">
    <h3 class="text-lg font-bold text-redolence-navy mb-4">Medical Documentation Guidelines</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-700">
        <div>
            <h4 class="font-semibold text-redolence-green mb-2">Treatment Documentation</h4>
            <ul class="space-y-1">
                <li>• Specify exact procedure performed</li>
                <li>• Include treatment areas and techniques</li>
                <li>• Document patient response and tolerance</li>
            </ul>
        </div>
        <div>
            <h4 class="font-semibold text-redolence-green mb-2">Progress Notes</h4>
            <ul class="space-y-1">
                <li>• Record clinical observations</li>
                <li>• Note any side effects or complications</li>
                <li>• Include follow-up recommendations</li>
            </ul>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
