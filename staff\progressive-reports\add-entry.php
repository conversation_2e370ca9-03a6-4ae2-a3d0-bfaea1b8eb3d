<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php');
}

// Get report ID
$reportId = $_GET['report_id'] ?? '';
if (!$reportId) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();
$progressiveReportEntry = new ProgressiveReportEntry();

// Get report details
$report = $progressiveReport->getById($reportId);
if (!$report) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Check if staff can access this report
$staffId = $_SESSION['user_id'];
if (!$progressiveReport->canAccess($reportId, $staffId, 'STAFF')) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Get appointments for this client (for dropdown selection)
$appointments = $database->fetchAll("
    SELECT b.*, s.name as service_name
    FROM bookings b
    LEFT JOIN services s ON b.service_id = s.id
    WHERE b.user_id = ? AND b.staff_id = ?
    ORDER BY b.date DESC, b.start_time DESC
", [$report['client_id'], $_SESSION['user_id']]);

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $entryData = [
        'appointment_id' => $_POST['appointment_id'] ?? null,
        'entry_date' => $_POST['entry_date'] ?? '',
        'treatment' => trim($_POST['treatment'] ?? ''),
        'description' => trim($_POST['description'] ?? ''),
        'notes' => trim($_POST['notes'] ?? '')
    ];

    if (empty($entryData['entry_date']) || empty($entryData['treatment']) || empty($entryData['description'])) {
        $message = 'Entry date, treatment, and description are required.';
        $messageType = 'error';
    } else {
        $result = $progressiveReportEntry->create($reportId, $entryData);

        if ($result) {
            header('Location: ' . getBasePath() . '/staff/progressive-reports/view.php?id=' . $reportId);
            exit;
        } else {
            $message = 'Failed to create report entry. Please try again.';
            $messageType = 'error';
        }
    }
}

$pageTitle = "Add Medical Progress Entry - " . $report['client_name'];
include __DIR__ . '/../../includes/staff_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Back Navigation -->
            <div class="mb-6">
                <a href="<?= getBasePath() ?>/staff/appointments" 
                   class="inline-flex items-center text-sm font-medium text-salon-gold hover:text-gold-light">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Back to Appointments
                </a>
            </div>

            <!-- Page Header -->
            <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 p-8 hover-lift">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-4">
                        <div class="w-12 h-12 bg-salon-gold rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-white font-serif">Add Progressive Report <span class="text-salon-gold">Entry</span></h1>
                        <p class="mt-2 text-lg text-gray-300">Document treatment progress for <?= htmlspecialchars($appointment['customer_name']) ?></p>
                    </div>
                </div>
            </div>

            <!-- Appointment Details -->
            <div class="bg-secondary-800 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-white mb-4">Appointment Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-gray-400">Patient:</span>
                        <div class="text-white font-medium"><?= htmlspecialchars($appointment['customer_name']) ?></div>
                    </div>
                    <div>
                        <span class="text-gray-400">Service:</span>
                        <div class="text-white font-medium"><?= htmlspecialchars($appointment['service_name'] ?? 'N/A') ?></div>
                    </div>
                    <div>
                        <span class="text-gray-400">Date:</span>
                        <div class="text-white font-medium"><?= date('F j, Y', strtotime($appointment['date'])) ?></div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="mb-6">
                    <div class="p-4 rounded-lg <?= $messageType === 'error' ? 'bg-red-900 border border-red-700 text-red-100' : 'bg-green-900 border border-green-700 text-green-100' ?>">
                        <?= htmlspecialchars($message) ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Add Entry Form -->
            <div class="bg-secondary-800 rounded-lg p-8">
                <form method="POST" class="space-y-6">
                    <!-- Treatment -->
                    <div>
                        <label for="treatment" class="block text-sm font-medium text-gray-300 mb-2">
                            Treatment Performed <span class="text-red-400">*</span>
                        </label>
                        <input type="text" 
                               name="treatment" 
                               id="treatment" 
                               required
                               value="<?= htmlspecialchars($_POST['treatment'] ?? $appointment['service_name'] ?? '') ?>"
                               class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                               placeholder="e.g., Botox injection, Facial treatment, Laser therapy">
                        <p class="mt-1 text-sm text-gray-400">Describe the specific treatment or procedure performed</p>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">
                            Treatment Description <span class="text-red-400">*</span>
                        </label>
                        <textarea name="description" 
                                  id="description" 
                                  required
                                  rows="6" 
                                  class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                                  placeholder="Provide detailed information about the treatment, patient response, areas treated, techniques used, etc."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                        <p class="mt-1 text-sm text-gray-400">Document the treatment details, patient response, and any observations</p>
                    </div>

                    <!-- Additional Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-300 mb-2">
                            Additional Notes
                        </label>
                        <textarea name="notes" 
                                  id="notes" 
                                  rows="4" 
                                  class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                                  placeholder="Any additional notes, follow-up recommendations, or observations..."><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                        <p class="mt-1 text-sm text-gray-400">Optional additional notes or recommendations</p>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <a href="<?= getBasePath() ?>/staff/appointments" 
                           class="px-6 py-3 border border-secondary-600 text-gray-300 hover:text-white hover:border-secondary-500 rounded-lg font-medium transition-all duration-300">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-3 bg-salon-gold text-black hover:bg-gold-light rounded-lg font-medium transition-all duration-300">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Add Entry
                        </button>
                    </div>
                </form>
            </div>

            <!-- Help Section -->
            <div class="mt-8 bg-secondary-900/50 backdrop-blur-sm border border-secondary-700 rounded-xl p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Progressive Report Guidelines</h3>
                <div class="space-y-3 text-sm text-gray-300">
                    <p>• <strong>Treatment:</strong> Specify the exact procedure or service performed</p>
                    <p>• <strong>Description:</strong> Include details about technique, areas treated, patient response, and results</p>
                    <p>• <strong>Notes:</strong> Add any follow-up recommendations, side effects, or special observations</p>
                    <p>• <strong>Documentation:</strong> This entry will be added to the patient's progressive report for future reference</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-resize textareas
document.addEventListener('DOMContentLoaded', function() {
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });
});
</script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
