<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php');
}

// Initialize database connection
global $database;

// Get parameters
$appointmentId = $_GET['appointment_id'] ?? '';
$reportId = $_GET['report_id'] ?? '';

if (!$appointmentId) {
    header('Location: ' . getBasePath() . '/staff/appointments');
    exit;
}

// Get appointment details
$appointment = $database->fetch("
    SELECT b.*, u.name as customer_name, u.email as customer_email, s.name as service_name
    FROM bookings b
    LEFT JOIN users u ON b.user_id = u.id
    LEFT JOIN services s ON b.service_id = s.id
    WHERE b.id = ? AND b.staff_id = ?
", [$appointmentId, $_SESSION['user_id']]);

if (!$appointment) {
    header('Location: ' . getBasePath() . '/staff/appointments');
    exit;
}

// Get or create progressive report
$report = null;
if ($reportId) {
    // Get existing report
    $report = $database->fetch("
        SELECT * FROM progressive_reports
        WHERE id = ? AND client_id = ?
    ", [$reportId, $appointment['user_id']]);

    if (!$report) {
        header('Location: ' . getBasePath() . '/staff/appointments');
        exit;
    }
} else {
    // Check if customer has existing report
    $report = $database->fetch("
        SELECT * FROM progressive_reports
        WHERE client_id = ?
        ORDER BY created_at DESC
        LIMIT 1
    ", [$appointment['user_id']]);

    if (!$report) {
        // Redirect to create new report
        header('Location: ' . getBasePath() . '/staff/progressive-reports/create.php?customer_id=' . $appointment['user_id'] . '&appointment_id=' . $appointmentId);
        exit;
    }
}

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $treatment = trim($_POST['treatment'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    $entryDate = $_POST['entry_date'] ?? '';

    $errors = [];

    // Validation
    if (empty($treatment)) {
        $errors[] = 'Medical treatment description is required.';
    }

    if (empty($notes)) {
        $errors[] = 'Medical progress notes are required.';
    }

    if (empty($entryDate)) {
        $errors[] = 'Treatment date is required.';
    } elseif (!strtotime($entryDate)) {
        $errors[] = 'Please enter a valid treatment date.';
    }

    // Create entry if no errors
    if (empty($errors)) {
        try {
            $entryId = generateUUID();
            $database->query("
                INSERT INTO progressive_report_entries (id, report_id, appointment_id, treatment, notes, entry_date, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ", [$entryId, $report['id'], $appointmentId, $treatment, $notes, $entryDate, $_SESSION['user_id']]);

            $message = 'Medical progress entry added successfully!';
            $messageType = 'success';

            // Redirect to view the report
            header('Location: ' . getBasePath() . '/staff/progressive-reports/view.php?id=' . $report['id']);
            exit;

        } catch (Exception $e) {
            $errors[] = 'Failed to add medical progress entry. Please try again.';
            error_log("Progressive Report Entry Error: " . $e->getMessage());
        }
    }
}

$pageTitle = "Add Medical Progress Entry - " . $appointment['customer_name'];
include __DIR__ . '/../../includes/staff_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Back Navigation -->
            <div class="mb-6">
                <a href="<?= getBasePath() ?>/staff/appointments" 
                   class="inline-flex items-center text-sm font-medium text-salon-gold hover:text-gold-light">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Back to Appointments
                </a>
            </div>

            <!-- Page Header -->
            <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 p-8 hover-lift">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-4">
                        <div class="w-12 h-12 bg-salon-gold rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-white font-serif">Add Progressive Report <span class="text-salon-gold">Entry</span></h1>
                        <p class="mt-2 text-lg text-gray-300">Document treatment progress for <?= htmlspecialchars($appointment['customer_name']) ?></p>
                    </div>
                </div>
            </div>

            <!-- Appointment Details -->
            <div class="bg-secondary-800 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-white mb-4">Appointment Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-gray-400">Patient:</span>
                        <div class="text-white font-medium"><?= htmlspecialchars($appointment['customer_name']) ?></div>
                    </div>
                    <div>
                        <span class="text-gray-400">Service:</span>
                        <div class="text-white font-medium"><?= htmlspecialchars($appointment['service_name'] ?? 'N/A') ?></div>
                    </div>
                    <div>
                        <span class="text-gray-400">Date:</span>
                        <div class="text-white font-medium"><?= date('F j, Y', strtotime($appointment['date'])) ?></div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="mb-6">
                    <div class="p-4 rounded-lg <?= $messageType === 'error' ? 'bg-red-900 border border-red-700 text-red-100' : 'bg-green-900 border border-green-700 text-green-100' ?>">
                        <?= htmlspecialchars($message) ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Add Entry Form -->
            <div class="bg-secondary-800 rounded-lg p-8">
                <form method="POST" class="space-y-6">
                    <!-- Treatment -->
                    <div>
                        <label for="treatment" class="block text-sm font-medium text-gray-300 mb-2">
                            Treatment Performed <span class="text-red-400">*</span>
                        </label>
                        <input type="text" 
                               name="treatment" 
                               id="treatment" 
                               required
                               value="<?= htmlspecialchars($_POST['treatment'] ?? $appointment['service_name'] ?? '') ?>"
                               class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                               placeholder="e.g., Botox injection, Facial treatment, Laser therapy">
                        <p class="mt-1 text-sm text-gray-400">Describe the specific treatment or procedure performed</p>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">
                            Treatment Description <span class="text-red-400">*</span>
                        </label>
                        <textarea name="description" 
                                  id="description" 
                                  required
                                  rows="6" 
                                  class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                                  placeholder="Provide detailed information about the treatment, patient response, areas treated, techniques used, etc."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                        <p class="mt-1 text-sm text-gray-400">Document the treatment details, patient response, and any observations</p>
                    </div>

                    <!-- Additional Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-300 mb-2">
                            Additional Notes
                        </label>
                        <textarea name="notes" 
                                  id="notes" 
                                  rows="4" 
                                  class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                                  placeholder="Any additional notes, follow-up recommendations, or observations..."><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                        <p class="mt-1 text-sm text-gray-400">Optional additional notes or recommendations</p>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <a href="<?= getBasePath() ?>/staff/appointments" 
                           class="px-6 py-3 border border-secondary-600 text-gray-300 hover:text-white hover:border-secondary-500 rounded-lg font-medium transition-all duration-300">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-3 bg-salon-gold text-black hover:bg-gold-light rounded-lg font-medium transition-all duration-300">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Add Entry
                        </button>
                    </div>
                </form>
            </div>

            <!-- Help Section -->
            <div class="mt-8 bg-secondary-900/50 backdrop-blur-sm border border-secondary-700 rounded-xl p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Progressive Report Guidelines</h3>
                <div class="space-y-3 text-sm text-gray-300">
                    <p>• <strong>Treatment:</strong> Specify the exact procedure or service performed</p>
                    <p>• <strong>Description:</strong> Include details about technique, areas treated, patient response, and results</p>
                    <p>• <strong>Notes:</strong> Add any follow-up recommendations, side effects, or special observations</p>
                    <p>• <strong>Documentation:</strong> This entry will be added to the patient's progressive report for future reference</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-resize textareas
document.addEventListener('DOMContentLoaded', function() {
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });
});
</script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
