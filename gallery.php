<?php
/**
 * Enhanced Gallery Page - Medical Aesthetics Results & Clinic Gallery
 * Modern grid layout with lightbox functionality and before/after comparisons
 */

require_once __DIR__ . '/config/app.php';

// Sample gallery data (in a real application, this would come from the database)
$galleryItems = [
    [
        'id' => 1,
        'title' => 'Facial Rejuvenation Results',
        'category' => 'before-after',
        'treatment' => 'HydraFacial + Chemical Peel',
        'before_image' => 'https://images.unsplash.com/photo-1594824804732-5f8fcaf009d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=90&fm=webp',
        'after_image' => 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=90&fm=webp',
        'description' => '6-week transformation showing improved skin texture, reduced fine lines, and enhanced radiance.',
        'duration' => '6 weeks',
        'sessions' => 4
    ],
    [
        'id' => 2,
        'title' => 'Skin Texture Improvement',
        'category' => 'before-after',
        'treatment' => 'Microneedling + LED Therapy',
        'before_image' => 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=90&fm=webp',
        'after_image' => 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=90&fm=webp',
        'description' => 'Dramatic improvement in skin texture and pore appearance after microneedling treatment series.',
        'duration' => '8 weeks',
        'sessions' => 3
    ],
    [
        'id' => 3,
        'title' => 'Treatment Room 1',
        'category' => 'clinic',
        'image' => 'https://images.unsplash.com/photo-1629909613654-28e377c37b09?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'description' => 'Our state-of-the-art treatment room equipped with the latest medical aesthetics technology.'
    ],
    [
        'id' => 4,
        'title' => 'Reception Area',
        'category' => 'clinic',
        'image' => 'https://images.unsplash.com/photo-1629909613654-28e377c37b09?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'description' => 'Welcome to our luxurious reception area designed for comfort and relaxation.'
    ],
    [
        'id' => 5,
        'title' => 'Anti-Aging Results',
        'category' => 'before-after',
        'treatment' => 'Botox + Dermal Fillers',
        'before_image' => 'https://images.unsplash.com/photo-**********-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'after_image' => 'https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'description' => 'Natural-looking anti-aging results with subtle enhancement and wrinkle reduction.',
        'duration' => '2 weeks',
        'sessions' => 1
    ],
    [
        'id' => 6,
        'title' => 'Consultation Room',
        'category' => 'clinic',
        'image' => 'https://images.unsplash.com/photo-1631815589968-fdb09a223b1e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'description' => 'Private consultation room where we discuss your beauty goals and treatment plans.'
    ]
];

$pageTitle = "Gallery - Treatment Results & Clinic Tour - Redolence Medi Aesthetics";
$pageDescription = "View our impressive before and after treatment results and take a virtual tour of our modern medical aesthetics clinic. See the transformations achieved with our professional treatments.";

include __DIR__ . '/includes/header.php';
?>

<!-- Enhanced Gallery Styles -->
<style>
/* Modern Typography */
.text-luxury {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.text-medical {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

.text-display {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
}

/* Enhanced Gradient Effects */
.gradient-text {
    background: linear-gradient(135deg, #49a75c 0%, #5894d2 50%, #d4af37 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Gallery Grid Animations */
.gallery-item {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 1.5rem;
}

.gallery-item:hover::before {
    opacity: 1;
}

/* Before/After Slider */
.before-after-container {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
}

.before-after-slider {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
    cursor: ew-resize;
}

.before-image, .after-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.after-image {
    clip-path: polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%);
    transition: clip-path 0.3s ease;
}

.slider-handle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: white;
    border: 3px solid #49a75c;
    border-radius: 50%;
    cursor: ew-resize;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.slider-handle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: #49a75c;
    border-radius: 50%;
}

.slider-line {
    position: absolute;
    top: 0;
    left: 50%;
    width: 2px;
    height: 100%;
    background: white;
    transform: translateX(-50%);
    z-index: 5;
}

/* Lightbox Styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    max-width: 90vw;
    max-height: 90vh;
    position: relative;
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.lightbox.active .lightbox-content {
    transform: scale(1);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* Filter Buttons */
.filter-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.filter-btn:hover::before {
    left: 100%;
}

.filter-btn.active {
    background: linear-gradient(135deg, #49a75c, #5894d2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}

/* Floating Elements */
.floating-element {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Enhanced Glass Effect */
.glass-card {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
</style>

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-redolence-green/20">
    <!-- Background Image with Overlay -->
    <div class="absolute inset-0 z-0">
        <img src="https://images.unsplash.com/photo-1629909613654-28e377c37b09?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=100" 
             alt="Medical Aesthetics Gallery" 
             class="w-full h-full object-cover opacity-30">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-900/80 via-gray-800/70 to-redolence-green/30"></div>
    </div>

    <!-- Floating Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="floating-element absolute top-1/4 left-1/4 w-64 h-64 bg-redolence-green/10 rounded-full blur-3xl"></div>
        <div class="floating-element absolute bottom-1/4 right-1/4 w-96 h-96 bg-redolence-blue/10 rounded-full blur-3xl" style="animation-delay: 1s;"></div>
        <div class="floating-element absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-128 h-128 bg-white/5 rounded-full blur-3xl" style="animation-delay: 2s;"></div>
    </div>

    <!-- Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-6 text-center">
        <div class="mb-8">
            <div class="inline-flex items-center bg-white/10 text-white px-6 py-3 rounded-full text-sm font-semibold mb-6 backdrop-blur-sm border border-white/20">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
                Treatment Results & Clinic Gallery
            </div>
            <h1 class="text-4xl md:text-7xl font-display font-bold text-white mb-6 leading-tight">
                See The
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Transformation
                </span>
            </h1>
            <p class="text-xl md:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed mb-8">
                Explore our gallery of remarkable before and after results, and take a virtual tour of our state-of-the-art medical aesthetics clinic.
            </p>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-redolence-green mb-2"><?= count(array_filter($galleryItems, fn($item) => $item['category'] === 'before-after')) ?></div>
                <div class="text-white/80 text-sm md:text-base">Before & After</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-yellow-400 mb-2"><?= count(array_filter($galleryItems, fn($item) => $item['category'] === 'clinic')) ?></div>
                <div class="text-white/80 text-sm md:text-base">Clinic Photos</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-red-400 mb-2">100%</div>
                <div class="text-white/80 text-sm md:text-base">Satisfaction</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-green-400 mb-2">500+</div>
                <div class="text-white/80 text-sm md:text-base">Happy Clients</div>
            </div>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#gallery" class="inline-flex items-center justify-center bg-redolence-green hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Gallery
            </a>
            <a href="<?= getBasePath() ?>/customer/book" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                </svg>
                Book Consultation
            </a>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section id="gallery" class="py-20 bg-gradient-to-br from-redolence-gray to-white">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-display font-bold text-gray-900 mb-6">
                <span class="gradient-text">Results Gallery</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Witness the remarkable transformations achieved through our advanced medical aesthetics treatments. Each image tells a story of renewed confidence and enhanced natural beauty.
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-redolence-green to-redolence-blue mx-auto rounded-full mt-6"></div>
        </div>

        <!-- Filter Buttons -->
        <div class="flex flex-wrap justify-center gap-4 mb-12">
            <button class="filter-btn active px-6 py-3 rounded-xl font-semibold transition-all duration-300 bg-white border-2 border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white relative overflow-hidden" data-filter="all">
                All Gallery
            </button>
            <button class="filter-btn px-6 py-3 rounded-xl font-semibold transition-all duration-300 bg-white border-2 border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white relative overflow-hidden" data-filter="before-after">
                Before & After
            </button>
            <button class="filter-btn px-6 py-3 rounded-xl font-semibold transition-all duration-300 bg-white border-2 border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white relative overflow-hidden" data-filter="clinic">
                Clinic Tour
            </button>
        </div>

        <!-- Gallery Grid -->
        <div class="gallery-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($galleryItems as $item): ?>
                <div class="gallery-item relative bg-white rounded-3xl shadow-xl overflow-hidden" data-category="<?= $item['category'] ?>" onclick="openLightbox(<?= $item['id'] ?>)">
                    <?php if ($item['category'] === 'before-after'): ?>
                        <!-- Before/After Slider -->
                        <div class="before-after-container">
                            <div class="before-after-slider" data-item-id="<?= $item['id'] ?>">
                                <img src="<?= $item['before_image'] ?>" alt="Before" class="before-image">
                                <img src="<?= $item['after_image'] ?>" alt="After" class="after-image">
                                <div class="slider-line"></div>
                                <div class="slider-handle"></div>

                                <!-- Before/After Labels -->
                                <div class="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    Before
                                </div>
                                <div class="absolute top-4 right-4 bg-redolence-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    After
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Regular Image -->
                        <div class="relative h-64">
                            <img src="<?= $item['image'] ?>" alt="<?= htmlspecialchars($item['title']) ?>" class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        </div>
                    <?php endif; ?>

                    <!-- Content -->
                    <div class="p-6">
                        <div class="mb-4">
                            <h3 class="text-xl font-display font-bold text-gray-900 mb-2">
                                <?= htmlspecialchars($item['title']) ?>
                            </h3>
                            <?php if (isset($item['treatment'])): ?>
                                <p class="text-redolence-blue font-semibold">
                                    <?= htmlspecialchars($item['treatment']) ?>
                                </p>
                            <?php endif; ?>
                        </div>

                        <p class="text-gray-600 leading-relaxed mb-4">
                            <?= htmlspecialchars($item['description']) ?>
                        </p>

                        <?php if ($item['category'] === 'before-after'): ?>
                            <!-- Treatment Details -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                    </svg>
                                    <?= htmlspecialchars($item['duration']) ?>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <?= $item['sessions'] ?> sessions
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- View Button -->
                        <button class="w-full bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View Details
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-16">
            <div class="glass-card rounded-3xl p-8 max-w-4xl mx-auto">
                <h3 class="text-3xl font-display font-bold text-gray-900 mb-4">
                    Ready for Your Transformation?
                </h3>
                <p class="text-xl text-gray-600 mb-8">
                    Join hundreds of satisfied clients who have achieved their beauty goals with our professional medical aesthetics treatments.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/customer/book"
                       class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                        </svg>
                        Book Your Consultation
                    </a>
                    <a href="<?= getBasePath() ?>/services"
                       class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-redolence-blue px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                        </svg>
                        View All Services
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Lightbox Modal -->
<div id="lightbox" class="lightbox">
    <div class="lightbox-content">
        <button class="lightbox-close" onclick="closeLightbox()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        <div id="lightbox-content-inner">
            <!-- Content will be populated by JavaScript -->
        </div>
    </div>
</div>

<script>
// Gallery data for JavaScript
const galleryData = <?= json_encode($galleryItems) ?>;

// Before/After Slider Functionality
function initBeforeAfterSliders() {
    const sliders = document.querySelectorAll('.before-after-slider');

    sliders.forEach(slider => {
        const afterImage = slider.querySelector('.after-image');
        const handle = slider.querySelector('.slider-handle');
        const line = slider.querySelector('.slider-line');
        let isDragging = false;

        function updateSlider(x) {
            const rect = slider.getBoundingClientRect();
            const percentage = Math.max(0, Math.min(100, ((x - rect.left) / rect.width) * 100));

            afterImage.style.clipPath = `polygon(${percentage}% 0%, 100% 0%, 100% 100%, ${percentage}% 100%)`;
            handle.style.left = `${percentage}%`;
            line.style.left = `${percentage}%`;
        }

        // Mouse events
        slider.addEventListener('mousedown', (e) => {
            isDragging = true;
            updateSlider(e.clientX);
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                updateSlider(e.clientX);
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events for mobile
        slider.addEventListener('touchstart', (e) => {
            isDragging = true;
            updateSlider(e.touches[0].clientX);
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging) {
                e.preventDefault();
                updateSlider(e.touches[0].clientX);
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });

        // Prevent click event from bubbling when dragging
        slider.addEventListener('click', (e) => {
            if (!isDragging) {
                updateSlider(e.clientX);
            }
        });
    });
}

// Gallery Filter Functionality
function initGalleryFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filter = button.dataset.filter;

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // Filter gallery items
            galleryItems.forEach(item => {
                const category = item.dataset.category;

                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 100);
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
}

// Lightbox Functionality
function openLightbox(itemId) {
    const item = galleryData.find(i => i.id === itemId);
    if (!item) return;

    const lightbox = document.getElementById('lightbox');
    const contentInner = document.getElementById('lightbox-content-inner');

    let content = '';

    if (item.category === 'before-after') {
        content = `
            <div class="p-8">
                <div class="mb-6">
                    <h2 class="text-3xl font-display font-bold text-gray-900 mb-2">${item.title}</h2>
                    <p class="text-xl text-redolence-blue font-semibold">${item.treatment}</p>
                </div>

                <div class="before-after-container mb-6">
                    <div class="before-after-slider" style="height: 400px;">
                        <img src="${item.before_image}" alt="Before" class="before-image">
                        <img src="${item.after_image}" alt="After" class="after-image">
                        <div class="slider-line"></div>
                        <div class="slider-handle"></div>

                        <div class="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            Before
                        </div>
                        <div class="absolute top-4 right-4 bg-redolence-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                            After
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 rounded-2xl p-6">
                        <h3 class="font-semibold text-gray-900 mb-4">Treatment Details</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-redolence-green mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-700">Duration: ${item.duration}</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-redolence-green mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-gray-700">Sessions: ${item.sessions}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6">
                        <h3 class="font-semibold text-gray-900 mb-4">Results</h3>
                        <p class="text-gray-700 leading-relaxed">${item.description}</p>
                    </div>
                </div>

                <div class="text-center">
                    <a href="<?= getBasePath() ?>/customer/book"
                       class="inline-flex items-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                        </svg>
                        Book Similar Treatment
                    </a>
                </div>
            </div>
        `;
    } else {
        content = `
            <div class="p-8">
                <div class="mb-6">
                    <h2 class="text-3xl font-display font-bold text-gray-900 mb-2">${item.title}</h2>
                </div>

                <div class="mb-6">
                    <img src="${item.image}" alt="${item.title}" class="w-full h-96 object-cover rounded-2xl">
                </div>

                <div class="mb-6">
                    <p class="text-xl text-gray-600 leading-relaxed">${item.description}</p>
                </div>

                <div class="text-center">
                    <a href="<?= getBasePath() ?>/contact"
                       class="inline-flex items-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Visit Our Clinic
                    </a>
                </div>
            </div>
        `;
    }

    contentInner.innerHTML = content;
    lightbox.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Re-initialize before/after sliders in lightbox
    if (item.category === 'before-after') {
        setTimeout(() => {
            initBeforeAfterSliders();
        }, 100);
    }
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.remove('active');
    document.body.style.overflow = '';
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initBeforeAfterSliders();
    initGalleryFilters();

    // Close lightbox when clicking outside
    document.getElementById('lightbox').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLightbox();
        }
    });

    // Close lightbox with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe gallery items for scroll animations
    document.querySelectorAll('.gallery-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
