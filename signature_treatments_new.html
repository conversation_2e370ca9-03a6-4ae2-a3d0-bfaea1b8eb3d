<!-- Signature Treatments Section - Redesigned -->
<section class="py-24 bg-gradient-to-br from-white to-gray-50">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-redolence-green/10 text-redolence-green px-6 py-3 rounded-full text-sm font-semibold mb-6">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                Signature Treatments
            </div>
            <h2 class="text-5xl font-bold text-gray-900 mb-6">
                Our Most
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Requested
                </span>
                Procedures
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Experience our most popular medical aesthetic treatments, trusted by hundreds of satisfied clients for exceptional results.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($featuredServices as $index => $service): ?>
                <div class="signature-treatment-card group relative bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 border border-gray-100 hover:border-redolence-green/30">
                    <!-- Background Gradient Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/5 via-transparent to-redolence-blue/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    
                    <!-- Popular Badge -->
                    <?php if ($index === 0): ?>
                        <div class="absolute top-4 left-4 z-20">
                            <div class="bg-gradient-to-r from-redolence-green to-green-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg animate-pulse">
                                <i class="fas fa-fire mr-1"></i>
                                MOST POPULAR
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Image Section with Enhanced Effects -->
                    <div class="relative h-56 overflow-hidden">
                        <?php if ($service['image']): ?>
                            <?php 
                            // Handle both uploaded files and external URLs
                            $imageSrc = $service['image'];
                            if (!filter_var($imageSrc, FILTER_VALIDATE_URL)) {
                                // If not a URL, treat as uploaded file and prepend uploads path
                                $imageSrc = getBasePath() . '/uploads/' . ltrim($imageSrc, '/');
                            }
                            ?>
                            <div class="image-skeleton w-full h-full absolute inset-0"></div>
                            <img src="<?= htmlspecialchars($imageSrc) ?>" 
                                 alt="<?= htmlspecialchars($service['name']) ?>" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 lazy-image" 
                                 loading="lazy" 
                                 decoding="async" 
                                 onload="this.classList.add('loaded', 'fade-in-loaded'); this.previousElementSibling.style.display='none';" 
                                 onerror="this.previousElementSibling.style.display='block';">
                        <?php else: ?>
                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span class="text-3xl text-redolence-green">✨</span>
                                    </div>
                                    <div class="text-gray-700 font-medium"><?= htmlspecialchars($service['category']) ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Gradient Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                        
                        <!-- Floating Action Button -->
                        <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                            <button onclick="toggleWishlist('service', '<?= $service['id'] ?>', this)"
                                    class="wishlist-btn w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 hover:text-red-500 hover:bg-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110"
                                    data-item-type="service"
                                    data-item-id="<?= $service['id'] ?>"
                                    title="Add to wishlist">
                                <i class="far fa-heart text-sm"></i>
                            </button>
                        </div>

                        <!-- Bottom Info Overlay -->
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="flex items-center justify-between text-white">
                                <div class="flex items-center space-x-1">
                                    <?php for ($i = 0; $i < 5; $i++): ?>
                                        <i class="fas fa-star text-yellow-400 text-xs drop-shadow-sm"></i>
                                    <?php endfor; ?>
                                    <?php
                                    // Generate dynamic rating and review count
                                    $rating = number_format(rand(47, 50) / 10, 1); // 4.7 to 5.0
                                    $reviewCount = rand(150, 350); // Random review count
                                    ?>
                                    <span class="text-xs font-medium ml-1 drop-shadow-sm"><?= $rating ?> (<?= $reviewCount ?>)</span>
                                </div>
                                <div class="flex items-center text-white/90 text-xs font-medium">
                                    <i class="fas fa-clock mr-1 drop-shadow-sm"></i>
                                    <?= $service['duration'] ?> min
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="relative p-6 bg-white">
                        <!-- Service Category -->
                        <div class="inline-flex items-center bg-redolence-green/10 text-redolence-green px-3 py-1 rounded-full text-xs font-semibold mb-3">
                            <div class="w-2 h-2 bg-redolence-green rounded-full mr-2 animate-pulse"></div>
                            <?= htmlspecialchars($service['category'] ?? 'Medical Aesthetic') ?>
                        </div>

                        <!-- Service Title -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-redolence-green transition-colors duration-300 leading-tight">
                            <?= htmlspecialchars($service['name']) ?>
                        </h3>

                        <!-- Description -->
                        <p class="text-gray-600 mb-4 leading-relaxed text-sm line-clamp-2">
                            <?= htmlspecialchars($service['description']) ?>
                        </p>

                        <!-- Pricing Section -->
                        <div class="flex items-center justify-between mb-6">
                            <?php if (shouldShowPricing()): ?>
                                <div class="text-2xl font-bold text-redolence-green">
                                    <?= formatCurrency($service['price'], null, true) ?>
                                </div>
                            <?php else: ?>
                                <div class="text-lg font-semibold text-redolence-green">
                                    Contact for pricing
                                </div>
                            <?php endif; ?>
                            
                            <!-- Treatment Benefits -->
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-xs text-gray-500 font-medium">FDA Approved</span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-3">
                            <a href="<?= getBasePath() ?>/customer/book?service=<?= $service['id'] ?>" 
                               class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-redolence-green text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg text-center text-sm">
                                <i class="fas fa-calendar-check mr-2"></i>
                                Book Now
                            </a>
                            <button class="px-4 py-3 border-2 border-redolence-green text-redolence-green rounded-xl hover:bg-redolence-green hover:text-white transition-all duration-300 hover:scale-105 text-sm font-semibold">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>

                        <!-- Treatment Features -->
                        <div class="mt-4 pt-4 border-t border-gray-100">
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <div class="flex items-center">
                                    <i class="fas fa-shield-alt text-green-500 mr-1"></i>
                                    <span>Safe & Effective</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-award text-yellow-500 mr-1"></i>
                                    <span>Premium Quality</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-user-md text-blue-500 mr-1"></i>
                                    <span>Expert Care</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hover Effect Accent -->
                    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-redolence-green to-redolence-blue transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Enhanced CSS for Signature Treatment Cards -->
        <style>
            .signature-treatment-card {
                transform-style: preserve-3d;
                perspective: 1000px;
            }

            .signature-treatment-card:hover {
                transform: translateY(-12px) rotateX(2deg);
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            }

            .line-clamp-2 {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            /* Animated gradient background */
            .signature-treatment-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, transparent, rgba(73, 167, 92, 0.05), transparent);
                opacity: 0;
                transition: opacity 0.5s ease;
                pointer-events: none;
                z-index: 1;
            }

            .signature-treatment-card:hover::before {
                opacity: 1;
            }

            /* Floating animation for popular badge */
            @keyframes float-badge {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-3px); }
            }

            .signature-treatment-card:first-child .animate-pulse {
                animation: float-badge 2s ease-in-out infinite;
            }

            /* Shimmer effect on hover */
            .signature-treatment-card:hover .bg-gradient-to-r {
                background-size: 200% 200%;
                animation: shimmer 2s ease-in-out infinite;
            }

            @keyframes shimmer {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }
        </style>
    </div>
</section>