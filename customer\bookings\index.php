<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Fix currency symbol if corrupted in database
try {
    $currentSymbol = getSetting('business', 'currency_symbol', null);
    if ($currentSymbol === '262145' || $currentSymbol === 262145) {
        setSetting('business', 'currency_symbol', 'TSH');
        error_log("Customer bookings: Fixed corrupted currency_symbol (was 262145, now TSH)");
    }
} catch (Exception $e) {
    error_log("Customer bookings: Error checking currency_symbol: " . $e->getMessage());
}
require_once __DIR__ . '/../../includes/booking_expiration.php';

// Run expiration check if needed
runExpirationCheckIfNeeded();

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'cancel_booking':
                    $bookingId = $_POST['booking_id'];
                    cancelCustomerBooking($_SESSION['user_id'], $bookingId);
                    $message = 'Booking cancelled successfully!';
                    $messageType = 'success';
                    break;
                case 'submit_review':
                    // Handle review submission (implement as needed)
                    $message = 'Review submitted successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get filters
$statusFilter = $_GET['status'] ?? 'all';
$dateFilter = $_GET['date_range'] ?? 'all';

// Get customer data
$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);

// Get bookings with filters
global $database;
$conditions = ["b.user_id = ?"];
$params = [$customerId];

if ($statusFilter !== 'all') {
    $conditions[] = "b.status = ?";
    $params[] = $statusFilter;
}

if ($dateFilter !== 'all') {
    switch ($dateFilter) {
        case 'upcoming':
            $conditions[] = "b.date >= CURDATE()";
            break;
        case 'past':
            $conditions[] = "b.date < CURDATE()";
            break;
        case 'this_month':
            $conditions[] = "YEAR(b.date) = YEAR(CURDATE()) AND MONTH(b.date) = MONTH(CURDATE())";
            break;
        case 'last_month':
            $conditions[] = "YEAR(b.date) = YEAR(CURDATE() - INTERVAL 1 MONTH) AND MONTH(b.date) = MONTH(CURDATE() - INTERVAL 1 MONTH)";
            break;
    }
}

$whereClause = 'WHERE ' . implode(' AND ', $conditions);

$bookings = $database->fetchAll("
    SELECT
        b.*,
        s.name as service_name,
        s.duration as service_duration,
        s.price as service_price,
        sv.name as variation_name,
        sv.price as variation_price,
        sv.duration as variation_duration,
        p.name as package_name,
        p.price as package_price,
        st.name as staff_name,
        st.phone as staff_phone,
        pay.id as payment_id,
        pay.status as payment_status,
        pay.payment_gateway,
        pay.payment_reference
    FROM bookings b
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
    LEFT JOIN packages p ON b.package_id = p.id
    LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
    LEFT JOIN payments pay ON b.id = pay.booking_id
    $whereClause
    ORDER BY b.date DESC, b.start_time DESC
", $params);

// Calculate duration for package bookings and handle variations
foreach ($bookings as $index => $booking) {
    if (!empty($booking['package_id']) && empty($booking['service_duration'])) {
        // Get total duration for package
        $packageServices = $database->fetchAll("
            SELECT s.duration
            FROM services s
            INNER JOIN package_services ps ON s.id = ps.service_id
            WHERE ps.package_id = ?
        ", [$booking['package_id']]);

        $bookings[$index]['service_duration'] = array_sum(array_column($packageServices, 'duration'));
    } elseif (!empty($booking['variation_duration'])) {
        // Use variation duration if available
        $bookings[$index]['service_duration'] = $booking['variation_duration'];
    }
}

$pageTitle = "My Bookings";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<style>
/* Professional Bookings Page Styles */
.bookings-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
}

.bookings-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bookings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.booking-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.booking-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.booking-item:hover::before {
    transform: scaleY(1);
}

.booking-item:hover {
    transform: translateX(5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(73, 167, 92, 0.3);
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-pending {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1e293b;
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.status-confirmed {
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    color: white;
    box-shadow: 0 4px 12px rgba(73, 167, 92, 0.3);
}

.status-completed {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.status-cancelled {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.status-in-progress {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.status-no-show {
    background: linear-gradient(135deg, #f97316, #ea580c);
    color: white;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
}

.status-expired {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.3);
}

.service-icon::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #49a75c, #2563eb, #49a75c);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.info-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    backdrop-filter: blur(5px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.filter-select {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.5);
    border-radius: 12px;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    color: #1e293b;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #49a75c;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(73, 167, 92, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-action:hover {
    transform: translateY(-1px);
}

.btn-call {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-pay {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-review {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1e293b;
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.btn-disabled {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: #d1d5db;
    cursor: not-allowed;
}

.notification {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.notification.success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #15803d;
}

.notification.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #dc2626;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    border-radius: 2px;
}

.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    animation: float 6s ease-in-out infinite;
}

.floating-circle:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-circle:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 10%;
    animation-delay: 2s;
}

.floating-circle:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-state-icon {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
}

.modal {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.star-rating .star {
    color: #d1d5db;
    cursor: pointer;
    transition: color 0.2s ease;
}

.star-rating .star:hover,
.star-rating .star.active {
    color: #fbbf24;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bookings-container {
        padding: 1rem;
    }
    
    .bookings-card {
        border-radius: 16px;
        margin-bottom: 1rem;
    }
    
    .booking-item {
        padding: 1.5rem;
        border-radius: 16px;
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
    }
    
    .info-card {
        padding: 0.75rem;
    }
    
    .btn-primary {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}
</style>

<!-- Floating Background Elements -->
<div class="floating-elements">
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
</div>

<div class="bookings-container">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Page Header -->
        <div class="bookings-card p-8 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-6 lg:mb-0">
                    <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-blue-500/10 text-redolence-green px-6 py-3 rounded-full text-sm font-bold mb-4 backdrop-blur-sm border border-redolence-green/20">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9a1 1 0 01-1 1H5a1 1 0 01-1-1V7z" clip-rule="evenodd"/>
                        </svg>
                        My Appointments
                    </div>
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                        Booking
                        <span class="bg-gradient-to-r from-redolence-green to-blue-600 bg-clip-text text-transparent">
                            History
                        </span>
                    </h1>
                    <p class="text-xl text-gray-600">
                        Track and manage all your beauty appointments in one place
                    </p>
                </div>
                
                <div class="flex flex-col sm:flex-row gap-4">
                    <?php if (PAYMENT_ENABLED): ?>
                    <a href="<?= getBasePath() ?>/customer/payments" class="btn-action btn-call">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                        </svg>
                        Payment Center
                    </a>
                    <?php endif; ?>
                    <a href="<?= getBasePath() ?>/customer/book" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Book New Appointment
                    </a>
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="notification <?= $messageType ?>">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <?php if ($messageType === 'success'): ?>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    <?php else: ?>
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    <?php endif; ?>
                </svg>
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="bookings-card p-8 mb-8">
            <h3 class="section-title">Filter Appointments</h3>
            <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="status" class="block text-sm font-semibold text-gray-700 mb-2">Status</label>
                    <select id="status" name="status" class="filter-select w-full">
                        <option value="all" <?= $statusFilter === 'all' ? 'selected' : '' ?>>All Status</option>
                        <option value="PENDING" <?= $statusFilter === 'PENDING' ? 'selected' : '' ?>>Pending</option>
                        <option value="CONFIRMED" <?= $statusFilter === 'CONFIRMED' ? 'selected' : '' ?>>Confirmed</option>
                        <option value="IN_PROGRESS" <?= $statusFilter === 'IN_PROGRESS' ? 'selected' : '' ?>>In Progress</option>
                        <option value="COMPLETED" <?= $statusFilter === 'COMPLETED' ? 'selected' : '' ?>>Completed</option>
                        <option value="CANCELLED" <?= $statusFilter === 'CANCELLED' ? 'selected' : '' ?>>Cancelled</option>
                        <option value="NO_SHOW" <?= $statusFilter === 'NO_SHOW' ? 'selected' : '' ?>>No Show</option>
                        <option value="EXPIRED" <?= $statusFilter === 'EXPIRED' ? 'selected' : '' ?>>Expired</option>
                    </select>
                </div>
                <div>
                    <label for="date_range" class="block text-sm font-semibold text-gray-700 mb-2">Date Range</label>
                    <select id="date_range" name="date_range" class="filter-select w-full">
                        <option value="all" <?= $dateFilter === 'all' ? 'selected' : '' ?>>All Time</option>
                        <option value="upcoming" <?= $dateFilter === 'upcoming' ? 'selected' : '' ?>>Upcoming</option>
                        <option value="past" <?= $dateFilter === 'past' ? 'selected' : '' ?>>Past</option>
                        <option value="this_month" <?= $dateFilter === 'this_month' ? 'selected' : '' ?>>This Month</option>
                        <option value="last_month" <?= $dateFilter === 'last_month' ? 'selected' : '' ?>>Last Month</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="btn-primary w-full">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"/>
                        </svg>
                        Apply Filters
                    </button>
                </div>
            </form>
        </div>

        <!-- Bookings List -->
        <?php if (empty($bookings)): ?>
            <div class="bookings-card">
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <svg class="w-16 h-16 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">No Appointments Found</h3>
                    <p class="text-xl text-gray-600 mb-8 max-w-md mx-auto">
                        You haven't made any appointments yet or no bookings match your current filters
                    </p>
                    <a href="<?= getBasePath() ?>/customer/book" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Book Your First Appointment
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="space-y-6">
                <?php foreach ($bookings as $booking): ?>
                    <div class="booking-item">
                        <div class="flex flex-col lg:flex-row gap-6">
                            <!-- Service Icon & Basic Info -->
                            <div class="flex items-start gap-6">
                                <div class="service-icon">
                                    <?php if (!empty($booking['package_name'])): ?>
                                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="flex-1">
                                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                                        <div>
                                            <h3 class="text-2xl font-bold text-gray-900 mb-2">
                                                <?php if (!empty($booking['service_name'])): ?>
                                                    <?= htmlspecialchars($booking['service_name']) ?>
                                                    <?php if (!empty($booking['variation_name'])): ?>
                                                        <span class="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full ml-3 font-semibold">
                                                            <?= htmlspecialchars($booking['variation_name']) ?>
                                                        </span>
                                                    <?php endif; ?>
                                                <?php elseif (!empty($booking['package_name'])): ?>
                                                    <?= htmlspecialchars($booking['package_name']) ?>
                                                    <span class="text-sm bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full ml-3 font-semibold">PACKAGE</span>
                                                <?php else: ?>
                                                    Unknown Service
                                                <?php endif; ?>
                                            </h3>
                                            <p class="text-gray-600">Booking ID: #<?= $booking['id'] ?></p>
                                        </div>
                                        
                                        <?php
                                        $statusClass = 'status-' . strtolower(str_replace('_', '-', $booking['status']));
                                        $statusIcons = [
                                            'PENDING' => 'fas fa-clock',
                                            'CONFIRMED' => 'fas fa-check-circle',
                                            'IN_PROGRESS' => 'fas fa-play-circle',
                                            'COMPLETED' => 'fas fa-check-double',
                                            'CANCELLED' => 'fas fa-times-circle',
                                            'NO_SHOW' => 'fas fa-user-times',
                                            'EXPIRED' => 'fas fa-hourglass-end'
                                        ];
                                        ?>
                                        <span class="status-badge <?= $statusClass ?>">
                                            <i class="<?= $statusIcons[$booking['status']] ?? 'fas fa-question-circle' ?>"></i>
                                            <?= $booking['status'] ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Booking Details Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                            <div class="info-card">
                                <div class="flex items-center mb-2">
                                    <svg class="w-5 h-5 text-redolence-green mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                    </svg>
                                    <span class="text-sm font-semibold text-gray-700">Staff Member</span>
                                </div>
                                <p class="font-semibold text-gray-900"><?= htmlspecialchars($booking['staff_name']) ?></p>
                            </div>

                            <div class="info-card">
                                <div class="flex items-center mb-2">
                                    <svg class="w-5 h-5 text-redolence-green mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9a1 1 0 01-1 1H5a1 1 0 01-1-1V7z" clip-rule="evenodd"/>
                                    </svg>
                                    <span class="text-sm font-semibold text-gray-700">Date & Time</span>
                                </div>
                                <p class="font-semibold text-gray-900">
                                    <?= date('M j, Y', strtotime($booking['date'])) ?><br>
                                    <span class="text-redolence-green"><?= date('g:i A', strtotime($booking['start_time'])) ?></span>
                                </p>
                            </div>

                            <div class="info-card">
                                <div class="flex items-center mb-2">
                                    <svg class="w-5 h-5 text-redolence-green mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                    </svg>
                                    <span class="text-sm font-semibold text-gray-700">Duration & Price</span>
                                </div>
                                <p class="font-semibold text-gray-900">
                                    <?= $booking['service_duration'] ?> minutes<br>
                                    <?php if (shouldShowPricing()): ?>
                                        <span class="text-redolence-green">TSH <?= number_format((int)$booking['total_amount']) ?></span>
                                    <?php else: ?>
                                        <span class="text-gray-500">Price on request</span>
                                    <?php endif; ?>
                                </p>
                            </div>

                            <div class="info-card">
                                <div class="flex items-center mb-2">
                                    <svg class="w-5 h-5 text-redolence-green mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                                    </svg>
                                    <span class="text-sm font-semibold text-gray-700">Payment Status</span>
                                </div>
                                <p class="font-semibold">
                                    <?php if ($booking['payment_status'] === 'COMPLETED'): ?>
                                        <span class="text-green-600">Paid</span>
                                    <?php elseif ($booking['payment_status'] === 'PENDING'): ?>
                                        <span class="text-yellow-600">Pending</span>
                                    <?php else: ?>
                                        <span class="text-red-600">Unpaid</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <!-- Package Services (if applicable) -->
                        <?php if (!empty($booking['package_name'])): ?>
                            <?php
                            $packageServices = $database->fetchAll("
                                SELECT s.name
                                FROM services s
                                INNER JOIN package_services ps ON s.id = ps.service_id
                                WHERE ps.package_id = ?
                                ORDER BY s.name
                            ", [$booking['package_id']]);
                            ?>
                            <div class="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                                <p class="text-sm font-semibold text-yellow-800 mb-2">Package includes:</p>
                                <div class="flex flex-wrap gap-2">
                                    <?php foreach ($packageServices as $service): ?>
                                        <span class="inline-block px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">
                                            <?= htmlspecialchars($service['name']) ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Notes (if any) -->
                        <?php if ($booking['notes']): ?>
                            <div class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                <p class="text-sm font-semibold text-blue-800 mb-2">Notes:</p>
                                <p class="text-blue-700"><?= htmlspecialchars($booking['notes']) ?></p>
                            </div>
                        <?php endif; ?>

                        <!-- Action Buttons -->
                        <div class="flex flex-wrap gap-3 mt-6 pt-6 border-t border-gray-200">
                            <?php if ($booking['status'] === 'PENDING' || $booking['status'] === 'CONFIRMED'): ?>
                                <?php
                                $bookingDateTime = new DateTime($booking['date'] . ' ' . $booking['start_time']);
                                $now = new DateTime();
                                $hoursDiff = ($bookingDateTime->getTimestamp() - $now->getTimestamp()) / 3600;
                                ?>

                                <?php if ($hoursDiff >= 24): ?>
                                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to cancel this appointment?')">
                                        <input type="hidden" name="action" value="cancel_booking">
                                        <input type="hidden" name="booking_id" value="<?= $booking['id'] ?>">
                                        <button type="submit" class="btn-action btn-secondary">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                            </svg>
                                            Cancel Booking
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <span class="btn-action btn-disabled">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        Cannot Cancel
                                    </span>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <?php if ($booking['staff_phone']): ?>
                                <a href="tel:<?= $booking['staff_phone'] ?>" class="btn-action btn-call">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    Call Staff
                                </a>
                            <?php endif; ?>
                            
                            <?php if (PAYMENT_ENABLED && ($booking['status'] === 'CONFIRMED' || $booking['status'] === 'COMPLETED')): ?>
                                <?php if (!$booking['payment_status'] || $booking['payment_status'] === 'FAILED'): ?>
                                    <a href="<?= getBasePath() ?>/customer/payments" class="btn-action btn-pay">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                        </svg>
                                        Pay Now
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if ($booking['status'] === 'COMPLETED'): ?>
                                <button onclick="openReviewModal('<?= $booking['id'] ?>')" class="btn-action btn-review">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                    Leave Review
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Review Modal -->
<div id="reviewModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="modal max-w-md w-full p-8">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-gray-900">Leave a Review</h3>
            <button onclick="closeReviewModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <form id="reviewForm" method="POST">
            <input type="hidden" name="action" value="submit_review">
            <input type="hidden" id="reviewBookingId" name="booking_id" value="">

            <div class="mb-6">
                <label class="block text-sm font-semibold text-gray-700 mb-3">Rating</label>
                <div class="star-rating flex gap-2" id="starRating">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <button type="button" class="star text-3xl transition-colors" data-rating="<?= $i ?>">
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </button>
                    <?php endfor; ?>
                </div>
                <input type="hidden" id="rating" name="rating" value="" required>
            </div>

            <div class="mb-6">
                <label for="review_comment" class="block text-sm font-semibold text-gray-700 mb-2">Comment</label>
                <textarea id="review_comment" name="comment" rows="4" 
                          class="filter-select w-full resize-none"
                          placeholder="Share your experience with us..."></textarea>
            </div>

            <div class="flex items-center justify-end space-x-4">
                <button type="button" onclick="closeReviewModal()" 
                        class="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-semibold transition-colors">
                    Cancel
                </button>
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    Submit Review
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Enhanced bookings page interactions
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll for notifications
    const notification = document.querySelector('.notification');
    if (notification) {
        notification.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Auto-hide notification after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }
    
    // Enhanced hover effects for booking items
    const bookingItems = document.querySelectorAll('.booking-item');
    bookingItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
    
    // Smooth animations on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all booking items
    document.querySelectorAll('.booking-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });
    
    // Form submission with loading states
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                submitBtn.disabled = true;
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Processing...
                `;
                
                // Re-enable after 3 seconds to prevent permanent disable
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 3000);
            }
        });
    });
});

// Review Modal Functions
function openReviewModal(bookingId) {
    document.getElementById('reviewBookingId').value = bookingId;
    document.getElementById('reviewModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeReviewModal() {
    document.getElementById('reviewModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    
    // Reset form
    document.getElementById('reviewForm').reset();
    document.getElementById('rating').value = '';
    document.querySelectorAll('.star').forEach(star => {
        star.classList.remove('active');
    });
}

// Star rating functionality
document.querySelectorAll('.star').forEach(star => {
    star.addEventListener('click', function() {
        const rating = parseInt(this.dataset.rating);
        document.getElementById('rating').value = rating;
        
        // Update star display
        document.querySelectorAll('.star').forEach((s, index) => {
            if (index < rating) {
                s.classList.add('active');
            } else {
                s.classList.remove('active');
            }
        });
    });
    
    // Hover effect for stars
    star.addEventListener('mouseenter', function() {
        const rating = parseInt(this.dataset.rating);
        document.querySelectorAll('.star').forEach((s, index) => {
            if (index < rating) {
                s.style.color = '#fbbf24';
            } else {
                s.style.color = '#d1d5db';
            }
        });
    });
});

// Reset star colors on mouse leave
document.getElementById('starRating').addEventListener('mouseleave', function() {
    const currentRating = parseInt(document.getElementById('rating').value) || 0;
    document.querySelectorAll('.star').forEach((s, index) => {
        if (index < currentRating) {
            s.style.color = '#fbbf24';
        } else {
            s.style.color = '#d1d5db';
        }
    });
});

// Close modal when clicking outside
document.getElementById('reviewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReviewModal();
    }
});

// Escape key to close modal
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('reviewModal').classList.contains('hidden')) {
        closeReviewModal();
    }
});

// Modern toast notification system
function showToast(message, type = 'success') {
    const existingToast = document.getElementById('toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    const toast = document.createElement('div');
    toast.id = 'toast';
    toast.className = `fixed top-4 right-4 z-50 flex items-center p-4 rounded-xl shadow-lg transform translate-x-full transition-all duration-300 ${
        type === 'success' 
            ? 'bg-green-500 text-white' 
            : 'bg-red-500 text-white'
    }`;
    
    toast.innerHTML = `
        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
            ${type === 'success' 
                ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>'
                : '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>'
            }
        </svg>
        <span class="font-medium">${message}</span>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        toast.style.transform = 'translateX(full)';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>