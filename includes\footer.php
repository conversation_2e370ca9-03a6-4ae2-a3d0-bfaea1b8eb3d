    </main>

<!-- Footer -->
<?php $basePath = getBasePath(); ?>
<footer class="bg-gradient-to-b from-white to-gray-50 text-gray-800 py-12 border-t border-gray-100">
    <div class="mx-auto max-w-7xl px-6 py-16 sm:py-20 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
            <!-- Brand Section -->
            <div class="md:col-span-1">
                <div class="flex items-center mb-4">
                    <svg class="h-8 w-8 text-redolence-green mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 7.5l4.5 2.25m0 0l4.5 2.25M14.25 9.75v9" />
                    </svg>
                    <span class="text-gray-800 font-bold text-2xl">Redolence Medical</span>
                </div>
                <p class="text-gray-600 text-sm mb-6 max-w-xs">
                    Advanced medical care with a personal touch. Your health and comfort are our priority.
                </p>
                <div class="flex space-x-4 mb-6">
                    <a href="#" class="bg-gray-100 text-gray-700 hover:bg-redolence-green hover:text-white transition-colors rounded-full p-2">
                        <span class="sr-only">Instagram</span>
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd" d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.49 0-.928-.422-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .506-.438.928-.928.928zM12.017 7.356c-2.448 0-4.474 2.026-4.474 4.474s2.026 4.474 4.474 4.474 4.474-2.026 4.474-4.474-2.026-4.474-4.474-4.474z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    <a href="#" class="bg-gray-100 text-gray-700 hover:bg-redolence-green hover:text-white transition-colors rounded-full p-2">
                        <span class="sr-only">Facebook</span>
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    <a href="#" class="bg-gray-100 text-gray-700 hover:bg-redolence-green hover:text-white transition-colors rounded-full p-2">
                        <span class="sr-only">Twitter</span>
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-gray-800 font-semibold text-lg mb-5 relative after:content-[''] after:absolute after:bottom-[-10px] after:left-0 after:w-10 after:h-1 after:bg-redolence-green">Quick Links</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="<?= $basePath ?>/" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <a href="<?= $basePath ?>/services" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            Services
                        </a>
                    </li>
                    <li>
                        <a href="<?= $basePath ?>/about" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            About Us
                        </a>
                    </li>
                    <li>
                        <a href="<?= $basePath ?>/contact" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            Contact
                        </a>
                    </li>
                    <li>
                        <a href="<?= $basePath ?>/faq" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            FAQ
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Services -->
            <div>
                <h3 class="text-gray-800 font-semibold text-lg mb-5 relative after:content-[''] after:absolute after:bottom-[-10px] after:left-0 after:w-10 after:h-1 after:bg-redolence-green">Services</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="<?= $basePath ?>/services#consultation" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            Medical Consultation
                        </a>
                    </li>
                    <li>
                        <a href="<?= $basePath ?>/services#emergency" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            Emergency Care
                        </a>
                    </li>
                    <li>
                        <a href="<?= $basePath ?>/services#specialty" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            Specialty Treatments
                        </a>
                    </li>
                    <li>
                        <a href="<?= $basePath ?>/services#wellness" class="text-gray-600 hover:text-redolence-green transition-colors flex items-center">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5l6 6-6 6M4.5 4.5l6 6-6 6" />
                            </svg>
                            Wellness Programs
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Contact & Newsletter -->
            <div>
                <h3 class="text-gray-800 font-semibold text-lg mb-5 relative after:content-[''] after:absolute after:bottom-[-10px] after:left-0 after:w-10 after:h-1 after:bg-redolence-green">Stay Connected</h3>
                <div class="space-y-4 mb-6">
                    <div class="flex items-start space-x-3">
                        <svg class="mt-1 h-5 w-5 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                        </svg>
                        <a href="mailto:<EMAIL>" class="text-gray-600 hover:text-redolence-green transition-colors"><EMAIL></a>
                    </div>
                    <div class="flex items-start space-x-3">
                        <svg class="mt-1 h-5 w-5 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                        </svg>
                        <a href="tel:+15551234567" class="text-gray-600 hover:text-redolence-green transition-colors">+****************</a>
                    </div>
                    <div class="flex items-start space-x-3">
                        <svg class="mt-1 h-10 w-10 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                        </svg>
                        <span class="text-gray-600">123 Health Avenue, Suite 456, Wellness City, WC 78910</span>
                    </div>
                </div>
                <form class="mt-6 flex max-w-md" action="<?= $basePath ?>/api/newsletter.php" method="POST">
                    <label for="email-address" class="sr-only">Email address</label>
                    <input type="email" name="email" id="email-address" autocomplete="email" required 
                           class="w-full min-w-0 appearance-none rounded-l-md border border-gray-300 bg-white px-3 py-2 text-gray-800 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-transparent sm:text-sm" 
                           placeholder="Newsletter signup">
                    <button type="submit" 
                            class="flex-shrink-0 rounded-r-md bg-redolence-green px-4 py-2 text-sm font-semibold text-white hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2 transition-colors">
                        Subscribe
                    </button>
                </form>
                <p class="text-gray-500 text-xs mt-2">We respect your privacy. Unsubscribe anytime.</p>
            </div>
        </div>
    </div>

    <!-- Bottom Bar -->
    <div class="bg-gray-100 py-6">
        <div class="mx-auto max-w-7xl px-6 flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-600 text-sm">
                &copy; <?= date('Y') ?> Redolence Medical. All rights reserved.
            </p>
            <div class="flex space-x-6 mt-4 md:mt-0">
                <a href="<?= $basePath ?>/privacy" class="text-gray-600 hover:text-redolence-green text-sm transition-colors">Privacy Policy</a>
                <a href="<?= $basePath ?>/terms" class="text-gray-600 hover:text-redolence-green text-sm transition-colors">Terms of Service</a>
                <a href="<?= $basePath ?>/cookie-policy" class="text-gray-600 hover:text-redolence-green text-sm transition-colors">Cookie Policy</a>
            </div>
        </div>
    </div>
</footer>

<!-- Include Enhanced Mobile Menu Component -->
<?php include __DIR__ . '/mobile-menu-new.php'; ?>

<!-- Newsletter form submission -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Newsletter form submission - handle all newsletter forms
        const newsletterForms = document.querySelectorAll('form[action*="/api/newsletter.php"], form[action*="newsletter"]');

        newsletterForms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const email = formData.get('email');
                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;

                // Validate email
                if (!email || !email.includes('@')) {
                    showNotification('Please enter a valid email address.', 'error');
                    return;
                }

                // Show loading state
                submitButton.textContent = 'Subscribing...';
                submitButton.disabled = true;

                fetch('<?= $basePath ?>/api/newsletter.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showNotification('Thank you for subscribing to our newsletter!', 'success');
                        this.reset();
                    } else {
                        showNotification(data.error || 'Failed to subscribe. Please try again.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Newsletter subscription error:', error);
                    showNotification('Failed to subscribe. Please check your connection and try again.', 'error');
                })
                .finally(() => {
                    // Reset button state
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                });
            });
        });

        // Notification function
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.newsletter-notification');
            existingNotifications.forEach(n => n.remove());

            // Create notification
            const notification = document.createElement('div');
            notification.className = `newsletter-notification fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

            if (type === 'success') {
                notification.className += ' bg-green-600 text-white';
            } else if (type === 'error') {
                notification.className += ' bg-red-600 text-white';
            } else {
                notification.className += ' bg-blue-600 text-white';
            }

            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }
    });
</script>
</body>
</html>
