<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_profile':
                    $updateData = [
                        'name' => $_POST['name'],
                        'phone' => $_POST['phone'],
                        'date_of_birth' => $_POST['date_of_birth'] ?: null,
                        'image' => $_POST['avatar'] ?: null
                    ];
                    
                    updateCustomerProfile($_SESSION['user_id'], $updateData);
                    $message = 'Profile updated successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'change_password':
                    $currentPassword = $_POST['current_password'];
                    $newPassword = $_POST['new_password'];
                    $confirmPassword = $_POST['confirm_password'];
                    
                    if ($newPassword !== $confirmPassword) {
                        throw new Exception("New passwords do not match");
                    }
                    
                    if (strlen($newPassword) < 8) {
                        throw new Exception("Password must be at least 8 characters long");
                    }
                    
                    changeCustomerPassword($_SESSION['user_id'], $currentPassword, $newPassword);
                    $message = 'Password changed successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get customer data
$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);
$stats = getIndividualCustomerStats($customerId);
$loyaltyTier = getCustomerLoyaltyTierEnhanced($customerId);

$pageTitle = "Profile Dashboard";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<style>
/* Modern Profile Dashboard Styles */
.profile-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
}

.profile-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.3);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.profile-avatar::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #49a75c, #2563eb, #49a75c);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #49a75c, #2563eb);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    background: rgba(248, 250, 252, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.5);
    border-radius: 12px;
    font-size: 1rem;
    color: #1e293b;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.form-input:focus {
    outline: none;
    border-color: #49a75c;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
    transform: translateY(-1px);
}

.form-input:disabled {
    background: rgba(226, 232, 240, 0.3);
    color: #64748b;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(73, 167, 92, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
}

.loyalty-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1e293b;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.notification {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.notification.success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #15803d;
}

.notification.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #dc2626;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #49a75c, #2563eb);
    border-radius: 2px;
}

.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(37, 99, 235, 0.1));
    animation: float 6s ease-in-out infinite;
}

.floating-circle:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-circle:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 10%;
    animation-delay: 2s;
}

.floating-circle:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-container {
        padding: 1rem;
    }
    
    .profile-card {
        border-radius: 16px;
        margin-bottom: 1rem;
    }
    
    .profile-avatar {
        width: 80px;
        height: 80px;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .form-input {
        padding: 0.875rem 1rem;
    }
    
    .btn-primary, .btn-secondary {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Loading Animation */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
</style>

<!-- Floating Background Elements -->
<div class="floating-elements">
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
</div>

<div class="profile-container">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Page Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-blue-500/10 text-redolence-green px-6 py-3 rounded-full text-sm font-bold mb-6 backdrop-blur-sm border border-redolence-green/20">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                </svg>
                Profile Dashboard
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Welcome back,
                <span class="bg-gradient-to-r from-redolence-green to-blue-600 bg-clip-text text-transparent">
                    <?= htmlspecialchars(explode(' ', $profile['name'])[0]) ?>
                </span>
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Manage your profile, track your beauty journey, and stay connected with Redolence.
            </p>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="notification <?= $messageType ?>">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <?php if ($messageType === 'success'): ?>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    <?php else: ?>
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    <?php endif; ?>
                </svg>
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Profile Overview Card -->
        <div class="profile-card p-8 mb-8">
            <div class="flex flex-col lg:flex-row items-center lg:items-start gap-8">
                <!-- Avatar Section -->
                <div class="flex flex-col items-center">
                    <div class="profile-avatar mb-4">
                        <?php if ($profile['avatar']): ?>
                            <img src="<?= htmlspecialchars($profile['avatar']) ?>" alt="Profile Picture" />
                        <?php else: ?>
                            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <div class="loyalty-badge">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        <?= $loyaltyTier['name'] ?> Member
                    </div>
                </div>

                <!-- Profile Info -->
                <div class="flex-1 text-center lg:text-left">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2"><?= htmlspecialchars($profile['name']) ?></h2>
                    <p class="text-lg text-gray-600 mb-4"><?= htmlspecialchars($profile['email']) ?></p>
                    <div class="flex flex-wrap justify-center lg:justify-start gap-4 text-sm text-gray-500">
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9a1 1 0 01-1 1H5a1 1 0 01-1-1V7z" clip-rule="evenodd"/>
                            </svg>
                            Member since <?= date('F Y', strtotime($profile['created_at'])) ?>
                        </div>
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <?= number_format($profile['points']) ?> Loyalty Points
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 gap-4 lg:gap-6">
                    <div class="stat-card">
                        <div class="text-3xl font-bold text-redolence-green mb-2"><?= $stats['completedBookings'] ?></div>
                        <div class="text-sm text-gray-600">Completed Visits</div>
                    </div>
                    <div class="stat-card">
                        <div class="text-3xl font-bold text-blue-600 mb-2">TSH <?= number_format($stats['totalSpent'], 0) ?></div>
                        <div class="text-sm text-gray-600">Total Spent</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Personal Information -->
            <div class="profile-card p-8">
                <h3 class="section-title">Personal Information</h3>
                
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="update_profile">

                    <div>
                        <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">Full Name</label>
                        <input type="text" id="name" name="name" value="<?= htmlspecialchars($profile['name']) ?>" required class="form-input">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="email" value="<?= htmlspecialchars($profile['email']) ?>" disabled class="form-input">
                        <p class="text-sm text-gray-500 mt-2">Email cannot be changed. Contact support if needed.</p>
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" id="phone" name="phone" value="<?= htmlspecialchars($profile['phone'] ?? '') ?>" class="form-input">
                    </div>

                    <div>
                        <label for="date_of_birth" class="block text-sm font-semibold text-gray-700 mb-2">Date of Birth</label>
                        <input type="date" id="date_of_birth" name="date_of_birth" value="<?= $profile['date_of_birth'] ? date('Y-m-d', strtotime($profile['date_of_birth'])) : '' ?>" class="form-input">
                    </div>

                    <div>
                        <label for="avatar" class="block text-sm font-semibold text-gray-700 mb-2">Profile Picture URL</label>
                        <input type="url" id="avatar" name="avatar" value="<?= htmlspecialchars($profile['avatar'] ?? '') ?>" class="form-input" placeholder="https://example.com/your-photo.jpg">
                    </div>

                    <button type="submit" class="btn-primary w-full">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Update Profile
                    </button>
                </form>
            </div>

            <!-- Security Settings -->
            <div class="profile-card p-8">
                <h3 class="section-title">Security Settings</h3>
                
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="change_password">

                    <div>
                        <label for="current_password" class="block text-sm font-semibold text-gray-700 mb-2">Current Password</label>
                        <input type="password" id="current_password" name="current_password" required class="form-input">
                    </div>

                    <div>
                        <label for="new_password" class="block text-sm font-semibold text-gray-700 mb-2">New Password</label>
                        <input type="password" id="new_password" name="new_password" required minlength="8" class="form-input">
                        <p class="text-sm text-gray-500 mt-2">Must be at least 8 characters long</p>
                    </div>

                    <div>
                        <label for="confirm_password" class="block text-sm font-semibold text-gray-700 mb-2">Confirm New Password</label>
                        <input type="password" id="confirm_password" name="confirm_password" required minlength="8" class="form-input">
                    </div>

                    <button type="submit" class="btn-secondary w-full">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                        </svg>
                        Change Password
                    </button>
                </form>

                <!-- Account Information -->
                <div class="mt-8 pt-8 border-t border-gray-200">
                    <h4 class="font-semibold text-gray-900 mb-4">Account Information</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                            <span class="text-gray-600">Account Created:</span>
                            <span class="font-medium text-gray-900"><?= date('M j, Y', strtotime($profile['created_at'])) ?></span>
                        </div>
                        <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                            <span class="text-gray-600">Referral Code:</span>
                            <div class="flex items-center gap-2">
                                <span class="font-mono font-semibold text-redolence-green"><?= htmlspecialchars($profile['referral_code']) ?></span>
                                <button onclick="copyReferralCode()" class="text-gray-400 hover:text-redolence-green transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <?php if ($profile['referred_by']): ?>
                            <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                                <span class="text-gray-600">Referred By:</span>
                                <span class="font-medium text-gray-900"><?= htmlspecialchars($profile['referred_by']) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Statistics -->
        <div class="profile-card p-8 mt-8">
            <h3 class="section-title">Account Statistics</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="stat-card">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9a1 1 0 01-1 1H5a1 1 0 01-1-1V7z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 mb-2"><?= $stats['totalBookings'] ?></p>
                    <p class="text-sm text-gray-600">Total Bookings</p>
                </div>
                
                <div class="stat-card">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 mb-2"><?= $stats['completedBookings'] ?></p>
                    <p class="text-sm text-gray-600">Completed Visits</p>
                </div>
                
                <div class="stat-card">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 mb-2">TSH <?= number_format($stats['avgBookingValue'], 0) ?></p>
                    <p class="text-sm text-gray-600">Avg. Booking Value</p>
                </div>
                
                <div class="stat-card">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 mb-2"><?= number_format($profile['points']) ?></p>
                    <p class="text-sm text-gray-600">Current Points</p>
                </div>
            </div>
            
            <?php if ($stats['firstVisit'] && $stats['lastVisit']): ?>
                <div class="mt-8 pt-8 border-t border-gray-200 text-center">
                    <p class="text-gray-600">
                        <span class="font-medium">First visit:</span> <?= date('M j, Y', strtotime($stats['firstVisit'])) ?> • 
                        <span class="font-medium">Last visit:</span> <?= date('M j, Y', strtotime($stats['lastVisit'])) ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Quick Actions -->
        <div class="profile-card p-8 mt-8">
            <h3 class="section-title">Quick Actions</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <a href="<?= getBasePath() ?>/customer/book" class="group flex items-center p-6 bg-gradient-to-br from-redolence-green/10 to-redolence-green/5 rounded-xl border border-redolence-green/20 hover:border-redolence-green/40 transition-all duration-300 hover:scale-105">
                    <div class="w-12 h-12 rounded-full bg-redolence-green flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 group-hover:text-redolence-green transition-colors">Book Appointment</h4>
                        <p class="text-sm text-gray-600">Schedule your next treatment</p>
                    </div>
                </a>

                <a href="<?= getBasePath() ?>/customer/bookings" class="group flex items-center p-6 bg-gradient-to-br from-blue-500/10 to-blue-500/5 rounded-xl border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 hover:scale-105">
                    <div class="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">View Bookings</h4>
                        <p class="text-sm text-gray-600">Check your appointments</p>
                    </div>
                </a>

                <a href="<?= getBasePath() ?>/customer/wishlist.php" class="group flex items-center p-6 bg-gradient-to-br from-purple-500/10 to-purple-500/5 rounded-xl border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:scale-105">
                    <div class="w-12 h-12 rounded-full bg-purple-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">Wishlist</h4>
                        <p class="text-sm text-gray-600">Your saved treatments</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced form validation and interactions
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
    
    // Form submission with loading states
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = `
                    <svg class="w-5 h-5 mr-2 inline animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Processing...
                `;
            }
        });
    });
    
    // Smooth scroll for notifications
    const notification = document.querySelector('.notification');
    if (notification) {
        notification.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Auto-hide notification after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }
    
    // Enhanced input focus effects
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});

// Copy referral code functionality with modern feedback
function copyReferralCode() {
    const referralCode = '<?= htmlspecialchars($profile['referral_code']) ?>';
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(referralCode).then(function() {
            showToast('Referral code copied to clipboard!', 'success');
        }).catch(function() {
            fallbackCopyTextToClipboard(referralCode);
        });
    } else {
        fallbackCopyTextToClipboard(referralCode);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showToast('Referral code copied to clipboard!', 'success');
    } catch (err) {
        showToast('Failed to copy referral code', 'error');
    }
    
    document.body.removeChild(textArea);
}

// Modern toast notification system
function showToast(message, type = 'success') {
    // Remove existing toast
    const existingToast = document.getElementById('toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.id = 'toast';
    toast.className = `fixed top-4 right-4 z-50 flex items-center p-4 rounded-xl shadow-lg transform translate-x-full transition-all duration-300 ${
        type === 'success' 
            ? 'bg-green-500 text-white' 
            : 'bg-red-500 text-white'
    }`;
    
    toast.innerHTML = `
        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
            ${type === 'success' 
                ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>'
                : '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>'
            }
        </svg>
        <span class="font-medium">${message}</span>
    `;
    
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // Animate out and remove
    setTimeout(() => {
        toast.style.transform = 'translateX(full)';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// Smooth animations on scroll
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe all profile cards
document.querySelectorAll('.profile-card').forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(card);
});
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>