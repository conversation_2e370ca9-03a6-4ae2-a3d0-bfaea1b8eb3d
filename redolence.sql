-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 23, 2025 at 02:13 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `flix_salonce2`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_2fa_attempts`
--

CREATE TABLE `admin_2fa_attempts` (
  `id` varchar(36) NOT NULL,
  `admin_id` varchar(36) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `attempt_type` enum('EMAIL_CODE','BACKUP_CODE') NOT NULL,
  `is_successful` tinyint(1) DEFAULT 0,
  `attempted_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_2fa_attempts`
--

INSERT INTO `admin_2fa_attempts` (`id`, `admin_id`, `ip_address`, `attempt_type`, `is_successful`, `attempted_at`) VALUES
('38faf11c-ca91-4ba2-ab7b-1fa3d899e02f', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '127.0.0.1', 'EMAIL_CODE', 0, '2025-07-07 01:56:00'),
('4bb7d755-df12-4d40-8c6c-9a7394307e0d', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '127.0.0.1', 'EMAIL_CODE', 1, '2025-07-07 01:56:09'),
('4bf2dcc5-bf69-4131-afa2-4805d31389e6', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '127.0.0.1', 'BACKUP_CODE', 1, '2025-07-07 01:36:46'),
('845d24ca-0875-4a46-a1b1-5585795ed100', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '127.0.0.1', 'BACKUP_CODE', 0, '2025-07-07 01:35:41'),
('d3c5337e-04fb-4e8e-abf9-b9b36f9d7121', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '127.0.0.1', 'BACKUP_CODE', 1, '2025-07-07 01:34:24'),
('e3257815-ca1d-4aad-b931-de5c4635d97b', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '127.0.0.1', 'BACKUP_CODE', 0, '2025-07-07 01:36:18'),
('f347273d-d993-445d-ac50-afa096d0b2f4', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '127.0.0.1', 'BACKUP_CODE', 1, '2025-07-07 01:48:45');

-- --------------------------------------------------------

--
-- Table structure for table `admin_2fa_backup_codes`
--

CREATE TABLE `admin_2fa_backup_codes` (
  `id` varchar(36) NOT NULL,
  `admin_id` varchar(36) NOT NULL,
  `code_hash` varchar(255) NOT NULL,
  `is_used` tinyint(1) DEFAULT 0,
  `used_at` timestamp NULL DEFAULT NULL,
  `used_ip` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_2fa_backup_codes`
--

INSERT INTO `admin_2fa_backup_codes` (`id`, `admin_id`, `code_hash`, `is_used`, `used_at`, `used_ip`, `created_at`) VALUES
('357ac2c5-3054-4db7-b12a-88b516b4d65b', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$nmTNxvKJozurs2OWrBAiP.3Y0N0qDrCN9/taDZsZ2iA9k8vgYaELS', 1, '2025-07-07 01:36:46', '127.0.0.1', '2025-07-07 01:32:00'),
('384cc200-1b35-4108-b1c1-15795b148191', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$vzHrN1saqXPJ6oI.B7XdOOs0rqdTyw3Zql6fR.10KRBeKFGP6VVRK', 0, NULL, NULL, '2025-07-07 01:32:00'),
('63da7f50-bb70-4d0d-a58a-a8292e4453c9', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$mC6.mIx71MR4gSv44E1WR.0UjH05eAa0Vc8emUWOU.KGiANtr2PWi', 0, NULL, NULL, '2025-07-07 01:32:00'),
('73ac9ed5-a508-4725-98e4-8b91b94b97f2', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$VDHCuyjePg040sM01fcjYOzbOxOMAmArrSJJgQURriFjWnShsVZzq', 0, NULL, NULL, '2025-07-07 01:31:59'),
('7c1fc2e0-4241-407c-a0fe-24e3f0405268', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$O0uj0..HV6JBR5OlrW5OH.OxSg.0iZzhgo0SlXx91S6fSl.P/S5HC', 0, NULL, NULL, '2025-07-07 01:32:00'),
('9e65dbdf-3aad-4879-91ed-6cd337afc81a', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$HyoHztYIHg38X.Vf4tcOyeUK706ymX41HPnpC5A/dOWjyWt6ZyT/i', 1, '2025-07-07 01:34:24', '127.0.0.1', '2025-07-07 01:32:00'),
('beeb6b29-06fe-4cce-a118-fca032580594', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$mSh4Ie.xYw4KVz9KLydxE.MYmajS0V9TsErrX25KCcxOQRNejhhv.', 0, NULL, NULL, '2025-07-07 01:32:00'),
('e1ac923b-fcb3-49a5-8c08-fd1dc3c60226', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$EUoGL.s3JZdB02mgFdRAYeKG8.gM7hL/48KerKUZF1kL5yEGhlTly', 0, NULL, NULL, '2025-07-07 01:32:00'),
('ea174cff-8149-4341-ae56-b4dac5b86751', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$tHNI9O3oVKaGIORECwgEzeUwkSLwD7E/0C/gFFggtBfKoncr2QiLi', 0, NULL, NULL, '2025-07-07 01:32:00'),
('f678d4d5-f76b-439f-aaf5-086cb8c9924b', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '$2y$10$lWau5/FOWzys41IiJ3mmfOgYkZ2FIjdE9WlIwgVhMa0XKV.6xAYNu', 1, '2025-07-07 01:48:45', '127.0.0.1', '2025-07-07 01:32:00');

-- --------------------------------------------------------

--
-- Table structure for table `admin_2fa_email_codes`
--

CREATE TABLE `admin_2fa_email_codes` (
  `id` varchar(36) NOT NULL,
  `admin_id` varchar(36) NOT NULL,
  `code` varchar(6) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_used` tinyint(1) DEFAULT 0,
  `attempts` int(11) DEFAULT 0,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_2fa_email_codes`
--

INSERT INTO `admin_2fa_email_codes` (`id`, `admin_id`, `code`, `expires_at`, `is_used`, `attempts`, `ip_address`, `user_agent`, `created_at`) VALUES
('dd75cf3d-de0f-481b-b128-e00ae8a175f4', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '578889', '2025-07-07 01:56:09', 1, 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:54:48');

-- --------------------------------------------------------

--
-- Table structure for table `admin_2fa_logs`
--

CREATE TABLE `admin_2fa_logs` (
  `id` varchar(36) NOT NULL,
  `admin_id` varchar(36) NOT NULL,
  `action` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_2fa_logs`
--

INSERT INTO `admin_2fa_logs` (`id`, `admin_id`, `action`, `description`, `metadata`, `ip_address`, `user_agent`, `created_at`) VALUES
('0f56d72a-a7a7-40b6-81fa-c4266b60c717', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_SUCCESS', 'Backup code used successfully', '{\"code_id\":\"f678d4d5-f76b-439f-aaf5-086cb8c9924b\",\"remaining_codes\":7}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:48:45'),
('12762a8c-d40e-4f6e-891c-260df3604681', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_EMAIL_FAILED', 'Invalid or expired email verification code', '{\"attempted_code\":\"578898\"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:56:00'),
('2a447185-be2e-4fd3-83d3-a6253c1c9041', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_EMAIL_SUCCESS', 'Email verification code verified successfully', '{\"code_id\":\"dd75cf3d-de0f-481b-b128-e00ae8a175f4\"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:56:09'),
('67508fc0-0ed4-48c1-a4ec-74b2eaacdf01', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_LOGIN_SUCCESS', 'Admin successfully used backup code for 2FA verification', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:36:46'),
('7e6d312a-c782-451f-8584-232dd48db21b', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_ENABLED', 'Two-factor authentication enabled', '{\"email_enabled\":1,\"backup_codes_enabled\":1}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:25:52'),
('8f650f79-0afa-4f7a-9524-a6063016c5fb', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_FAILED', 'Invalid backup code attempted', '{\"attempted_code\":\"YH****\"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:36:18'),
('9a06bfe7-8976-4c65-8a20-a9eeae7d3220', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_SUCCESS', 'Backup code used successfully', '{\"code_id\":\"9e65dbdf-3aad-4879-91ed-6cd337afc81a\",\"remaining_codes\":9}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:34:24'),
('a9282542-2621-4e7f-b354-4fad984f98cd', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_FAILED', 'Invalid backup code attempted', '{\"attempted_code\":\"YH****\"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:35:41'),
('baa436ba-ed40-4642-bfd3-b5154d0c0377', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_LOGIN_SUCCESS', 'Admin successfully completed 2FA verification', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:56:09'),
('ca21741d-7e8b-4840-9952-1ff3374a66df', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_SUCCESS', 'Backup code used successfully', '{\"code_id\":\"357ac2c5-3054-4db7-b12a-88b516b4d65b\",\"remaining_codes\":8}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:36:46'),
('e5f1154a-a143-4211-9e3d-7434cb160397', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_ENABLED', 'Two-factor authentication enabled', '{\"email_enabled\":1,\"backup_codes_enabled\":1}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:18:06'),
('edcd2374-ffc9-4d57-bcd5-3e84001408a4', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_ENABLED', 'Two-factor authentication enabled', '{\"email_enabled\":1,\"backup_codes_enabled\":1}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:00:42'),
('f1d2bf00-e95e-4591-b06c-91a1dec8ea72', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_LOGIN_SUCCESS', 'Admin successfully used backup code for 2FA verification', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:48:45'),
('f60de06d-6389-4430-b6d6-e7bed223620f', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_GENERATED', 'Backup codes generated', '{\"codes_count\":10}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:32:00'),
('f809364c-fb40-4fc6-b65a-1671f1a39a0d', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_BACKUP_LOGIN_SUCCESS', 'Admin successfully used backup code for 2FA verification', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:34:24'),
('fe9aa4e3-271c-474a-9092-3cdb295de79b', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2FA_EMAIL_SENT', 'Email verification code sent', '{\"code_id\":\"dd75cf3d-de0f-481b-b128-e00ae8a175f4\",\"expires_at\":\"2025-07-07 05:04:48\"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-07 01:54:53');

-- --------------------------------------------------------

--
-- Table structure for table `admin_2fa_settings`
--

CREATE TABLE `admin_2fa_settings` (
  `id` varchar(36) NOT NULL,
  `admin_id` varchar(36) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 0,
  `email_2fa_enabled` tinyint(1) DEFAULT 0,
  `backup_codes_enabled` tinyint(1) DEFAULT 0,
  `backup_codes_generated_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_2fa_settings`
--

INSERT INTO `admin_2fa_settings` (`id`, `admin_id`, `is_enabled`, `email_2fa_enabled`, `backup_codes_enabled`, `backup_codes_generated_at`, `created_at`, `updated_at`) VALUES
('7397173c-5782-4fb2-9cab-719a5337796a', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 1, 1, 1, '2025-07-07 01:32:00', '2025-07-07 01:22:10', '2025-07-07 01:32:00');

-- --------------------------------------------------------

--
-- Table structure for table `admin_logs`
--

CREATE TABLE `admin_logs` (
  `id` varchar(36) NOT NULL,
  `admin_id` varchar(36) NOT NULL,
  `action` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_logs`
--

INSERT INTO `admin_logs` (`id`, `admin_id`, `action`, `description`, `metadata`, `ip_address`, `user_agent`, `created_at`) VALUES
('71021c20-ad91-4cf3-8bed-1789074006f8', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'EMAIL_CHANGE', 'Admin email changed', '{\"old_email\":\"<EMAIL>\",\"new_email\":\"<EMAIL>\"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-05 19:32:05'),
('902504dd-42a6-404d-94c3-4a16eecbaeaa', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'PROFILE_UPDATE', 'Admin profile updated', '{\"updated_fields\":[\"image\"]}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-05 20:34:58'),
('97b0fd43-85f0-4e27-8907-d38156b72855', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'PROFILE_UPDATE', 'Admin profile updated', '{\"updated_fields\":[\"image\"]}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-05 20:34:49'),
('b98d5a38-43a5-4a47-8fcb-9b58c355cb0a', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'EMAIL_CHANGE', 'Admin email changed', '{\"old_email\":\"<EMAIL>\",\"new_email\":\"<EMAIL>\"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', '2025-07-05 19:31:35');

-- --------------------------------------------------------

--
-- Table structure for table `blog`
--

CREATE TABLE `blog` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `excerpt` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `blog_posts`
--

CREATE TABLE `blog_posts` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `summary` text DEFAULT NULL,
  `full_content` longtext NOT NULL,
  `image_url` varchar(2048) DEFAULT NULL,
  `publish_date` datetime DEFAULT NULL,
  `author_id` varchar(36) DEFAULT NULL,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `blog_posts`
--

INSERT INTO `blog_posts` (`id`, `title`, `slug`, `summary`, `full_content`, `image_url`, `publish_date`, `author_id`, `status`, `created_at`, `updated_at`) VALUES
('0274156e-5f36-4f7b-b95e-12ff75639264', 'Test', 'test', '💇‍♀️ Expert Stylists &amp; Therapists\r\nOur stylists are not only highly skilled, but passionate about bringing out your natural beauty. From modern cuts to classic styles, from precision coloring to deep hair repair — you’ll leave Flix looking radiant and feeling rejuvenated.', '✨ Experience the Flix Difference\r\nAt Flix, we do more than just hair and skin — we create moments of transformation. Whether you\'re prepping for a big event or simply need a self-care day, our team of trained professionals is here to make you feel your absolute best.\r\n\r\n💇‍♀️ Expert Stylists & Therapists\r\nOur stylists are not only highly skilled, but passionate about bringing out your natural beauty. From modern cuts to classic styles, from precision coloring to deep hair repair — you’ll leave Flix looking radiant and feeling rejuvenated.\r\n\r\n🌿 Premium Products, Gentle Touch\r\nWe use only the best in beauty — premium, skin-safe, and eco-conscious products that nourish while they enhance. Your hair, skin, and nails will thank you.', '937f919f-f658-4a98-82a0-dae38ea1074a.jpg', '2025-06-07 00:01:00', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'published', '2025-06-06 21:36:03', '2025-06-06 21:55:27'),
('81818206-7cf3-4d17-8530-832afe9dad24', '💖 Unwind, Refresh; Shine at Flix Salon &amp; SPA', 'unwind-refresh-shine-at-flix-salon-amp-spa', 'Welcome to Flix Salon &amp;amp; SPA, where beauty meets relaxation and confidence begins. Nestled in the heart of [Your Location], our salon and spa is your personal haven for expert beauty care, luxurious treatments, and a touch of indulgence you truly deserve.', '✨ Experience the Flix Difference\r\nAt Flix, we do more than just hair and skin — we create moments of transformation. Whether you\'re prepping for a big event or simply need a self-care day, our team of trained professionals is here to make you feel your absolute best.\r\n\r\n💇‍♀️ Expert Stylists & Therapists\r\nOur stylists are not only highly skilled, but passionate about bringing out your natural beauty. From modern cuts to classic styles, from precision coloring to deep hair repair — you’ll leave Flix looking radiant and feeling rejuvenated.\r\n\r\n🌿 Premium Products, Gentle Touch\r\nWe use only the best in beauty — premium, skin-safe, and eco-conscious products that nourish while they enhance. Your hair, skin, and nails will thank you.', 'https://img.freepik.com/free-photo/side-view-woman-talking-video-call_23-2150812833.jpg', '2025-06-05 12:01:00', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'published', '2025-06-04 01:47:20', '2025-06-06 11:01:30'),
('b6df4e5f-6952-4771-adde-87fd95074c4c', 'Test 2', 'test-2', '🌿 Premium Products, Gentle Touch\r\nWe use only the best in beauty — premium, skin-safe, and eco-conscious products that nourish while they enhance. Your hair, skin, and nails will thank you.', '🌿 Premium Products, Gentle Touch\r\nWe use only the best in beauty — premium, skin-safe, and eco-conscious products that nourish while they enhance. Your hair, skin, and nails will thank you.', 'afd8e663-998a-4d82-bfe2-2f8093de26a3.jpg', '2025-06-07 23:23:00', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'published', '2025-06-06 21:49:00', '2025-06-06 21:55:43');

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `service_id` varchar(36) DEFAULT NULL,
  `package_id` varchar(36) DEFAULT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `offer_id` varchar(36) DEFAULT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `status` enum('PENDING','CONFIRMED','IN_PROGRESS','COMPLETED','CANCELLED','NO_SHOW','EXPIRED') DEFAULT 'PENDING',
  `total_amount` int(11) NOT NULL,
  `points_used` int(11) DEFAULT 0,
  `points_earned` int(11) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `service_variation_id` varchar(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`id`, `user_id`, `service_id`, `package_id`, `staff_id`, `offer_id`, `date`, `start_time`, `end_time`, `status`, `total_amount`, `points_used`, `points_earned`, `notes`, `created_at`, `updated_at`, `service_variation_id`) VALUES
('1986c2b1-5a30-4f93-b69e-35a105783ecc', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', 'c001781b-954d-4ca6-a48a-66e20b6b1ed7', NULL, '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, '2025-07-01', '12:03:00', '13:03:00', 'EXPIRED', 45000, 0, 0, '', '2025-06-30 18:36:05', '2025-07-03 21:58:00', NULL),
('4d21b14a-4ddc-4ba5-852b-18b4acdffdb3', '4544f774-891d-4333-85c4-e78c998882cf', '6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', NULL, '8c42848c-6123-4438-9f23-8f5c07680371', NULL, '2025-07-04', '12:02:00', '13:02:00', 'PENDING', 550000, 0, 0, '', '2025-06-30 18:37:31', '2025-06-30 18:37:31', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `booking_reminders`
--

CREATE TABLE `booking_reminders` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `reminder_type` enum('24_HOURS','1_DAY','30_MINUTES','AT_TIME') NOT NULL,
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') NOT NULL DEFAULT 'MEDIUM',
  `status` enum('PENDING','SENT','FAILED','SKIPPED') NOT NULL DEFAULT 'PENDING',
  `scheduled_time` datetime NOT NULL,
  `sent_time` datetime DEFAULT NULL,
  `attempts` int(11) DEFAULT 0,
  `max_attempts` int(11) DEFAULT 3,
  `customer_email_sent` tinyint(1) DEFAULT 0,
  `staff_email_sent` tinyint(1) DEFAULT 0,
  `customer_email_status` enum('PENDING','SENT','FAILED') DEFAULT 'PENDING',
  `staff_email_status` enum('PENDING','SENT','FAILED') DEFAULT 'PENDING',
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `booking_reminders`
--

INSERT INTO `booking_reminders` (`id`, `booking_id`, `reminder_type`, `priority`, `status`, `scheduled_time`, `sent_time`, `attempts`, `max_attempts`, `customer_email_sent`, `staff_email_sent`, `customer_email_status`, `staff_email_status`, `error_message`, `created_at`, `updated_at`) VALUES
('2bbd1cb4-33bf-4de1-9e48-38750ca03f13', '4d21b14a-4ddc-4ba5-852b-18b4acdffdb3', 'AT_TIME', 'URGENT', 'PENDING', '2025-07-04 12:02:00', NULL, 0, 2, 0, 0, 'PENDING', 'PENDING', NULL, '2025-07-03 15:26:32', '2025-07-03 15:26:32'),
('cdb7814c-ecc6-4d41-9eaf-8f36bbf2b2f3', '4d21b14a-4ddc-4ba5-852b-18b4acdffdb3', '30_MINUTES', 'HIGH', 'PENDING', '2025-07-04 11:32:00', NULL, 0, 3, 0, 0, 'PENDING', 'PENDING', NULL, '2025-07-03 15:26:32', '2025-07-03 15:26:32');

-- --------------------------------------------------------

--
-- Table structure for table `booking_status_log`
--

CREATE TABLE `booking_status_log` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `old_status` varchar(20) NOT NULL,
  `new_status` varchar(20) NOT NULL,
  `changed_by` varchar(36) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cms_content`
--

CREATE TABLE `cms_content` (
  `id` varchar(36) NOT NULL,
  `section` varchar(100) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `order_index` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cms_content`
--

INSERT INTO `cms_content` (`id`, `section`, `title`, `content`, `image`, `order_index`, `is_active`, `created_at`, `updated_at`) VALUES
('1614ac99-e2f0-4a1b-90d8-292b34ab11e9', 'hero', 'Welcome to Flix Salonce', 'Experience luxury and elegance at our premium beauty salon', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('5cb3b7c9-110e-498d-9544-3229756b3f48', 'mission', 'Our Mission', 'To enhance natural beauty and boost confidence through professional beauty services', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('a92aa99e-c89b-43d5-a1b5-5c3230200795', 'about', 'About Our Salon', 'We are dedicated to providing exceptional beauty services in a luxurious environment', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `contact_messages`
--

CREATE TABLE `contact_messages` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `status` enum('NEW','READ','REPLIED','ARCHIVED') DEFAULT 'NEW',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_messages`
--

CREATE TABLE `customer_messages` (
  `id` varchar(36) NOT NULL,
  `customer_id` varchar(36) NOT NULL,
  `admin_id` varchar(36) NOT NULL,
  `message_type` enum('email','sms','both') NOT NULL DEFAULT 'email',
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `status` enum('SENT','DELIVERED','FAILED','PENDING') NOT NULL DEFAULT 'PENDING',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customer_messages`
--

INSERT INTO `customer_messages` (`id`, `customer_id`, `admin_id`, `message_type`, `subject`, `message`, `status`, `created_at`, `updated_at`) VALUES
('83620440-7357-435b-9404-a918b2d8d97c', '4544f774-891d-4333-85c4-e78c998882cf', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'email', 'Special Offer Just for You!', 'Hi chare,\r\n\r\nWe have a special promotion just for you at Flix Salonce!\r\n\r\n[Add promotion details here]\r\n\r\nBook your appointment today!\r\n\r\nBest regards,\r\nFlix Salonce Team', 'SENT', '2025-06-27 01:39:19', '2025-06-27 01:39:27'),
('f07a077a-8071-4dcd-9f19-6654bb21b0aa', '4544f774-891d-4333-85c4-e78c998882cf', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'email', 'Thank You - Flix Salonce', 'Dear chare,\r\n\r\nThank you for choosing Flix Salonce! We hope you loved your recent visit.\r\n\r\nYour current loyalty points balance: 606\r\n\r\nWe look forward to seeing you again soon!\r\n\r\nBest regards,\r\nFlix Salonce Team', 'SENT', '2025-06-24 11:12:33', '2025-06-24 11:12:39');

-- --------------------------------------------------------

--
-- Table structure for table `customer_tiers`
--

CREATE TABLE `customer_tiers` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL,
  `min_spent` int(11) NOT NULL DEFAULT 0,
  `points_multiplier` decimal(3,2) NOT NULL DEFAULT 1.00,
  `benefits` text DEFAULT NULL,
  `color` varchar(50) NOT NULL DEFAULT 'text-gray-600',
  `bg_color` varchar(50) NOT NULL DEFAULT 'bg-gray-100',
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customer_tiers`
--

INSERT INTO `customer_tiers` (`id`, `name`, `min_spent`, `points_multiplier`, `benefits`, `color`, `bg_color`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
('7d82b1a0-98df-48f1-b4e9-27c4c9c2a2e9', 'Gold', 500000, 1.50, '50% bonus points,Priority booking,Exclusive offers,Birthday bonus', 'text-yellow-600', 'bg-yellow-100', 1, 3, '2025-06-10 00:49:08', '2025-06-10 00:49:08'),
('9e8335c8-9bf7-48f5-bcbc-85952993fcc4', 'VIP', 1000000, 2.00, 'Double points,VIP treatment,Exclusive services,Personal stylist,Birthday bonus', 'text-purple-600', 'bg-purple-100', 1, 4, '2025-06-10 00:49:08', '2025-06-10 00:49:08'),
('a901e979-dfcb-41e1-9160-00bac908a724', 'Bronze', 0, 1.00, 'Basic rewards,Birthday bonus', 'text-orange-600', 'bg-orange-100', 1, 1, '2025-06-10 00:49:08', '2025-06-10 00:49:08'),
('c03dfd2f-1a6b-4c6a-9679-e9abedf2f7b5', 'Silver', 300000, 1.20, '20% bonus points,Priority booking,Birthday bonus', 'text-gray-600', 'bg-gray-100', 1, 2, '2025-06-10 00:49:08', '2025-06-10 01:15:36'),
('eeccb82c-3e3a-4a79-bc6d-1a9c1f373bff', 'Updated Test Tier', 75000, 1.10, 'Test benefit 1,Test benefit 2', 'text-blue-600', 'bg-blue-100', 0, 99, '2025-06-10 00:57:17', '2025-06-10 01:16:16');

-- --------------------------------------------------------

--
-- Table structure for table `custom_services`
--

CREATE TABLE `custom_services` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `duration` int(11) NOT NULL COMMENT 'Duration in minutes',
  `category` varchar(100) DEFAULT 'Custom',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `custom_services`
--

INSERT INTO `custom_services` (`id`, `name`, `description`, `price`, `duration`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
('cs-001', 'VIP Consultation', 'Personalized beauty consultation with senior stylist', 15000.00, 60, 'Consultation', 1, '2025-06-22 06:24:55', '2025-06-22 06:24:55'),
('cs-002', 'Express Touch-up', 'Quick styling touch-up service', 8000.00, 30, 'Styling', 1, '2025-06-22 06:24:55', '2025-06-22 06:24:55'),
('cs-003', 'Premium Hair Treatment', 'Luxury hair treatment with imported products', 25000.00, 90, 'Hair Care', 1, '2025-06-22 06:24:55', '2025-06-22 06:24:55'),
('cs-004', 'Bridal Makeup Trial', 'Complete bridal makeup trial session', 20000.00, 120, 'Bridal', 1, '2025-06-22 06:24:55', '2025-06-22 06:24:55'),
('cs-005', 'Skin Analysis', 'Professional skin analysis and recommendation', 12000.00, 45, 'Skincare', 1, '2025-06-22 06:24:55', '2025-06-22 06:24:55');

-- --------------------------------------------------------

--
-- Table structure for table `email_logs`
--

CREATE TABLE `email_logs` (
  `id` int(11) NOT NULL,
  `recipient` varchar(255) NOT NULL,
  `subject` varchar(500) NOT NULL,
  `status` enum('SENT','FAILED','PENDING') DEFAULT 'PENDING',
  `details` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_logs`
--

INSERT INTO `email_logs` (`id`, `recipient`, `subject`, `status`, `details`, `created_at`) VALUES
(122, '<EMAIL>', 'Special Offer Just for You!', 'SENT', '', '2025-06-27 01:39:27'),
(123, '<EMAIL>', 'Booking Request Received - Flix Salon & SPA', 'FAILED', 'SMTP Error: The following recipients failed: <EMAIL>: The mail server could not deliver <NAME_EMAIL>.  The account or\r\ndomain may not exist, they may be blacklisted, or missing the proper dns\r\nentries.\r\n', '2025-06-30 18:36:10'),
(124, '<EMAIL>', 'Booking Request Received - Flix Salon & SPA', 'SENT', '', '2025-06-30 18:37:35'),
(125, '<EMAIL>', 'Two-Factor Authentication Code - Flix Salonce', 'SENT', '', '2025-07-07 01:54:53');

-- --------------------------------------------------------

--
-- Table structure for table `faqs`
--

CREATE TABLE `faqs` (
  `id` int(11) NOT NULL,
  `category` varchar(100) NOT NULL,
  `question` text NOT NULL,
  `answer` text NOT NULL,
  `display_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `faqs`
--

INSERT INTO `faqs` (`id`, `category`, `question`, `answer`, `display_order`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'general', 'What services do you offer?', 'We offer a comprehensive range of luxury beauty services including professional hair cutting, advanced coloring techniques, styling, makeup application, premium skincare treatments, nail services, and rejuvenating spa treatments. ', 1, 1, '2025-06-22 01:18:03', '2025-06-22 01:55:57'),
(2, 'general', 'What are your operating hours?', 'We\'re open Monday-Friday 9:00 AM - 7:00 PM, Saturday 8:00 AM - 6:00 PM, and closed on Sundays for staff rest and training. Holiday hours may vary - please check our website or call ahead during holiday periods for updated schedules.', 2, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(3, 'general', 'Do you accept walk-ins?', 'While we welcome walk-ins when possible, we highly recommend booking an appointment to ensure availability and minimize wait times. Our premium services require proper preparation and dedicated time with our specialists for the best results.', 3, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(4, 'booking', 'How do I book an appointment?', 'You can book online through our website\'s secure booking system, call us at (*************, or visit us in person. Our online booking is available 24/7 for your convenience and allows you to select your preferred stylist, view real-time availability, and receive instant confirmation.', 1, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(5, 'booking', 'Can I cancel or reschedule my appointment?', 'Yes, you can cancel or reschedule up to 24 hours before your appointment without penalty. Same-day cancellations may incur a fee. We understand that plans change, so we offer flexible rescheduling options through our online portal or by calling us directly.', 2, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(6, 'booking', 'How far in advance should I book?', 'We recommend booking 1-2 weeks in advance for regular services and 3-4 weeks for special events or bridal services. Popular time slots and preferred stylists fill up quickly, especially during peak seasons and weekends.', 3, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(7, 'payment', 'What payment methods do you accept?', 'We accept cash, all major credit cards (Visa, MasterCard), debit cards, and modern digital payments including Apple Pay, Google Pay, and contactless payments for your convenience and security.', 1, 1, '2025-06-22 01:18:03', '2025-06-23 00:37:59'),
(8, 'payment', 'Do you offer gift cards?', 'Yes! We offer both digital and physical gift cards in any amount. They make perfect gifts for any occasion and never expire. You can purchase them online, in-person, or by phone. Gift cards can be used for any of our services.', 2, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(9, 'payment', 'What is your tipping policy?', 'Tips are appreciated but not required. Standard tipping is 15-20% of the service cost. You can add tips to your card payment or give cash directly to your stylist. Our team values your satisfaction above all else.', 3, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(10, 'safety', 'What safety measures do you have in place?', 'We follow strict sanitation protocols, use hospital-grade disinfectants, sterilize all tools between clients, and maintain a clean, safe environment. Our staff is trained in the latest safety procedures to ensure your health and wellbeing.', 1, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(11, 'safety', 'Do you accommodate allergies and sensitivities?', 'Absolutely! Please inform us of any allergies or sensitivities when booking. We offer hypoallergenic products and can perform patch tests when needed. Your safety and comfort are our top priorities.', 2, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03'),
(12, 'safety', 'Are your products cruelty-free?', 'Yes, we are committed to using only cruelty-free products. We carefully select premium brands that align with our ethical standards and don\'t test on animals. Beauty should never come at the expense of animal welfare.', 3, 1, '2025-06-22 01:18:03', '2025-06-22 01:18:03');

-- --------------------------------------------------------

--
-- Table structure for table `gallery`
--

CREATE TABLE `gallery` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image_url` varchar(255) NOT NULL,
  `category` varchar(100) NOT NULL,
  `order_index` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `gallery`
--

INSERT INTO `gallery` (`id`, `title`, `description`, `image_url`, `category`, `order_index`, `is_active`, `created_at`, `updated_at`) VALUES
('203ba46b-f6f9-40ae-82be-bc70d7af0d5f', 'Beauty Treatment', 'Luxury beauty service', 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=600', 'Beauty', 0, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('3092b133-ccdc-43e9-8b47-197c866354b9', 'Facial Treatment', 'Relaxing facial session', 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=600', 'Facial', 0, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('46b0e2ae-0076-4419-b6d4-3b2207a5ca9a', 'Makeup Application', 'Professional makeup', 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=600', 'Beauty', 0, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('7245c1a5-f08e-4a83-a9b1-ffa3d2d25c07', 'Salon Interior New', 'Modern salon space', 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=600', 'Interior', 0, 1, '2025-06-01 00:06:02', '2025-07-01 22:14:03'),
('8d967d81-4c0c-4529-8ac5-9fd501bc6f23', 'Hair Coloring', 'Professional hair coloring', 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600', 'Hair', 0, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('b46b7efc-41e9-4f53-a2f3-b1924989b237', 'Nail Art Design', 'Creative nail art', 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=600', 'Nails', 0, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('da84b87c-552e-4ba3-bc7f-574ff41fe3c1', 'Massage Therapy', 'Therapeutic massage session', 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=600', 'Massage', 0, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('f4c968f3-40ae-45b2-a1df-0f78a85e1af9', 'Hair Styling Work', 'Beautiful hair transformation', 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=600', 'Hair', 0, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `newsletter`
--

CREATE TABLE `newsletter` (
  `id` varchar(36) NOT NULL,
  `email` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `newsletter`
--

INSERT INTO `newsletter` (`id`, `email`, `is_active`, `created_at`) VALUES
('', '<EMAIL>', 1, '2025-06-01 01:51:19'),
('2bba5856-37f8-4c86-a3aa-fa1147359853', '<EMAIL>', 1, '2025-06-06 11:26:52'),
('5ba535e5-f905-4cf3-bf57-5fbc2afbbc19', '<EMAIL>', 1, '2025-06-06 11:29:23');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('BOOKING_NEW','BOOKING_CONFIRMED','BOOKING_CANCELLED','BOOKING_COMPLETED','BOOKING_REMINDER','BOOKING_EXPIRED','BOOKING_NO_SHOW','BOOKING_REMINDER_24H','BOOKING_REMINDER_5H','BOOKING_REMINDER_30M','BOOKING_REMINDER_NOW','CUSTOMER_NEW','CUSTOMER_BIRTHDAY','NEWSLETTER_SUBSCRIBER','PAYMENT_SUCCESS','PAYMENT_FAILED','REFUND_PROCESSED','STAFF_NEW','STAFF_SCHEDULE_CHANGE','STAFF_LEAVE_REQUEST','SYSTEM_MAINTENANCE','SYSTEM_UPDATE','SYSTEM_BACKUP','PROMOTION_NEW','OFFER_EXPIRING','LOYALTY_MILESTONE','REVIEW_NEW','COMPLAINT_NEW','GENERAL','ADMIN_STAFF_ASSIGNED','ADMIN_STAFF_UNASSIGNED','ADMIN_BOOKING_UPDATED','ADMIN_STATUS_CHANGED','ADMIN_BOOKING_RESCHEDULED','ADMIN_BOOKING_DETAILS_CHANGED') NOT NULL,
  `category` enum('BOOKING','CUSTOMER','STAFF','PAYMENT','SYSTEM','MARKETING','FEEDBACK') NOT NULL,
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') DEFAULT 'MEDIUM',
  `is_read` tinyint(1) DEFAULT 0,
  `action_url` varchar(500) DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `category`, `priority`, `is_read`, `action_url`, `metadata`, `expires_at`, `created_at`, `updated_at`) VALUES
('04b1c2a2-d0ea-46de-9415-b3272668e0c1', '8c42848c-6123-4438-9f23-8f5c07680371', 'New Booking Assignment', 'You have been assigned a new booking with chare for Facial Treatment on 2025-06-20 at 14:03:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-20', '{\"booking_id\":\"60b88d4a-33c5-49d7-a13a-6c95fb2a63d5\",\"customer_name\":\"chare\",\"service_name\":\"Facial Treatment\",\"staff_name\":\"Staff Member\",\"booking_date\":\"2025-06-20\",\"booking_time\":\"14:03:00\"}', NULL, '2025-06-12 00:57:50', '2025-06-12 00:57:50'),
('15d78655-e7bb-406c-8324-0907e36f5638', '8c42848c-6123-4438-9f23-8f5c07680371', 'New Booking Assignment', 'You have been assigned a new booking with chare for Gel Manicure on 2025-07-04 at 12:02:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-07-04', '{\"booking_id\":\"4d21b14a-4ddc-4ba5-852b-18b4acdffdb3\",\"customer_name\":\"chare\",\"service_name\":\"Gel Manicure\",\"staff_name\":\"Staff Test\",\"booking_date\":\"2025-07-04\",\"booking_time\":\"12:02:00\"}', NULL, '2025-06-30 18:37:31', '2025-06-30 18:37:31'),
('22d258e0-3373-4272-97b7-2affc34f27b9', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Pedicure new on 2025-06-19 at 11:01:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-19', '{\"booking_id\":\"60ccb7ef-56bc-4451-8c5b-222f9eda95d6\",\"customer_name\":\"chare\",\"service_name\":\"Pedicure new\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-19\",\"booking_time\":\"11:01:00\"}', NULL, '2025-06-18 19:54:07', '2025-06-18 19:54:07'),
('325fb1c1-95a3-4829-a6b2-e22e67409729', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Makeup Application on 2025-06-27 at 11:02:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-27', '{\"booking_id\":\"6fc0483e-cf31-4d41-afb5-182c2ae73a1d\",\"customer_name\":\"chare\",\"service_name\":\"Makeup Application\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-27\",\"booking_time\":\"11:02:00\"}', NULL, '2025-06-24 12:05:05', '2025-06-24 12:05:05'),
('4ddc0316-57a0-4354-945e-42ccabf71630', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Hair Highlights on 2025-06-24 at 13:23:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-24', '{\"booking_id\":\"c66c2709-cc98-41bd-8b82-d00cff838694\",\"customer_name\":\"chare\",\"service_name\":\"Hair Highlights\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-24\",\"booking_time\":\"13:23:00\"}', NULL, '2025-06-22 22:42:43', '2025-06-22 22:42:43'),
('500e6a63-9746-4e54-848d-08674cc28247', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Bridal Package on 2025-06-24 at 15:54:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-24', '{\"booking_id\":\"0bfeee21-6925-4a63-82f7-70ffac4df11c\",\"customer_name\":\"chare\",\"service_name\":\"Bridal Package\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-24\",\"booking_time\":\"15:54:00\"}', NULL, '2025-06-22 22:45:32', '2025-06-22 22:45:32'),
('5577709a-e2c6-4240-8e48-d09a0973021a', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with Ailya Hassan for RERE on 2025-06-20 at 13:32:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-20', '{\"booking_id\":\"66910982-603d-4166-a36e-bb6662b744e4\",\"customer_name\":\"Ailya Hassan\",\"service_name\":\"RERE\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-20\",\"booking_time\":\"13:32:00\"}', NULL, '2025-06-18 23:59:42', '2025-06-18 23:59:42'),
('67634806-7f0d-411b-b763-a2ef4faab387', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Bridal Package on 2025-06-24 at 11:11:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-24', '{\"booking_id\":\"442cabbd-53eb-4a76-91a8-ebeaf4729f1f\",\"customer_name\":\"chare\",\"service_name\":\"Bridal Package\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-24\",\"booking_time\":\"11:11:00\"}', NULL, '2025-06-22 22:33:20', '2025-06-22 22:33:20'),
('78fead6b-1c01-4552-9b89-d28233797a69', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'chare has requested a booking for Gel Manicure on 2025-07-04 at 12:02:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/admin/bookings/view.php?id=4d21b14a-4ddc-4ba5-852b-18b4acdffdb3', '{\"booking_id\":\"4d21b14a-4ddc-4ba5-852b-18b4acdffdb3\",\"customer_name\":\"chare\",\"service_name\":\"Gel Manicure\",\"staff_name\":\"Staff Test\",\"booking_date\":\"2025-07-04\",\"booking_time\":\"12:02:00\"}', NULL, '2025-06-30 18:37:31', '2025-06-30 18:37:31'),
('82585f01-8deb-4168-be4c-8c51e41e3c55', '8c42848c-6123-4438-9f23-8f5c07680371', 'New Booking Assignment', 'You have been assigned a new booking with chare for Facial Treatment on 2025-06-20 at 11:00:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-20', '{\"booking_id\":\"f239740e-6c55-4eb9-85e2-52fc8fc26d36\",\"customer_name\":\"chare\",\"service_name\":\"Facial Treatment\",\"staff_name\":\"Staff Member\",\"booking_date\":\"2025-06-20\",\"booking_time\":\"11:00:00\"}', NULL, '2025-06-12 00:58:53', '2025-06-12 00:58:53'),
('84effd67-935c-410a-8484-967412aba3c8', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Bridal Package on 2025-06-25 at 13:34:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-25', '{\"booking_id\":\"23b0e804-38fe-4cc6-bf7f-361d5016402d\",\"customer_name\":\"chare\",\"service_name\":\"Bridal Package\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-25\",\"booking_time\":\"13:34:00\"}', NULL, '2025-06-22 22:52:42', '2025-06-22 22:52:42'),
('89a14602-240a-462b-a390-0ce0492898e2', '8c42848c-6123-4438-9f23-8f5c07680371', 'New Booking Assignment', 'You have been assigned a new booking with chare for Pedicure new on 2025-06-20 at 11:11:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-20', '{\"booking_id\":\"0088304a-bc36-4644-a89a-6493193fc56f\",\"customer_name\":\"chare\",\"service_name\":\"Pedicure new\",\"staff_name\":\"Staff Test\",\"booking_date\":\"2025-06-20\",\"booking_time\":\"11:11:00\"}', NULL, '2025-06-18 20:20:40', '2025-06-18 20:20:40'),
('9522d338-1153-47fd-a179-fb86c29a0fd9', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Eyebrow Shaping on 2025-06-24 at 12:23:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-24', '{\"booking_id\":\"5f339d3c-0775-45ed-9145-a28540b44215\",\"customer_name\":\"chare\",\"service_name\":\"Eyebrow Shaping\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-24\",\"booking_time\":\"12:23:00\"}', NULL, '2025-06-22 22:51:46', '2025-06-22 22:51:46'),
('9e084573-00e8-4a43-af5a-9fc06d762be5', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with Ailya Hassan for Hair Cut & Style on 2025-07-01 at 12:03:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-07-01', '{\"booking_id\":\"1986c2b1-5a30-4f93-b69e-35a105783ecc\",\"customer_name\":\"Ailya Hassan\",\"service_name\":\"Hair Cut & Style\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-07-01\",\"booking_time\":\"12:03:00\"}', NULL, '2025-06-30 18:36:05', '2025-06-30 18:36:05'),
('a1341165-cb12-4d6e-aa80-88046ad6ddd9', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'chare has requested a booking for Makeup Application on 2025-06-27 at 11:02:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/admin/bookings/view.php?id=6fc0483e-cf31-4d41-afb5-182c2ae73a1d', '{\"booking_id\":\"6fc0483e-cf31-4d41-afb5-182c2ae73a1d\",\"customer_name\":\"chare\",\"service_name\":\"Makeup Application\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-27\",\"booking_time\":\"11:02:00\"}', NULL, '2025-06-24 12:05:05', '2025-06-24 12:05:05'),
('af355479-fd88-400e-b4d0-e28fc50607df', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Hair Highlights on 2025-06-14 at 11:11:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 1, '/staff/appointments?date=2025-06-14', '{\"booking_id\":\"a71a3c93-c69b-407f-a995-e5b02e830227\",\"customer_name\":\"chare\",\"service_name\":\"Hair Highlights\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-14\",\"booking_time\":\"11:11:00\"}', NULL, '2025-06-14 00:12:24', '2025-06-14 01:35:29'),
('b391d8a1-d5b3-406c-823d-57076e0809f2', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'Ailya Hassan has requested a booking for Hair Cut & Style on 2025-07-01 at 12:03:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/admin/bookings/view.php?id=1986c2b1-5a30-4f93-b69e-35a105783ecc', '{\"booking_id\":\"1986c2b1-5a30-4f93-b69e-35a105783ecc\",\"customer_name\":\"Ailya Hassan\",\"service_name\":\"Hair Cut & Style\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-07-01\",\"booking_time\":\"12:03:00\"}', NULL, '2025-06-30 18:36:05', '2025-06-30 18:36:05'),
('b572fb12-9a7a-4c66-b7bb-3f2679e45f47', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'Booking Cancelled', 'Booking for chare (Makeup Application) on 2025-06-27 has been cancelled', 'BOOKING_CANCELLED', 'BOOKING', 'MEDIUM', 0, '/admin/bookings/view.php?id=6fc0483e-cf31-4d41-afb5-182c2ae73a1d', '{\"booking_id\":\"6fc0483e-cf31-4d41-afb5-182c2ae73a1d\",\"customer_name\":\"chare\",\"service_name\":\"Makeup Application\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-27\",\"booking_time\":\"11:02:00\"}', NULL, '2025-06-24 12:05:45', '2025-06-24 12:05:45'),
('eb886f36-4c39-49a0-82ce-ef310060b0d1', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with Ailya Hassan for Swedish Massage on 2025-06-25 at 10:19:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-25', '{\"booking_id\":\"3587e46a-3808-44c8-b5cd-ae818f6893d1\",\"customer_name\":\"Ailya Hassan\",\"service_name\":\"Swedish Massage\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-25\",\"booking_time\":\"10:19:00\"}', NULL, '2025-06-22 23:19:32', '2025-06-22 23:19:32'),
('ef09d913-1b99-4f34-a295-2e73552a2a50', '8c42848c-6123-4438-9f23-8f5c07680371', 'New Booking Assignment', 'You have been assigned a new booking with chare for Hair Transformation on 2025-06-24 at 12:12:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-24', '{\"booking_id\":\"cded7743-a2cb-497d-a53e-bbd31670ca25\",\"customer_name\":\"chare\",\"service_name\":\"Hair Transformation\",\"staff_name\":\"Staff Test\",\"booking_date\":\"2025-06-24\",\"booking_time\":\"12:12:00\"}', NULL, '2025-06-22 22:40:53', '2025-06-22 22:40:53'),
('feab4c80-9603-486d-8f4f-ed84d8898ee1', '8c42848c-6123-4438-9f23-8f5c07680371', 'New Booking Assignment', 'You have been assigned a new booking with chare for Pedicure new on 2025-06-19 at 10:01:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-19', '{\"booking_id\":\"caafb291-d1cd-4d09-9c51-fca76ff73dc8\",\"customer_name\":\"chare\",\"service_name\":\"Pedicure new\",\"staff_name\":\"Staff Test\",\"booking_date\":\"2025-06-19\",\"booking_time\":\"10:01:00\"}', NULL, '2025-06-18 20:35:21', '2025-06-18 20:35:21');

-- --------------------------------------------------------

--
-- Table structure for table `offers`
--

CREATE TABLE `offers` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `discount` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `valid_from` datetime NOT NULL,
  `valid_to` datetime NOT NULL,
  `max_usage` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `offers`
--

INSERT INTO `offers` (`id`, `title`, `description`, `discount`, `code`, `valid_from`, `valid_to`, `max_usage`, `is_active`, `image`, `created_at`, `updated_at`) VALUES
('b93eead3-2c1b-46cc-bc52-4003ea3b8ab7', 'New Customer Special', 'Get 20% off your first visits', 5, 'WELCOME20', '2025-06-06 00:00:00', '2025-09-01 00:00:00', 100, 1, 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400', '2025-06-01 00:06:02', '2025-06-06 13:06:41'),
('c259412c-36cf-4138-ad4f-746eb31add9b', 'Referral Bonus', 'Refer a friend and both get 15% off', 15, 'REFER15', '2025-06-01 03:06:02', '2025-12-01 03:06:02', NULL, 1, 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400', '2025-06-01 00:06:02', '2025-06-05 22:32:28'),
('cf2926c4-21f0-4bd3-8ad4-93fa338810e3', 'Summer Glow Package', 'Special summer facial and hair treatment combo', 25, 'SUMMER25', '2025-06-01 03:06:02', '2025-08-01 03:06:02', 50, 1, 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400', '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `packages`
--

CREATE TABLE `packages` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` int(11) NOT NULL,
  `discount` int(11) DEFAULT 0,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `package_type` enum('REGULAR','CUSTOM','MIXED') DEFAULT 'REGULAR',
  `manual_services` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON array of manually entered services for this package' CHECK (json_valid(`manual_services`)),
  `package_duration` int(11) DEFAULT 0 COMMENT 'Overall package duration in minutes (optional)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `packages`
--

INSERT INTO `packages` (`id`, `name`, `description`, `price`, `discount`, `image`, `is_active`, `created_at`, `updated_at`, `package_type`, `manual_services`, `package_duration`) VALUES
('7118dab3-3184-4113-8791-36da83d868cc', 'Bridal Package', 'Look radiant on your big day with our expert bridal makeup, hair styling, and beauty treatments.', 500000, 0, 'acc5c148-7688-4eef-a0e2-69e91c4b6494.jpg', 1, '2025-06-22 07:03:58', '2025-06-22 22:19:21', 'CUSTOM', '[{\"name\":\"Ist Scrub\",\"description\":\"\",\"price\":0,\"duration\":0},{\"name\":\"2nd Facial\",\"description\":\"\",\"price\":0,\"duration\":0},{\"name\":\"3rd Hairstyle\",\"description\":\"\",\"price\":0,\"duration\":0}]', 60),
('97a321b1-9598-4831-b4eb-ea1f92a70903', 'Test Package with Image', 'Test package for debugging image functionality', 500000, 0, 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305147.jpg', 1, '2025-06-01 16:18:06', '2025-06-05 21:00:12', 'REGULAR', NULL, 0),
('a0f1c3f8-f561-4aa8-af25-59d8ac475cce', 'Hair Transformation', 'Complete hair makeover with cut, color, and styling', 160000, 10, 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305132.jpg', 1, '2025-06-01 00:06:02', '2025-06-05 23:14:05', 'REGULAR', NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `package_custom_services`
--

CREATE TABLE `package_custom_services` (
  `id` varchar(36) NOT NULL,
  `package_id` varchar(36) NOT NULL,
  `custom_service_id` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `package_services`
--

CREATE TABLE `package_services` (
  `id` varchar(36) NOT NULL,
  `package_id` varchar(36) NOT NULL,
  `service_id` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `package_services`
--

INSERT INTO `package_services` (`id`, `package_id`, `service_id`, `created_at`) VALUES
('2fa862c6-a398-49d7-ba13-594cd199e597', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', '386ff31e-0274-443f-8807-53202fa0b7fe', '2025-06-05 23:14:05'),
('54a7f459-5c9b-4d1e-8632-d1ec84eee84c', '97a321b1-9598-4831-b4eb-ea1f92a70903', '386ff31e-0274-443f-8807-53202fa0b7fe', '2025-06-05 21:00:12'),
('730aba05-8d85-4f7a-ac32-697fdde3c1f1', '97a321b1-9598-4831-b4eb-ea1f92a70903', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', '2025-06-05 21:00:12'),
('9b391928-4d7c-4d19-97e1-d4e3c78b75f7', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', 'cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', '2025-06-05 23:14:05'),
('aa8673ed-251e-46b8-9389-5df3d4e109b2', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', '2025-06-05 23:14:05');

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `amount` int(11) NOT NULL COMMENT 'Amount in smallest currency unit',
  `currency` varchar(3) DEFAULT 'TZS',
  `status` enum('PENDING','COMPLETED','FAILED','REFUNDED') DEFAULT 'PENDING',
  `payment_method` varchar(50) DEFAULT 'card',
  `stripe_payment_id` varchar(255) DEFAULT NULL,
  `dpo_token` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `flutterwave_tx_ref` varchar(255) DEFAULT NULL,
  `flutterwave_tx_id` varchar(255) DEFAULT NULL,
  `payment_gateway` enum('DPO','STRIPE','FLUTTERWAVE') DEFAULT 'DPO',
  `payment_reference` varchar(255) DEFAULT NULL,
  `payment_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`payment_data`)),
  `webhook_verified` tinyint(1) DEFAULT 0,
  `verification_attempts` int(11) DEFAULT 0,
  `last_verification_attempt` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_logs`
--

CREATE TABLE `payment_logs` (
  `id` varchar(36) NOT NULL,
  `payment_id` varchar(36) NOT NULL,
  `event_type` enum('CREATED','PROCESSING','COMPLETED','FAILED','WEBHOOK_RECEIVED','VERIFIED','REFUNDED') NOT NULL,
  `gateway` enum('DPO','STRIPE','FLUTTERWAVE') NOT NULL,
  `event_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`event_data`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_webhooks`
--

CREATE TABLE `payment_webhooks` (
  `id` varchar(36) NOT NULL,
  `gateway` enum('DPO','STRIPE','FLUTTERWAVE') NOT NULL,
  `webhook_id` varchar(255) NOT NULL,
  `event_type` varchar(100) NOT NULL,
  `payment_id` varchar(36) DEFAULT NULL,
  `status` enum('PENDING','PROCESSED','FAILED','IGNORED') DEFAULT 'PENDING',
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`payload`)),
  `signature_verified` tinyint(1) DEFAULT 0,
  `processed_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `point_transactions`
--

CREATE TABLE `point_transactions` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `points` int(11) NOT NULL,
  `type` enum('EARNED','REDEMPTION','BONUS','REFUND') NOT NULL,
  `description` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `booking_id` varchar(36) DEFAULT NULL,
  `reward_id` varchar(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `point_transactions`
--

INSERT INTO `point_transactions` (`id`, `user_id`, `points`, `type`, `description`, `created_at`, `booking_id`, `reward_id`) VALUES
('', '4544f774-891d-4333-85c4-e78c998882cf', 200, 'BONUS', 'Referral bonus', '2025-06-14 18:25:37', NULL, NULL),
('02f04575-6741-4741-9ae4-0324ac35de71', '4544f774-891d-4333-85c4-e78c998882cf', 200, 'BONUS', 'Referral bonus', '2025-06-14 18:47:18', NULL, NULL),
('2cb1da28-ef67-4f3e-a36d-5619249d9aeb', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', 200, 'BONUS', 'Referral bonus', '2025-06-14 18:43:47', NULL, NULL),
('37ddc6ae-6335-4ca7-ba48-447fb7058c91', '12a6f3a6-7f1d-4bfc-87c9-147153949d9a', 100, 'BONUS', 'Welcome bonus', '2025-06-14 18:47:18', NULL, NULL),
('4c96f44a-86bb-408d-8145-bff1bc0f594f', '4544f774-891d-4333-85c4-e78c998882cf', 2, 'EARNED', 'Points earned from booking payment', '2025-06-08 19:45:58', NULL, NULL),
('5cb847f1-7fd1-4388-bd16-23ebb1eb4c21', '4544f774-891d-4333-85c4-e78c998882cf', 2, 'EARNED', 'Points earned from booking payment', '2025-06-08 19:48:18', NULL, NULL),
('840bf357-ae89-4c58-8943-3b8604e32793', '4544f774-891d-4333-85c4-e78c998882cf', 2, 'EARNED', 'Points earned from booking payment', '2025-06-10 00:40:47', NULL, NULL),
('89c81480-f2bb-4573-9e69-6f05fb5ac367', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', -950, '', 'Points removed due to booking expiration', '2025-06-07 22:04:53', 'd0572cef-f40a-44f4-b75a-70a5b6899e78', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `reminder_logs`
--

CREATE TABLE `reminder_logs` (
  `id` int(11) NOT NULL,
  `reminder_id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `action` enum('CREATED','SENT','FAILED','RETRY','SKIPPED') NOT NULL,
  `recipient_type` enum('CUSTOMER','STAFF','BOTH') NOT NULL,
  `recipient_email` varchar(255) DEFAULT NULL,
  `details` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `reminder_logs`
--

INSERT INTO `reminder_logs` (`id`, `reminder_id`, `booking_id`, `action`, `recipient_type`, `recipient_email`, `details`, `created_at`) VALUES
(1, '39a71393-fb85-4351-9074-d94e51aa9751', 'a60757d5-a9be-41c6-b78c-9ea28ea5b0ed', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-10 20:34:00', '2025-06-07 22:55:35'),
(2, 'e239288c-d8f8-4c43-bdf7-266f62ad3718', 'a60757d5-a9be-41c6-b78c-9ea28ea5b0ed', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-11 20:04:00', '2025-06-07 22:55:35'),
(3, '8a8a3bb9-3c1b-4389-92cc-780940aaafed', 'a60757d5-a9be-41c6-b78c-9ea28ea5b0ed', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-11 20:34:00', '2025-06-07 22:55:35'),
(4, 'f8c27e02-cbaa-4464-8673-9c1e6557db59', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-11 22:00:00', '2025-06-07 22:55:35'),
(5, 'c3128994-d60c-44c4-b9f0-339ea4fb144e', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-12 21:30:00', '2025-06-07 22:55:35'),
(6, '77702070-5b7d-4a91-a911-2ed2ad2a7968', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-12 22:00:00', '2025-06-07 22:55:35'),
(7, '9af22e41-d9b5-4bd1-875d-6ac28bd4d3b6', '2f04c209-766c-4ec0-9184-d01091f8afd2', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-19 20:54:00', '2025-06-07 22:55:35'),
(8, 'be172c5e-c1de-4206-b797-ea1711d2007d', '2f04c209-766c-4ec0-9184-d01091f8afd2', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-20 20:24:00', '2025-06-07 22:55:35'),
(9, '54337cde-774b-438e-af04-a50d2a890e4f', '2f04c209-766c-4ec0-9184-d01091f8afd2', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-20 20:54:00', '2025-06-07 22:55:35'),
(10, '72d1776a-1949-4816-84e8-a4adc72c1acf', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-11 22:00:00', '2025-06-07 23:07:52'),
(11, '95b7dd1c-01f5-4625-8451-ee6b475a490a', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-12 21:30:00', '2025-06-07 23:07:52'),
(12, 'b58c3455-b98c-4d5f-b4ae-894708619a7b', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-12 22:00:00', '2025-06-07 23:07:52'),
(13, 'fc03d17e-de45-45fe-9acd-2a03a8928ce3', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:40:00', '2025-06-07 23:08:24'),
(14, '6322e5fa-1fbe-405f-83ba-88a680967e3f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:10:00', '2025-06-07 23:08:24'),
(15, '0286f791-3fe5-41b7-9f25-9e83bda1ff50', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:36'),
(16, '0c717671-e419-4a3a-b78a-8169e590efa2', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:36'),
(17, '473ae346-3957-4006-91e9-ec888eb3724d', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:38'),
(18, 'cb755348-e413-4485-9384-9f94429c789f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:38'),
(19, 'ae760e92-a006-451c-b944-70012e9cf3e2', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:38'),
(20, '8591126e-3b7d-4efa-938a-0895b9ffac34', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:38'),
(21, '23d74828-293b-4471-85c8-1350ba29641f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:38'),
(22, '3aab5667-92c2-40fd-a3da-74e98d8eecc0', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:38'),
(23, '0b1cea99-38aa-45eb-9012-0e3071d9e71f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(24, '6e53e3ad-af1e-416f-a412-ebe1cc9856b3', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(25, '8c377d80-d23d-42fc-941e-db70edac989b', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(26, 'ddbd63bf-9e70-443e-9604-bb01455abd4b', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(27, '8c5cabd5-8cbe-46bf-aaa5-ecadcf612274', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(28, '31e4dcb9-e027-4e55-a12a-db244973ad63', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(29, 'fae809fb-c0b1-44eb-a5c1-9d38100b8e05', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(30, 'd991ad63-7ed0-474b-8296-2cb7eda7ca8f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(31, '3765bfdd-145d-443c-a2fb-6eebeeb450cc', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(32, '32b74f97-4f30-4238-bd65-51d1324ef0de', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(33, '7c3459a9-c96c-4cd6-8711-ae62ceceac64', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:40'),
(34, '96333d63-179a-4a55-b8df-00d11a3d2b7c', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:40'),
(35, 'f30a21b6-f139-4025-8b13-6eba1283097b', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:40'),
(36, 'aec056db-1068-4cee-8d6e-cc4591853da1', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:40'),
(37, '347b6e85-3a01-4e97-9567-fb215f0727ea', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:45'),
(38, '513e6145-8050-4aac-84ef-f7b4a738c03c', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:45'),
(39, 'ec158994-392e-406b-b0cc-b72c53f995c4', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:45'),
(40, '9a497434-d7fe-42dc-bf08-c5fb48e8e980', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:45'),
(41, 'b707de85-8903-4dcc-b39a-585ec9c5e4fe', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:45'),
(42, 'd8d708e9-ca72-4226-9fe5-1616ca94c6ad', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:45'),
(43, 'be787aa1-4644-4ecf-b343-3ef73d16cf7a', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:49'),
(44, 'e165b7b6-ac22-43d9-a2ed-e2ce38e39de2', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:49'),
(45, 'c3251b5e-23f1-4ecc-b1b7-18f675ee7a35', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:12:01'),
(46, 'b213720b-ad3d-4329-97d5-a1ce862f7346', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:12:01'),
(47, '952894ef-dba4-4cc9-bf57-04b2a089686c', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:12:02'),
(48, '68634687-ff85-45fc-8df9-16711700a00c', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:12:02'),
(49, '04adc599-b650-4f65-a582-8e45b8f2aa8f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:12:05'),
(50, '256e4306-22f5-43ce-8a60-26d6d2a7b722', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:12:05'),
(51, '8a1b6eae-99ff-4174-9584-15182db4d177', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:26:47'),
(52, '775b7935-ea52-4c2c-bf90-4243d941026b', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 10:30:00', '2025-06-07 23:35:13'),
(53, 'e74674cb-0704-44a7-87ed-dd661b523bce', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 11:00:00', '2025-06-07 23:35:13'),
(54, 'e74674cb-0704-44a7-87ed-dd661b523bce', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:56:57'),
(55, 'e74674cb-0704-44a7-87ed-dd661b523bce', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:56:59'),
(56, '4495954d-2a5b-41b3-b49f-f9a82e2ce6d6', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'CREATED', 'BOTH', '', 'Additional high priority reminder scheduled', '2025-06-08 07:57:06'),
(57, '775b7935-ea52-4c2c-bf90-4243d941026b', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:57:06'),
(58, '775b7935-ea52-4c2c-bf90-4243d941026b', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:57:11'),
(59, '4495954d-2a5b-41b3-b49f-f9a82e2ce6d6', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:57:16'),
(60, '7808a2e4-0e51-42d6-a2eb-e1b22b18ab97', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:31:00', '2025-06-08 08:16:20'),
(61, '263ecf68-9de0-493c-8e0c-31ac1073b27e', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:01:00', '2025-06-08 08:16:20'),
(62, '8c4e8e7a-e625-4e9f-b506-602e289c5994', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:31:00', '2025-06-08 08:16:51'),
(63, '99f0ded2-b3a6-4be4-a220-6c9113ff7fd8', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:01:00', '2025-06-08 08:16:51'),
(64, '7808a2e4-0e51-42d6-a2eb-e1b22b18ab97', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'RETRY', 'BOTH', '', 'Retry scheduled for failed reminder', '2025-06-08 08:26:21'),
(65, '7808a2e4-0e51-42d6-a2eb-e1b22b18ab97', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'RETRY', 'BOTH', '', 'Retry scheduled for failed reminder', '2025-06-08 08:26:54'),
(66, '4c4516ee-bf12-47c8-ae2e-66831a07d0c8', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:53:00', '2025-06-08 08:47:10'),
(67, 'a79fb8b1-d37b-469a-98bd-5a73f93b3b87', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:23:00', '2025-06-08 08:47:10'),
(68, '66391444-2cc2-4288-b05d-d77b144bacfd', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:53:00', '2025-06-08 08:48:16'),
(69, 'ff6ea595-9c3d-4c41-86e0-1c1a5a094a3b', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:23:00', '2025-06-08 08:48:16'),
(70, 'ccc0e3f2-eb83-411a-94fd-2daed41c521e', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Additional high priority reminder scheduled', '2025-06-08 08:52:24'),
(71, '4c4516ee-bf12-47c8-ae2e-66831a07d0c8', '95fe59e1-7826-472c-8baa-540901a766bb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 08:52:24'),
(72, 'ccc0e3f2-eb83-411a-94fd-2daed41c521e', '95fe59e1-7826-472c-8baa-540901a766bb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 09:04:45'),
(73, 'a79fb8b1-d37b-469a-98bd-5a73f93b3b87', '95fe59e1-7826-472c-8baa-540901a766bb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 09:18:47'),
(74, 'bbf83d8f-1ec4-415f-b2ef-7b15749f8921', '0f8dabae-940a-45ac-962a-f0a44711506a', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 13:51:00', '2025-06-08 10:50:10'),
(75, '22dd7b86-e693-4e33-9197-89eeced42edb', '0f8dabae-940a-45ac-962a-f0a44711506a', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 14:21:00', '2025-06-08 10:50:10'),
(76, 'ce876528-4637-463f-b8e4-8d99d7a17a5c', '0f8dabae-940a-45ac-962a-f0a44711506a', 'CREATED', 'BOTH', '', 'Additional high priority reminder scheduled', '2025-06-08 10:50:36'),
(77, 'bbf83d8f-1ec4-415f-b2ef-7b15749f8921', '0f8dabae-940a-45ac-962a-f0a44711506a', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 10:50:36'),
(78, 'ce876528-4637-463f-b8e4-8d99d7a17a5c', '0f8dabae-940a-45ac-962a-f0a44711506a', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 11:02:23'),
(79, '22dd7b86-e693-4e33-9197-89eeced42edb', '0f8dabae-940a-45ac-962a-f0a44711506a', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 11:16:09'),
(80, '7285bf64-0534-44c1-9dfa-898554b72efa', 'bdbf49e9-4b87-4bdf-afa1-407fbeed572c', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-09 14:00:00', '2025-06-08 21:06:01'),
(81, '0fa97944-35e3-4973-99a4-9838b17da3f6', 'bdbf49e9-4b87-4bdf-afa1-407fbeed572c', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-10 13:30:00', '2025-06-08 21:06:01'),
(82, 'b6910f2b-08c9-47bf-b669-1707ba7434f0', 'bdbf49e9-4b87-4bdf-afa1-407fbeed572c', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-10 14:00:00', '2025-06-08 21:06:01'),
(83, '7285bf64-0534-44c1-9dfa-898554b72efa', 'bdbf49e9-4b87-4bdf-afa1-407fbeed572c', 'FAILED', 'STAFF', '', 'Failed to send customer email; No staff email available', '2025-06-09 18:33:07'),
(84, '7285bf64-0534-44c1-9dfa-898554b72efa', 'bdbf49e9-4b87-4bdf-afa1-407fbeed572c', 'RETRY', 'BOTH', '', 'Retry scheduled for failed reminder', '2025-06-09 18:33:07'),
(85, '7285bf64-0534-44c1-9dfa-898554b72efa', 'bdbf49e9-4b87-4bdf-afa1-407fbeed572c', 'FAILED', 'STAFF', '', 'Failed to send customer email; No staff email available', '2025-06-09 18:33:10'),
(86, '127ce64f-985f-4b37-bf73-b7b9d60104d5', '3d0521c1-6a08-4b84-bdc0-939ab2c2b6d8', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-10 10:32:00', '2025-06-10 01:51:30'),
(87, '144cd27b-2d6e-4a33-b157-10f912b4e96c', '3d0521c1-6a08-4b84-bdc0-939ab2c2b6d8', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-10 11:02:00', '2025-06-10 01:51:31'),
(88, '56cb84ba-37f2-405e-b325-4ab9c3855251', '3d0521c1-6a08-4b84-bdc0-939ab2c2b6d8', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-10 10:32:00', '2025-06-10 01:56:32'),
(89, 'aa9aef9c-d45d-4864-b70a-ca15e0b0881c', '3d0521c1-6a08-4b84-bdc0-939ab2c2b6d8', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-10 11:02:00', '2025-06-10 01:56:32'),
(90, '76c1c561-5948-4a33-80da-aca3b508d729', '3d0521c1-6a08-4b84-bdc0-939ab2c2b6d8', 'CREATED', 'BOTH', '', 'Additional high priority reminder scheduled', '2025-06-10 07:44:58'),
(91, '127ce64f-985f-4b37-bf73-b7b9d60104d5', '3d0521c1-6a08-4b84-bdc0-939ab2c2b6d8', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-10 07:44:58'),
(92, '76c1c561-5948-4a33-80da-aca3b508d729', '3d0521c1-6a08-4b84-bdc0-939ab2c2b6d8', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-10 07:46:45'),
(93, '144cd27b-2d6e-4a33-b157-10f912b4e96c', '3d0521c1-6a08-4b84-bdc0-939ab2c2b6d8', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-10 07:57:19'),
(94, '9553f453-c280-4ee6-b665-f90855a90ccd', '60b88d4a-33c5-49d7-a13a-6c95fb2a63d5', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-19 14:03:00', '2025-06-12 00:58:30'),
(95, '1a784a25-2a70-4ade-b300-0e90b55fa73d', '60b88d4a-33c5-49d7-a13a-6c95fb2a63d5', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-20 13:33:00', '2025-06-12 00:58:30'),
(96, '361548fa-2b15-4df4-a94f-91b0943f41bf', '60b88d4a-33c5-49d7-a13a-6c95fb2a63d5', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-20 14:03:00', '2025-06-12 00:58:30'),
(97, 'ddba9f6b-f786-49db-b608-57b4c6bd6b5a', 'f239740e-6c55-4eb9-85e2-52fc8fc26d36', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-19 11:00:00', '2025-06-12 00:58:57'),
(98, '11925594-35ab-428f-9994-9c7b22560c5c', 'f239740e-6c55-4eb9-85e2-52fc8fc26d36', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-20 10:30:00', '2025-06-12 00:58:57'),
(99, '95636bcf-df6a-43b5-af01-d83f98cd2981', 'f239740e-6c55-4eb9-85e2-52fc8fc26d36', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-20 11:00:00', '2025-06-12 00:58:57'),
(100, 'da7663bc-c90d-48ef-bbdf-2d1418a282b3', 'a71a3c93-c69b-407f-a995-e5b02e830227', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-14 10:41:00', '2025-06-14 00:12:30'),
(101, '58acf000-5ba4-4c8d-8a6d-804b3e8f8313', 'a71a3c93-c69b-407f-a995-e5b02e830227', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-14 11:11:00', '2025-06-14 00:12:30'),
(102, '2f77e708-735f-4b77-8318-03854cc4e4b9', '60ccb7ef-56bc-4451-8c5b-222f9eda95d6', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-19 10:31:00', '2025-06-18 19:54:14'),
(103, '4bab3da4-eda4-46fa-a086-1cec010959eb', '60ccb7ef-56bc-4451-8c5b-222f9eda95d6', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-19 11:01:00', '2025-06-18 19:54:14'),
(104, 'fcc0c1a4-1965-478e-bb4a-cbd92f30f6ea', '0088304a-bc36-4644-a89a-6493193fc56f', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-19 11:11:00', '2025-06-18 20:20:47'),
(105, '166ec53b-71e3-47f7-9ad2-2fb771e423cd', '0088304a-bc36-4644-a89a-6493193fc56f', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-20 10:41:00', '2025-06-18 20:20:47'),
(106, '5c3b75f6-824b-463f-b80a-639aa7bdf257', '0088304a-bc36-4644-a89a-6493193fc56f', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-20 11:11:00', '2025-06-18 20:20:47'),
(107, 'fcc0c1a4-1965-478e-bb4a-cbd92f30f6ea', '0088304a-bc36-4644-a89a-6493193fc56f', 'SKIPPED', 'BOTH', '', 'Booking cancelled', '2025-06-18 20:26:33'),
(108, '166ec53b-71e3-47f7-9ad2-2fb771e423cd', '0088304a-bc36-4644-a89a-6493193fc56f', 'SKIPPED', 'BOTH', '', 'Booking cancelled', '2025-06-18 20:26:33'),
(109, '5c3b75f6-824b-463f-b80a-639aa7bdf257', '0088304a-bc36-4644-a89a-6493193fc56f', 'SKIPPED', 'BOTH', '', 'Booking cancelled', '2025-06-18 20:26:33'),
(110, 'bc159b4f-7d25-4000-870a-c0c4da8e9dbb', 'caafb291-d1cd-4d09-9c51-fca76ff73dc8', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-19 09:31:00', '2025-06-18 20:35:26'),
(111, '8f846a00-4a6f-41f5-848a-9c35d79f70a7', 'caafb291-d1cd-4d09-9c51-fca76ff73dc8', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-19 10:01:00', '2025-06-18 20:35:26'),
(112, '45d4bae7-a820-46b9-829a-27597eca5159', '66910982-603d-4166-a36e-bb6662b744e4', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-19 13:32:00', '2025-06-18 23:59:47'),
(113, '977a4e38-7aed-4161-b8c6-397ed38928fc', '66910982-603d-4166-a36e-bb6662b744e4', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-20 13:02:00', '2025-06-18 23:59:47'),
(114, 'b3bdadaa-4951-4014-be07-70eda84c2a76', '66910982-603d-4166-a36e-bb6662b744e4', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-20 13:32:00', '2025-06-18 23:59:47'),
(115, '45d4bae7-a820-46b9-829a-27597eca5159', '66910982-603d-4166-a36e-bb6662b744e4', 'SENT', 'STAFF', '', 'Reminder sent successfully', '2025-06-19 15:14:07'),
(116, 'f70c9f72-51c1-4b82-a0dd-38f26c788a7d', '442cabbd-53eb-4a76-91a8-ebeaf4729f1f', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-23 11:11:00', '2025-06-22 22:33:23'),
(117, '712ea7f5-33ba-4c76-9421-36b9b883685c', '442cabbd-53eb-4a76-91a8-ebeaf4729f1f', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-24 10:41:00', '2025-06-22 22:33:23'),
(118, '658c8194-3a2d-4d3a-a8c4-8fa3e24c0103', '442cabbd-53eb-4a76-91a8-ebeaf4729f1f', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-24 11:11:00', '2025-06-22 22:33:23'),
(119, '7268566a-0538-4476-8a78-8ac2970b36e1', '442cabbd-53eb-4a76-91a8-ebeaf4729f1f', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-23 11:11:00', '2025-06-22 22:38:57'),
(120, 'f3de8534-69fd-4b51-babf-c839cf1293e8', '442cabbd-53eb-4a76-91a8-ebeaf4729f1f', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-24 10:41:00', '2025-06-22 22:38:57'),
(121, '87a47471-b679-4641-bda7-60b0fb2a3604', '442cabbd-53eb-4a76-91a8-ebeaf4729f1f', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-24 11:11:00', '2025-06-22 22:38:57'),
(122, 'ce54377f-7136-4ba0-be1a-8f2be4485c68', 'cded7743-a2cb-497d-a53e-bbd31670ca25', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-23 12:12:00', '2025-06-22 22:40:54'),
(123, '1de86d73-0435-4ef6-badb-68fae49e5e2b', 'cded7743-a2cb-497d-a53e-bbd31670ca25', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-24 11:42:00', '2025-06-22 22:40:54'),
(124, '2b6cb990-8699-4e87-9072-bf299e1e6d3c', 'cded7743-a2cb-497d-a53e-bbd31670ca25', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-24 12:12:00', '2025-06-22 22:40:54'),
(125, '6d447261-cb04-42ba-a9ef-cad0d0dd71a6', 'c66c2709-cc98-41bd-8b82-d00cff838694', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-23 13:23:00', '2025-06-22 22:42:55'),
(126, '9995ae98-2130-4ce8-b55e-e1397ca1a1dd', 'c66c2709-cc98-41bd-8b82-d00cff838694', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-24 12:53:00', '2025-06-22 22:42:55'),
(127, 'b0a05f1e-6c08-40fe-82fa-79b5d9496a8e', 'c66c2709-cc98-41bd-8b82-d00cff838694', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-24 13:23:00', '2025-06-22 22:42:55'),
(128, '4622b4b9-49c8-4b3b-bbc1-bc3c0bbddaab', 'c66c2709-cc98-41bd-8b82-d00cff838694', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-23 13:23:00', '2025-06-22 22:44:25'),
(129, '0a6682ae-5dda-46a0-983d-a695a5830b55', 'c66c2709-cc98-41bd-8b82-d00cff838694', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-24 12:53:00', '2025-06-22 22:44:25'),
(130, 'b46eb3da-1977-4450-8a76-10131cad5b81', 'c66c2709-cc98-41bd-8b82-d00cff838694', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-24 13:23:00', '2025-06-22 22:44:25'),
(131, '7921e1b1-532c-497b-8d77-01cf993ef191', '0bfeee21-6925-4a63-82f7-70ffac4df11c', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-23 15:54:00', '2025-06-22 22:45:44'),
(132, 'b3d7023f-2bad-450e-8842-0e259852ce93', '0bfeee21-6925-4a63-82f7-70ffac4df11c', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-24 15:24:00', '2025-06-22 22:45:44'),
(133, '251a3899-393b-4f94-98ad-d122b97dcca7', '0bfeee21-6925-4a63-82f7-70ffac4df11c', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-24 15:54:00', '2025-06-22 22:45:44'),
(134, 'ac96f1fe-52e2-46d2-b90f-bc68d5ac46cb', '5f339d3c-0775-45ed-9145-a28540b44215', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-23 12:23:00', '2025-06-22 22:51:54'),
(135, '856f421a-52de-45ed-bd59-cbe09b225e72', '5f339d3c-0775-45ed-9145-a28540b44215', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-24 11:53:00', '2025-06-22 22:51:54'),
(136, 'cb4ad988-89f7-43f2-aa61-dfc7911269bf', '5f339d3c-0775-45ed-9145-a28540b44215', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-24 12:23:00', '2025-06-22 22:51:54'),
(137, '02a56781-992a-45f4-90d1-c72fcd7e3bb9', '23b0e804-38fe-4cc6-bf7f-361d5016402d', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-24 13:34:00', '2025-06-22 22:52:47'),
(138, 'ad5b6e74-2707-4539-9baa-04eab57e2fe1', '23b0e804-38fe-4cc6-bf7f-361d5016402d', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-25 13:04:00', '2025-06-22 22:52:47'),
(139, '0f8255bd-3363-43a8-b11a-8fd221bc82c7', '23b0e804-38fe-4cc6-bf7f-361d5016402d', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-25 13:34:00', '2025-06-22 22:52:47'),
(140, 'c1a86425-cdda-4d32-94ab-4efe0d55e4af', '23b0e804-38fe-4cc6-bf7f-361d5016402d', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-24 13:34:00', '2025-06-22 22:53:35'),
(141, '0272096a-8b7b-4651-839f-0881bbb1b7bf', '23b0e804-38fe-4cc6-bf7f-361d5016402d', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-25 13:04:00', '2025-06-22 22:53:35'),
(142, 'ad8847dd-c3fc-4ad6-aecb-c1df763dcd48', '23b0e804-38fe-4cc6-bf7f-361d5016402d', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-25 13:34:00', '2025-06-22 22:53:35'),
(143, '5e2f8cb9-b60d-40cf-a4ce-1fb8bfcaeaee', '3587e46a-3808-44c8-b5cd-ae818f6893d1', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-24 10:19:00', '2025-06-22 23:19:34'),
(144, '219c173c-2c14-49d1-aca5-ea46d00189b9', '3587e46a-3808-44c8-b5cd-ae818f6893d1', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-25 09:49:00', '2025-06-22 23:19:34'),
(145, '059ad825-d6bc-4ca3-84d9-804afac4c4e8', '3587e46a-3808-44c8-b5cd-ae818f6893d1', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-25 10:19:00', '2025-06-22 23:19:34'),
(146, '5e2f8cb9-b60d-40cf-a4ce-1fb8bfcaeaee', '3587e46a-3808-44c8-b5cd-ae818f6893d1', 'SENT', 'STAFF', '', 'Reminder sent successfully', '2025-06-24 10:56:46'),
(147, '02a56781-992a-45f4-90d1-c72fcd7e3bb9', '23b0e804-38fe-4cc6-bf7f-361d5016402d', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-24 10:58:22'),
(148, '72d7f051-b869-48ec-9f1d-e6dd4aa34b96', '6fc0483e-cf31-4d41-afb5-182c2ae73a1d', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-26 11:02:00', '2025-06-24 12:05:10'),
(149, '81b84d98-79bf-4e62-8013-9de5707421bc', '6fc0483e-cf31-4d41-afb5-182c2ae73a1d', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-27 10:32:00', '2025-06-24 12:05:10'),
(150, 'b4c25b41-d840-4f09-9491-e69b7a00262c', '6fc0483e-cf31-4d41-afb5-182c2ae73a1d', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-27 11:02:00', '2025-06-24 12:05:10'),
(151, 'cdb7814c-ecc6-4d41-9eaf-8f36bbf2b2f3', '4d21b14a-4ddc-4ba5-852b-18b4acdffdb3', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-07-04 11:32:00', '2025-07-03 15:26:32'),
(152, '2bbd1cb4-33bf-4de1-9e48-38750ca03f13', '4d21b14a-4ddc-4ba5-852b-18b4acdffdb3', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-07-04 12:02:00', '2025-07-03 15:26:32');

-- --------------------------------------------------------

--
-- Table structure for table `reminder_rate_limit`
--

CREATE TABLE `reminder_rate_limit` (
  `booking_id` varchar(36) NOT NULL,
  `reminder_type` varchar(20) NOT NULL,
  `last_sent` timestamp NOT NULL DEFAULT current_timestamp(),
  `send_count` int(11) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reviews`
--

CREATE TABLE `reviews` (
  `id` varchar(36) NOT NULL,
  `customer_id` varchar(36) NOT NULL,
  `service_id` varchar(36) DEFAULT NULL,
  `package_id` varchar(36) DEFAULT NULL,
  `booking_id` varchar(36) DEFAULT NULL,
  `rating` int(11) NOT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `title` varchar(255) NOT NULL,
  `comment` text NOT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `is_featured` tinyint(1) DEFAULT 0,
  `status` enum('pending','verified','rejected') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `reviews`
--

INSERT INTO `reviews` (`id`, `customer_id`, `service_id`, `package_id`, `booking_id`, `rating`, `title`, `comment`, `is_verified`, `is_featured`, `status`, `created_at`, `updated_at`) VALUES
('16aef166-5cbf-42d7-8ee9-07b3e0a40a8b', '6085b727-0ac8-4d95-b10f-c6e766420e0c', NULL, '97a321b1-9598-4831-b4eb-ea1f92a70903', NULL, 5, 'Wonderful Facial Treatment', 'My skin has never looked better! The facial treatment was customized to my skin type and the results are visible immediately. The staff explained each step of the process.', 1, 1, 'verified', '2025-05-09 01:37:53', '2025-06-07 02:46:00'),
('347ddf5d-0cad-45ec-a32d-16c9c2906fb1', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', NULL, 'b7306f8c-e63e-4fa2-a761-2b47a94e9034', NULL, 4, 'Great Hair Color Service', 'The hair colorist did an amazing job with my highlights. The color turned out exactly as I envisioned and the hair feels healthy. Professional and skilled team.', 1, 0, 'verified', '2025-05-07 01:37:53', '2025-06-07 02:45:45'),
('4300760d-4c9e-4fda-a472-58b8463d8b62', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', NULL, '97a321b1-9598-4831-b4eb-ea1f92a70903', NULL, 5, 'Absolutely Amazing Experience!', 'I had the most wonderful facial treatment here. The staff was incredibly professional and the atmosphere was so relaxing. My skin feels amazing and looks radiant. I will definitely be coming back!', 1, 1, 'verified', '2025-05-10 01:37:53', '2025-06-07 02:45:55'),
('51ad4902-b389-44a4-ab16-9b9cc88004bf', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', NULL, 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', NULL, 3, 'Good Service Overall', 'Had a pleasant experience overall. The service was good and the staff was friendly. The only minor issue was the wait time, but the quality made up for it.', 1, 0, 'verified', '2025-04-15 01:37:53', '2025-06-07 01:37:53'),
('61ed6ccc-deed-412c-925d-837a7c54b10b', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', NULL, 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', NULL, 5, 'Excellent Package Deal', 'Booked the spa package and it was worth every penny. Multiple treatments in one session and all were executed perfectly. Great value for money and exceptional service.', 1, 0, 'verified', '2025-04-24 01:37:53', '2025-06-07 02:45:47'),
('68db1b92-cbfb-4119-b891-72f28854b02d', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', NULL, NULL, 5, 'Test', 'dvsd', 1, 0, 'verified', '2025-06-07 02:32:44', '2025-06-07 02:45:32'),
('8d60d527-84c9-4c7d-8360-e723a4b2811b', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', NULL, 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', NULL, 4, 'Relaxing Massage Therapy', 'The massage was incredibly relaxing and therapeutic. The therapist was skilled and professional. The ambiance of the spa really helped me unwind after a stressful week.', 1, 1, 'verified', '2025-04-07 01:37:53', '2025-06-07 02:44:50'),
('ea1c8ae2-0fac-45f8-8cac-99790aa3f95b', '350e5a4d-da0d-4c42-ab08-302eb513c732', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', NULL, NULL, 4, 'Great Manicure Service', 'Love the attention to detail in their manicure service. The nail technician was very careful and the results lasted for weeks. Clean environment and friendly staff.', 1, 0, 'verified', '2025-04-28 01:37:53', '2025-06-07 01:37:53');

-- --------------------------------------------------------

--
-- Table structure for table `rewards`
--

CREATE TABLE `rewards` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `points_required` int(11) NOT NULL,
  `value` int(11) NOT NULL,
  `type` enum('discount','service','product','bonus') DEFAULT 'discount',
  `is_active` tinyint(1) DEFAULT 1,
  `max_usage` int(11) DEFAULT NULL,
  `usage_count` int(11) DEFAULT 0,
  `image` varchar(255) DEFAULT NULL,
  `terms_conditions` text DEFAULT NULL,
  `valid_from` date DEFAULT NULL,
  `valid_to` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `rewards`
--

INSERT INTO `rewards` (`id`, `name`, `description`, `points_required`, `value`, `type`, `is_active`, `max_usage`, `usage_count`, `image`, `terms_conditions`, `valid_from`, `valid_to`, `created_at`, `updated_at`) VALUES
('reward-1', 'TSH 5,000 Service Discount', 'Get TSH 5,000 off any service booking', 500, 5000, 'discount', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-2', 'TSH 10,000 Service Discount', 'Get TSH 10,000 off any service booking', 1000, 10000, 'discount', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-3', 'Free Basic Manicure', 'Complimentary basic manicure service', 1500, 25000, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-5', 'Free Hair Wash & Blow Dry', 'Complimentary hair wash and blow dry service', 800, 15000, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-6', 'VIP Treatment Upgrade', 'Upgrade any service to VIP treatment', 1200, 30000, 'bonus', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-7', 'Birthday Special Package', 'Special birthday package with multiple services', 3000, 50000, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44');

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `price` int(11) NOT NULL,
  `duration` int(11) NOT NULL COMMENT 'Duration in minutes',
  `category` varchar(100) NOT NULL,
  `category_id` varchar(36) DEFAULT NULL,
  `subcategory_id` varchar(36) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `name`, `description`, `price`, `duration`, `category`, `category_id`, `subcategory_id`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
('0203e223-025e-4289-a962-47ce833d09af', 'Eyebrow Threading', 'Precision eyebrow threading', 15000, 30, 'Eyebrows', NULL, '6709af32-0eb9-4443-83fd-fcbb48609f29', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('03987f1b-5463-4205-ab16-54e211947442', 'RERE', '', 45555, 56, 'Beauty', '037c4a11-9a00-4c86-be46-2f15237f20b3', '509c050b-18a1-4f24-ac58-5648b176427c', NULL, 1, '2025-06-18 22:06:24', '2025-06-18 22:27:11'),
('07009d50-1416-4ea5-a9f2-1c8c8926d3eb', 'Spa Pedicure', 'Luxurious spa pedicure', 45000, 90, 'Nails', NULL, '09520c96-31e6-4de2-80e4-d7ecbe60f096', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('0dcf98ad-f2c9-4d39-868e-b8fcc52a5733', 'Anti-Aging Treatment', 'Comprehensive anti-aging treatment', 95000, 90, 'Beauty', NULL, '6339d10c-d55d-4d38-af45-6a4efe1e5f6f', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('141187d4-2c1c-4372-9ad3-979ab8737feb', 'Full Hair Color', 'Complete hair coloring service', 80000, 120, 'Hair', NULL, '4663636a-c3a4-45f5-a19b-bffcccc24b17', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('188b333e-45f2-4067-8fb7-d1f08f8edd09', 'Bob Cut', 'Stylish bob haircut', 28000, 50, 'Hair', NULL, '090e553e-71ff-4300-a4a4-41836091c0d3', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('19623395-c402-474f-84c6-78c600b18bd6', 'Microdermabrasion', 'Professional microdermabrasion treatment', 85000, 60, 'Beauty', NULL, '02c0a7f3-ed62-4157-8b5c-3f21c63a8242', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('2a8a6186-ef59-46c9-9b70-0b90c8949365', 'Classic Pedicure', 'Traditional pedicure service', 30000, 60, 'Nails', NULL, '09520c96-31e6-4de2-80e4-d7ecbe60f096', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'Pedicure new', 'Relaxing pedicure service', 450000, 60, 'Nails', 'e85a3548-eb2f-45d5-b3d9-113d39d7865b', NULL, 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400', 1, '2025-06-01 00:06:02', '2025-06-18 21:10:44'),
('386ff31e-0274-443f-8807-53202fa0b7fe', 'Facial Treatment', 'Deep cleansing facial treatment', 65000, 75, 'Facial', '8768082b-b036-4d2e-9e51-48385da7e4c2', NULL, 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400', 1, '2025-06-01 00:06:02', '2025-06-18 21:10:44'),
('389f948c-141b-4fa3-9ec1-c1887d9ed745', 'Craq Venture2', 'ca', 343000, 22, 'Beauty', '037c4a11-9a00-4c86-be46-2f15237f20b3', '6339d10c-d55d-4d38-af45-6a4efe1e5f6f', 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305116.jpg?t=st=1748796051~exp=1748799651~hmac=a3c787643f154275d3422dfa773b6f63174ba9741dac774fd9e45b5cf466084a', 1, '2025-06-01 14:46:57', '2025-06-18 23:53:22'),
('40612bf7-1372-4165-aff2-ff895f492f37', 'Updo', 'Elegant updo hairstyle', 50000, 75, 'Hair', NULL, '854d6195-069e-4501-8e58-89368b1f57dd', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('51e0abf5-2f4e-414a-be10-2c28e7fb59e9', 'Advanced Nail Art', 'Complex nail art designs', 25000, 60, 'Nails', NULL, 'c94b5aa6-b4ea-465c-ba8a-55bb2d1c608e', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('52414255-8a36-4924-889d-fa97af8cc8d0', 'Laser Hair Removal', 'Advanced laser hair removal', 200000, 90, 'Hair Removal', NULL, '7c6f81e1-48a2-43ff-ac87-d212493f9b79', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('57dba613-8b94-486f-ab0c-7bc66f067312', 'Event Makeup', 'Special event makeup', 80000, 90, 'Makeup', NULL, '5d2a8119-011a-4bc3-8de4-c460eec485fa', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('5d73a322-22a4-447e-87d1-6bb9458d9744', 'Classic Manicure', 'Traditional manicure service', 25000, 45, 'Nails', NULL, '4ef8fe62-7e87-4967-be38-32459279cf6a', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('5ffce5a5-f93e-4fdd-bb66-7c54376f0fef', 'Hair Highlights', 'Professional highlighting service', 950000, 150, 'Hair', '87e28781-979b-4259-94a0-cd2deeac8c0d', NULL, 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400', 1, '2025-06-01 00:06:02', '2025-06-18 21:10:44'),
('6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', 'Gel Manicure', 'Long-lasting gel manicure', 550000, 60, 'Nails', 'e85a3548-eb2f-45d5-b3d9-113d39d7865b', NULL, 'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400', 1, '2025-06-01 00:06:02', '2025-06-18 21:10:44'),
('636a838f-671c-4260-8def-93951b934e4a', 'Microblading', 'Semi-permanent eyebrow microblading', 150000, 180, 'Eyebrows', NULL, '11c45014-5c1f-4aaa-8d55-740ff602c8ae', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('6d9748db-5d66-4109-9e82-d10b7b647cf7', 'Aromatherapy Massage', 'Relaxing aromatherapy massage', 80000, 75, 'Massage', NULL, 'f15841ac-49ae-4beb-ac96-f8fcdb96531e', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('71e9ed3b-40f8-4cc4-9c39-5ad68ee31eb4', 'Everyday Makeup', 'Natural everyday makeup', 50000, 60, 'Makeup', NULL, '78451e11-a90e-4a1f-8fe6-9637b57cae8c', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('74465952-b6bd-4e66-ab18-94f438ea05ac', 'Deep Cleansing Facial', 'Thorough deep cleansing facial', 60000, 75, 'Beauty', NULL, '509c050b-18a1-4f24-ac58-5648b176427c', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('7a983989-b7f7-451d-bdfe-0d10bbce819a', 'Lash Lift', 'Natural lash lift and curl', 60000, 75, 'Lashes', NULL, '9457ff12-30af-4201-b6ac-dc69c01778af', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('7b0008d1-a0c2-40d3-a015-a29d413bd665', 'Sports Massage', 'Athletic sports massage', 80000, 60, 'Massage', NULL, '108e02eb-4bee-4cd2-9ea1-137d329f77fb', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', 'Deep Tissue Massage', 'Therapeutic deep tissue massage', 95000, 75, 'Massage', '69b5a253-aa59-4f3c-8206-0889880e4544', NULL, 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400', 1, '2025-06-01 00:06:02', '2025-06-27 00:08:11'),
('7f42c734-47e2-4e32-8608-eb5ea2fecbfa', 'Test Service', '', 50000, 60, 'Beauty', '037c4a11-9a00-4c86-be46-2f15237f20b3', '02c0a7f3-ed62-4157-8b5c-3f21c63a8242', 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400', 1, '2025-06-08 09:39:23', '2025-06-18 22:18:40'),
('871b42f2-62fb-4152-baee-662a08ceefa3', 'Eyebrow Tinting', 'Eyebrow tinting service', 20000, 35, 'Eyebrows', NULL, '6b1eb8a5-331c-4ea8-954a-976b53ca1a66', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('8a5c5254-c5c6-48fa-b813-b85e8a5bdb13', 'Blowout', 'Professional blowout styling', 35000, 45, 'Hair', NULL, '854d6195-069e-4501-8e58-89368b1f57dd', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('8dbf4103-ff59-4836-89b1-629208dc686a', 'Balayage', 'Hand-painted balayage technique', 95000, 150, 'Hair', NULL, '4663636a-c3a4-45f5-a19b-bffcccc24b17', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('941d1e9e-0ed7-4d24-8c11-d0bb689f4a07', 'Root Touch-up', 'Root color touch-up service', 45000, 60, 'Hair', NULL, '4663636a-c3a4-45f5-a19b-bffcccc24b17', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('958b58c2-c476-463b-b7f6-12b82fda1930', 'Makeup Application', 'Professional makeup service', 75000, 60, 'Beauty', '037c4a11-9a00-4c86-be46-2f15237f20b3', '509c050b-18a1-4f24-ac58-5648b176427c', 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400', 1, '2025-06-01 00:06:02', '2025-06-27 00:08:11'),
('9a169567-0b73-45a4-bc1f-5aa9677c6f73', 'Swedish Massage', 'Relaxing full body massage', 80000, 60, 'Massage', '69b5a253-aa59-4f3c-8206-0889880e4544', NULL, 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400', 1, '2025-06-01 00:06:02', '2025-06-27 00:08:11'),
('9ac6e200-c2c2-4da4-96c3-f9cb244bc73d', 'Chemical Peel', 'Professional chemical peel', 90000, 45, 'Beauty', NULL, '02c0a7f3-ed62-4157-8b5c-3f21c63a8242', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('9b1d0d89-727b-46fd-93bd-0f3757ec1c57', 'French Manicure', 'Elegant French manicure', 30000, 50, 'Nails', NULL, '4ef8fe62-7e87-4967-be38-32459279cf6a', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('a2db72b8-71e6-4b25-ba24-c0c21af460fd', 'Classic Lash Extensions', 'Classic individual lash extensions', 80000, 120, 'Lashes', NULL, '9457ff12-30af-4201-b6ac-dc69c01778af', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('a73c5c9b-ddf8-4f17-ba0f-1250168ea615', 'Classic Haircut', 'Traditional haircut with styling', 25000, 45, 'Hair', NULL, '090e553e-71ff-4300-a4a4-41836091c0d3', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('a8230836-13b0-41f8-b098-7ce28d01b477', 'Firming Treatment', 'Skin firming and tightening', 85000, 75, 'Beauty', NULL, '6339d10c-d55d-4d38-af45-6a4efe1e5f6f', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('abb14ff2-c314-48f8-b721-5488faf39735', 'Deep Tissue Treatment', 'Intensive deep tissue massage', 85000, 75, 'Massage', NULL, '108e02eb-4bee-4cd2-9ea1-137d329f77fb', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('ae17be15-0710-482f-879c-941181d05506', 'Leg Waxing', 'Full leg waxing service', 45000, 60, 'Hair Removal', NULL, '1d5f2ad6-b7c8-42c2-9844-188a1a505ee3', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('af7575b4-d503-4a1f-873c-7b17d05efa6e', 'Spa Manicure', 'Luxurious spa manicure', 40000, 75, 'Nails', NULL, '4ef8fe62-7e87-4967-be38-32459279cf6a', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('b6d3202a-c7dd-4580-a974-1c153a2dc519', 'Cellulite Treatment', 'Specialized cellulite reduction', 120000, 75, 'Body Treatments', NULL, '94b9b4ef-dc98-4553-8b91-53712098aa84', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('bb5e8c6f-97ff-46e3-a4cf-e3b52050b83b', 'Acne Treatment Facial', 'Specialized acne treatment', 70000, 90, 'Beauty', NULL, '509c050b-18a1-4f24-ac58-5648b176427c', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('bde7fc78-ba62-4f92-9e2a-81214bb5a247', 'Hair Coloring', 'Full hair coloring service', 85000, 120, 'Hair', '87e28781-979b-4259-94a0-cd2deeac8c0d', NULL, 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400', 1, '2025-06-01 00:06:02', '2025-06-27 00:08:11'),
('c001781b-954d-4ca6-a48a-66e20b6b1ed7', 'Hair Cut & Style', 'Professional haircut with styling', 45000, 60, 'Hair', '87e28781-979b-4259-94a0-cd2deeac8c0d', NULL, 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400', 1, '2025-06-01 00:06:02', '2025-06-27 00:08:11'),
('c7de44eb-0de3-46fd-8402-f4aa41b1e915', 'Bridal Makeup', 'Complete bridal makeup service', 120000, 120, 'Makeup', NULL, '2cc6dafa-46e2-4d69-b7c7-ca95d690f5ca', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('cca32fd7-b4ce-49a1-b4be-c45751a61c48', 'Volume Lash Extensions', 'Volume lash extensions', 120000, 150, 'Lashes', NULL, 'f2f9bdc2-e5f7-4cfe-ab24-f38236b316d0', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', 'Eyebrow Shaping', 'Professional eyebrow shaping', 2500, 30, 'Beauty', '037c4a11-9a00-4c86-be46-2f15237f20b3', '509c050b-18a1-4f24-ac58-5648b176427c', 'https://images.unsplash.com/photo-1588681664899-f142ff2dc9b1?w=400', 1, '2025-06-01 00:06:02', '2025-06-18 22:28:58'),
('d2c27a51-2ffa-48e3-9a90-25afa3af6ec0', 'Hydrating Facial', 'Moisturizing hydrating facial', 55000, 60, 'Beauty', NULL, '509c050b-18a1-4f24-ac58-5648b176427c', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('d4d35cfc-2eb6-474c-9b67-6775aadad633', 'Manicure', 'Classic manicure service', 35000, 45, 'Nails', 'e85a3548-eb2f-45d5-b3d9-113d39d7865b', NULL, 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400', 1, '2025-06-01 00:06:02', '2025-06-27 00:08:11'),
('d82223c0-04d9-40fe-a523-691549cb201b', 'Detox Body Wrap', 'Detoxifying body wrap treatment', 100000, 90, 'Body Treatments', NULL, 'd5a6ade2-dda5-4d78-948f-70e4397330e4', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('d9f2135b-ee0b-4dca-979d-ba75fd4ebb61', 'Layered Cut', 'Modern layered haircut', 30000, 60, 'Hair', NULL, '090e553e-71ff-4300-a4a4-41836091c0d3', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('dc20dff0-0f57-4389-953f-69e348b40a74', 'Anti-Aging Facial', 'Advanced anti-aging facial', 95000, 90, 'Facial', '8768082b-b036-4d2e-9e51-48385da7e4c2', NULL, 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400', 1, '2025-06-01 00:06:02', '2025-06-27 00:08:11'),
('e0fc73a8-dfc8-4bb6-8b34-9b9a5f17fcca', 'Upper Lip Threading', 'Upper lip threading service', 12000, 15, 'Hair Removal', NULL, '7d19d10d-b6a2-45f0-9fc9-31e05d00d13e', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('e65984a9-97cd-4d37-bba9-f21ef7cb1ad8', 'Eyebrow Waxing', 'Professional eyebrow waxing', 18000, 25, 'Eyebrows', NULL, '6709af32-0eb9-4443-83fd-fcbb48609f29', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('eb9b887b-8193-4340-91ce-0a81804cd225', 'Brazilian Wax', 'Brazilian waxing service', 60000, 45, 'Hair Removal', NULL, '1d5f2ad6-b7c8-42c2-9844-188a1a505ee3', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('ec523a94-1dbc-407b-b55f-fdba86151107', 'Basic Nail Art', 'Simple nail art designs', 15000, 30, 'Nails', NULL, 'c94b5aa6-b4ea-465c-ba8a-55bb2d1c608e', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39'),
('fc5948d1-bb19-4e9f-ae33-4008dd707de7', 'Hot Stone Massage', 'Soothing hot stone massage', 90000, 90, 'Massage', NULL, 'e73e9b3d-7696-4c4f-a1b0-8dc7e81ef6e7', NULL, 1, '2025-06-30 22:42:39', '2025-06-30 22:42:39');

-- --------------------------------------------------------

--
-- Table structure for table `service_categories`
--

CREATE TABLE `service_categories` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `service_categories`
--

INSERT INTO `service_categories` (`id`, `name`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
('037c4a11-9a00-4c86-be46-2f15237f20b3', 'Beauty', 'Comprehensive beauty treatments and cosmetic services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('4a24a5b0-9682-4940-be09-e17034f6ddd2', 'Hair Removal', 'Professional hair removal services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('5335ca5a-8253-498c-9ab8-94eae3321c9e', 'Makeup', 'Professional makeup application and styling services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('69910667-eb76-46b6-8d51-d093fae52683', 'Eyebrows', 'Professional eyebrow shaping and styling services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('69b5a253-aa59-4f3c-8206-0889880e4544', 'Massage', 'Relaxing therapeutic massage and wellness services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('7dba5069-0c5c-451b-b081-61474a750e85', 'Lashes', 'Eyelash extensions and enhancement services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('8768082b-b036-4d2e-9e51-48385da7e4c2', 'Facial', 'Rejuvenating facial treatments and skincare services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('87e28781-979b-4259-94a0-cd2deeac8c0d', 'Hair', 'Professional hair cutting, styling, and treatment services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('bd15df65-bc22-4def-a42c-45367426bf75', 'Body Treatments', 'Full body treatments and wellness services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('e85a3548-eb2f-45d5-b3d9-113d39d7865b', 'Nails', 'Complete nail care including manicure and pedicure services', 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44');

-- --------------------------------------------------------

--
-- Table structure for table `service_categories_backup`
--

CREATE TABLE `service_categories_backup` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` varchar(36) DEFAULT NULL,
  `level` int(11) DEFAULT 0 COMMENT '0 for main categories, 1+ for subcategories',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Order within the same parent',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `service_categories_backup`
--

INSERT INTO `service_categories_backup` (`id`, `name`, `description`, `parent_id`, `level`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
('037c4a11-9a00-4c86-be46-2f15237f20b3', 'Beauty', 'Comprehensive beauty treatments and cosmetic services', NULL, 0, 1, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('4a24a5b0-9682-4940-be09-e17034f6ddd2', 'Hair Removal', 'Professional hair removal services', NULL, 0, 6, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('5335ca5a-8253-498c-9ab8-94eae3321c9e', 'Makeup', 'Professional makeup application and styling services', NULL, 0, 8, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('69910667-eb76-46b6-8d51-d093fae52683', 'Eyebrows', 'Professional eyebrow shaping and styling services', NULL, 0, 3, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('69b5a253-aa59-4f3c-8206-0889880e4544', 'Massage', 'Relaxing therapeutic massage and wellness services', NULL, 0, 9, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('7dba5069-0c5c-451b-b081-61474a750e85', 'Lashes', 'Eyelash extensions and enhancement services', NULL, 0, 7, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('8768082b-b036-4d2e-9e51-48385da7e4c2', 'Facial', 'Rejuvenating facial treatments and skincare services', NULL, 0, 4, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('87e28781-979b-4259-94a0-cd2deeac8c0d', 'Hair', 'Professional hair cutting, styling, and treatment services', NULL, 0, 5, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('bd15df65-bc22-4def-a42c-45367426bf75', 'Body Treatments', 'Full body treatments and wellness services', NULL, 0, 2, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44'),
('e85a3548-eb2f-45d5-b3d9-113d39d7865b', 'Nails', 'Complete nail care including manicure and pedicure services', NULL, 0, 10, 1, '2025-06-03 08:34:35', '2025-06-18 21:10:44');

-- --------------------------------------------------------

--
-- Table structure for table `service_subcategories`
--

CREATE TABLE `service_subcategories` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `category_id` varchar(36) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `service_subcategories`
--

INSERT INTO `service_subcategories` (`id`, `name`, `description`, `category_id`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
('02c0a7f3-ed62-4157-8b5c-3f21c63a8242', 'Skin Care', 'Advanced skin care treatments', '037c4a11-9a00-4c86-be46-2f15237f20b3', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('090e553e-71ff-4300-a4a4-41836091c0d3', 'Hair Cutting', 'Professional hair cutting services', '87e28781-979b-4259-94a0-cd2deeac8c0d', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('09520c96-31e6-4de2-80e4-d7ecbe60f096', 'Pedicure', 'Professional pedicure services', 'e85a3548-eb2f-45d5-b3d9-113d39d7865b', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('108e02eb-4bee-4cd2-9ea1-137d329f77fb', 'Therapeutic Massage', 'Therapeutic and deep tissue massage', '69b5a253-aa59-4f3c-8206-0889880e4544', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('11c45014-5c1f-4aaa-8d55-740ff602c8ae', 'Microblading', 'Semi-permanent eyebrow microblading', '69910667-eb76-46b6-8d51-d093fae52683', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('1d5f2ad6-b7c8-42c2-9844-188a1a505ee3', 'Waxing', 'Professional waxing services', '4a24a5b0-9682-4940-be09-e17034f6ddd2', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('248f99fa-a327-42e6-9450-15cf6a337f7c', 'Hair Extensions', 'Hair extension and weaving services', '87e28781-979b-4259-94a0-cd2deeac8c0d', 4, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('270b5382-ddd3-4619-b8d1-641c85081f58', 'Anti-Aging', 'Anti-aging and rejuvenation treatments', '8768082b-b036-4d2e-9e51-48385da7e4c2', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('2cc6dafa-46e2-4d69-b7c7-ca95d690f5ca', 'Bridal Makeup', 'Professional bridal makeup services', '5335ca5a-8253-498c-9ab8-94eae3321c9e', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('2fa9970c-e988-46fe-80a5-0f3977fbc749', 'Gel Nails', 'Gel nail applications and maintenance', 'e85a3548-eb2f-45d5-b3d9-113d39d7865b', 4, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('346029ab-8d89-4609-a15c-8b1e586e96da', 'Facial Treatments', 'Professional facial and skin care treatments', '8768082b-b036-4d2e-9e51-48385da7e4c2', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('3d533289-e1ab-4351-9a4d-ed50dd18d0dd', 'Aromatherapy', 'Aromatherapy massage treatments', '69b5a253-aa59-4f3c-8206-0889880e4544', 4, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('4663636a-c3a4-45f5-a19b-bffcccc24b17', 'Hair Coloring', 'Hair coloring and highlighting services', '87e28781-979b-4259-94a0-cd2deeac8c0d', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('4ef8fe62-7e87-4967-be38-32459279cf6a', 'Manicure', 'Professional manicure services', 'e85a3548-eb2f-45d5-b3d9-113d39d7865b', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('509c050b-18a1-4f24-ac58-5648b176427c', 'Facial Treatments', 'Professional facial and skin care treatments', '037c4a11-9a00-4c86-be46-2f15237f20b3', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('5d2a8119-011a-4bc3-8de4-c460eec485fa', 'Event Makeup', 'Special event makeup services', '5335ca5a-8253-498c-9ab8-94eae3321c9e', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('6339d10c-d55d-4d38-af45-6a4efe1e5f6f', 'Anti-Aging', 'Anti-aging and rejuvenation treatments', '037c4a11-9a00-4c86-be46-2f15237f20b3', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('6709af32-0eb9-4443-83fd-fcbb48609f29', 'Eyebrow Shaping', 'Professional eyebrow shaping and threading', '69910667-eb76-46b6-8d51-d093fae52683', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('6b1eb8a5-331c-4ea8-954a-976b53ca1a66', 'Eyebrow Tinting', 'Eyebrow tinting and coloring services', '69910667-eb76-46b6-8d51-d093fae52683', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('78451e11-a90e-4a1f-8fe6-9637b57cae8c', 'Everyday Makeup', 'Daily makeup application services', '5335ca5a-8253-498c-9ab8-94eae3321c9e', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('79c0c150-d92b-4074-8331-b814579708d2', 'Skin Care', 'Advanced skin care treatments', '8768082b-b036-4d2e-9e51-48385da7e4c2', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('7c6f81e1-48a2-43ff-ac87-d212493f9b79', 'Laser Hair Removal', 'Advanced laser hair removal treatments', '4a24a5b0-9682-4940-be09-e17034f6ddd2', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('7d19d10d-b6a2-45f0-9fc9-31e05d00d13e', 'Threading', 'Precision threading services', '4a24a5b0-9682-4940-be09-e17034f6ddd2', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('854d6195-069e-4501-8e58-89368b1f57dd', 'Hair Styling', 'Hair styling and treatment services', '87e28781-979b-4259-94a0-cd2deeac8c0d', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('9457ff12-30af-4201-b6ac-dc69c01778af', 'Basic Services', 'Basic lashes services', '7dba5069-0c5c-451b-b081-61474a750e85', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('94b9b4ef-dc98-4553-8b91-53712098aa84', 'Cellulite Treatment', 'Specialized cellulite reduction treatments', 'bd15df65-bc22-4def-a42c-45367426bf75', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('c94b5aa6-b4ea-465c-ba8a-55bb2d1c608e', 'Nail Art', 'Creative nail art and designs', 'e85a3548-eb2f-45d5-b3d9-113d39d7865b', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('d5a6ade2-dda5-4d78-948f-70e4397330e4', 'Body Wraps', 'Detoxifying and moisturizing body wraps', 'bd15df65-bc22-4def-a42c-45367426bf75', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('e73e9b3d-7696-4c4f-a1b0-8dc7e81ef6e7', 'Hot Stone Massage', 'Hot stone massage therapy', '69b5a253-aa59-4f3c-8206-0889880e4544', 3, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('f15841ac-49ae-4beb-ac96-f8fcdb96531e', 'Relaxation Massage', 'Relaxing massage treatments', '69b5a253-aa59-4f3c-8206-0889880e4544', 1, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30'),
('f2f9bdc2-e5f7-4cfe-ab24-f38236b316d0', 'Premium Services', 'Premium lashes services', '7dba5069-0c5c-451b-b081-61474a750e85', 2, 1, '2025-06-18 21:44:30', '2025-06-18 21:44:30');

-- --------------------------------------------------------

--
-- Table structure for table `service_variations`
--

CREATE TABLE `service_variations` (
  `id` varchar(36) NOT NULL,
  `service_id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `duration` int(11) NOT NULL COMMENT 'Duration in minutes',
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `service_variations`
--

INSERT INTO `service_variations` (`id`, `service_id`, `name`, `description`, `price`, `duration`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
('07a614c8-c537-419b-a8fa-3e876c2f59e2', '6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', 'Basic Manicure', 'Nail shaping and polish application', 15000.00, 30, 0, 1, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('14469bcf-cc79-4925-bde9-a9259e29c5a7', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', '90 Minutes', 'Extended massage with deep tissue work', 75000.00, 90, 0, 2, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('2bcd8fa7-beba-4e9f-844f-66c09bdc5ea4', '9a169567-0b73-45a4-bc1f-5aa9677c6f73', '90 Minutes', 'Extended massage with deep tissue work', 75000.00, 90, 1, 2, '2025-06-18 19:18:14', '2025-06-22 22:56:31'),
('340cdce1-6a5c-4039-aa7f-0d1c7e827f10', '6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', 'Luxury Manicure', 'Premium manicure with hand treatment', 35000.00, 60, 0, 3, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('36366332-fb9f-4030-9e69-ea5519827750', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'Basic Pedicure', 'Foot care and nail polish', 20000.00, 55, 1, 1, '2025-06-18 19:18:14', '2025-06-18 19:57:16'),
('3894bb29-b3aa-49c7-9957-662900188f43', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', '60 Minutes', 'Standard relaxing massage session', 50000.00, 60, 0, 1, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('3a6daf0c-10ea-4751-b0c1-2286e3f1a8a4', '386ff31e-0274-443f-8807-53202fa0b7fe', 'Anti-Aging Treatment', 'Advanced anti-aging facial with serums', 65000.00, 75, 0, 3, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('47a5653b-6df9-49c7-8f44-b2fade579f11', '386ff31e-0274-443f-8807-53202fa0b7fe', 'Basic Facial', 'Cleansing and moisturizing facial', 30000.00, 45, 0, 1, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('49b8d746-5f38-4d58-874e-2a1ce2ba849d', 'c001781b-954d-4ca6-a48a-66e20b6b1ed7', 'Cut, Wash &amp; Style', 'Complete hair service with wash', 55000.00, 90, 0, 3, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('59c7317a-0fc5-4118-a51c-f188874e4b5f', 'dc20dff0-0f57-4389-953f-69e348b40a74', 'Basic Facial', 'Cleansing and moisturizing facial', 30000.00, 45, 0, 1, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('6db4d025-84cd-4765-8a8c-2d2a5d6dd44b', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'Spa Pedicure', 'Relaxing pedicure with foot massage', 30000.00, 60, 1, 2, '2025-06-18 19:18:14', '2025-06-18 19:21:27'),
('71e4f272-5894-4a63-9a71-4a3faeb4f34a', '9a169567-0b73-45a4-bc1f-5aa9677c6f73', '60 Minutes', 'Standard relaxing massage session', 50000.00, 60, 1, 1, '2025-06-18 19:18:14', '2025-06-22 22:56:27'),
('7d36407c-0c92-493b-b6e1-e1b380833fc7', '386ff31e-0274-443f-8807-53202fa0b7fe', 'Deep Cleansing', 'Deep pore cleansing with extractions', 45000.00, 60, 0, 2, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('85ba4c41-c595-4b6a-b5bb-4b7eddc91e85', '6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', 'Gel Manicure', 'Long-lasting gel polish manicure', 25000.00, 45, 0, 2, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('8b3d3f26-1ede-4fc8-bc1a-c2f67851882f', '0203e223-025e-4289-a962-47ce833d09af', '1232', '', 232312.00, 23, 1, 2, '2025-07-04 21:40:16', '2025-07-04 21:40:16'),
('a19fe1fa-c4e1-4894-8d45-88d7dc24408f', 'dc20dff0-0f57-4389-953f-69e348b40a74', 'Anti-Aging Treatment', 'Advanced anti-aging facial with serums', 65000.00, 75, 0, 3, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('a65b2997-623f-4a80-ae1a-03e70af466c1', 'd4d35cfc-2eb6-474c-9b67-6775aadad633', 'Gel Manicure', 'Long-lasting gel polish manicure', 25000.00, 45, 0, 2, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('a94b730a-2e03-4a81-a66e-c119f50027f6', '9a169567-0b73-45a4-bc1f-5aa9677c6f73', '70 Minutes', 'Full body therapeutic massage', 100000.00, 70, 1, 3, '2025-06-18 19:18:14', '2025-06-22 23:00:18'),
('b8248e45-1d01-4078-8948-0622db91dd31', 'd4d35cfc-2eb6-474c-9b67-6775aadad633', 'Luxury Manicure', 'Premium manicure with hand treatment', 35000.00, 60, 0, 3, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('db45b110-789a-46dd-b49c-dad420eac855', '0203e223-025e-4289-a962-47ce833d09af', '1', '', 343243.00, 12, 1, 1, '2025-07-04 21:40:06', '2025-07-04 21:40:06'),
('e1261325-caa4-4134-bad5-9785670fde24', 'dc20dff0-0f57-4389-953f-69e348b40a74', 'Deep Cleansing', 'Deep pore cleansing with extractions', 45000.00, 60, 0, 2, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('e1378579-3843-4132-ac73-5898f8d31d04', 'c001781b-954d-4ca6-a48a-66e20b6b1ed7', 'Basic Cut', 'Simple hair cutting and styling', 25000.00, 30, 0, 1, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('ed865eb0-9a79-4b36-bbdb-ad9d8c65a3b9', 'd4d35cfc-2eb6-474c-9b67-6775aadad633', 'Basic Manicure', 'Nail shaping and polish application', 15000.00, 30, 0, 1, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('f9700df5-2650-4592-9956-ae5a28c856ee', 'c001781b-954d-4ca6-a48a-66e20b6b1ed7', 'Cut &amp; Style', 'Hair cut with professional styling', 40000.00, 60, 0, 2, '2025-06-18 19:18:14', '2025-06-18 19:18:14'),
('fed898e5-f089-4ad0-aaf6-2bf0b5a60956', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', '120 Minutes', 'Full body therapeutic massage', 100000.00, 120, 0, 3, '2025-06-18 19:18:14', '2025-06-18 19:18:14');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(36) NOT NULL,
  `session_token` varchar(255) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `expires` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `requires_2fa` tinyint(1) DEFAULT 0,
  `is_2fa_verified` tinyint(1) DEFAULT 0,
  `pending_2fa_admin_id` varchar(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` varchar(36) NOT NULL,
  `category` varchar(100) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` longtext NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `category`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
('', 'business', 'timezone', 'TSH', '2025-06-04 01:56:29', '2025-06-27 00:27:20'),
('009b951d-fc8e-4bb6-a7ac-31a168420107', 'business', 'address', 'Upanga, Dar Es Salaam, Tanzania', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('1a50d335-dd0a-474b-bb01-4f2a02401720', 'business', 'email', '<EMAIL>', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('28d52ba6-e191-44f0-bf19-61a226700947', 'business', 'phone', '(255) 745 456-789', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('34789dcc-40e7-11f0-a274-84a93e04fdad', 'business', 'currency_code', 'TZS', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34789ee0-40e7-11f0-a274-84a93e04fdad', 'business', 'currency_symbol', 'TSH', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3478f8f9-40e7-11f0-a274-84a93e04fdad', 'booking', 'default_duration', '60', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34790560-40e7-11f0-a274-84a93e04fdad', 'booking', 'buffer_time', '15', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347905bc-40e7-11f0-a274-84a93e04fdad', 'booking', 'advance_booking_days', '30', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347905f3-40e7-11f0-a274-84a93e04fdad', 'booking', 'min_advance_hours', '2', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34790815-40e7-11f0-a274-84a93e04fdad', 'booking', 'cancellation_fee', '25', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34790859-40e7-11f0-a274-84a93e04fdad', 'booking', 'auto_expire_enabled', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34790889-40e7-11f0-a274-84a93e04fdad', 'booking', 'expire_after_hours', '2', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34794ccd-40e7-11f0-a274-84a93e04fdad', 'notification', 'email_booking_confirmation', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795700-40e7-11f0-a274-84a93e04fdad', 'notification', 'email_booking_reminder', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795766-40e7-11f0-a274-84a93e04fdad', 'notification', 'email_booking_cancellation', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479579b-40e7-11f0-a274-84a93e04fdad', 'notification', 'email_promotional', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347957d7-40e7-11f0-a274-84a93e04fdad', 'notification', 'admin_new_booking', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795808-40e7-11f0-a274-84a93e04fdad', 'notification', 'admin_booking_cancellation', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795834-40e7-11f0-a274-84a93e04fdad', 'notification', 'admin_daily_summary', '0', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795861-40e7-11f0-a274-84a93e04fdad', 'notification', 'reminder_24h', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479588d-40e7-11f0-a274-84a93e04fdad', 'notification', 'reminder_5h', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347958bd-40e7-11f0-a274-84a93e04fdad', 'notification', 'reminder_30m', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347958e9-40e7-11f0-a274-84a93e04fdad', 'notification', 'reminder_at_time', '0', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479894e-40e7-11f0-a274-84a93e04fdad', 'points', 'welcome_bonus', '100', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347989c8-40e7-11f0-a274-84a93e04fdad', 'points', 'referral_bonus', '500', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798a25-40e7-11f0-a274-84a93e04fdad', 'points', 'birthday_bonus', '200', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798a77-40e7-11f0-a274-84a93e04fdad', 'points', 'review_bonus', '50', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798c76-40e7-11f0-a274-84a93e04fdad', 'points', 'points_value', '10', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798ce4-40e7-11f0-a274-84a93e04fdad', 'points', 'max_redemption_percent', '50', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798d47-40e7-11f0-a274-84a93e04fdad', 'points', 'points_expiry_days', '365', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479bf26-40e7-11f0-a274-84a93e04fdad', 'system', 'max_file_size', '10', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479c8f9-40e7-11f0-a274-84a93e04fdad', 'system', 'allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479c979-40e7-11f0-a274-84a93e04fdad', 'system', 'maintenance_mode', '0', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479c9d7-40e7-11f0-a274-84a93e04fdad', 'system', 'maintenance_message', 'We are currently performing maintenance. Please check back soon.', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479ca2e-40e7-11f0-a274-84a93e04fdad', 'system', 'session_timeout', '120', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479ca81-40e7-11f0-a274-84a93e04fdad', 'system', 'password_min_length', '8', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479cad6-40e7-11f0-a274-84a93e04fdad', 'system', 'login_attempts', '5', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479cb2b-40e7-11f0-a274-84a93e04fdad', 'system', 'lockout_duration', '15', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479cb75-40e7-11f0-a274-84a93e04fdad', 'system', 'cache_enabled', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479cbc5-40e7-11f0-a274-84a93e04fdad', 'system', 'cache_duration', '24', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('4f40db19-8f13-4a17-9464-d8553c026191', 'points', 'redeem_rate', '100', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('71b96c2e-5914-491a-b699-f214c80001e0', 'email', 'notifications', '1', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('8e8ef51d-57a6-4165-b3e7-459c7bc82de2', 'payment', 'stripe_enabled', '1', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('a1831bd1-e677-4189-b91c-1e7bc2f1b42b', 'business', 'hours', '{\"monday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"tuesday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"wednesday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"thursday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"friday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"saturday\":{\"open\":\"10:00\",\"close\":\"16:00\",\"closed\":false},\"sunday\":{\"open\":\"10:00\",\"close\":\"16:00\",\"closed\":true}}', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('b34108c4-68e1-4e5a-b407-117217b54e24', 'booking', 'advance_days', '30', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('c41ad510-e282-4790-8f15-ed58b84826f6', 'booking', 'cancellation_hours', '24', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('e415ff38-12a1-4734-bc17-f1f96d9bf964', 'business', 'name', 'Flix Salonce', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('e698120e-1b3c-4a9c-a8fb-b05743345cd5', 'points', 'earn_rate', '1', '2025-06-01 00:06:02', '2025-06-04 01:57:01');

-- --------------------------------------------------------

--
-- Table structure for table `staff_schedules`
--

CREATE TABLE `staff_schedules` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `day_of_week` varchar(20) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `is_working` tinyint(1) DEFAULT 1,
  `role` varchar(100) DEFAULT 'Staff Member',
  `hourly_rate` int(11) DEFAULT 50,
  `bio` text DEFAULT NULL,
  `experience` int(11) DEFAULT 0,
  `schedule` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`schedule`)),
  `specialties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specialties`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_schedules`
--

INSERT INTO `staff_schedules` (`id`, `user_id`, `staff_id`, `day_of_week`, `start_time`, `end_time`, `is_working`, `role`, `hourly_rate`, `bio`, `experience`, `schedule`, `specialties`, `is_active`, `created_at`, `updated_at`) VALUES
('03dd0250-4c68-46ad-8977-c04eafd5a705', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, NULL, NULL, NULL, 1, 'Hair Stylist', 600000, NULL, 0, '{\"monday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"tuesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"wednesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"thursday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"friday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"saturday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"22:00:00\"},\"sunday\":{\"is_working\":true,\"start_time\":\"10:30:00\",\"end_time\":\"15:00:00\"}}', NULL, 1, '2025-06-13 23:20:48', '2025-06-13 23:20:48'),
('0d6f8a0c-b7a7-4c23-92c3-41c2c6b40135', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'tuesday', '09:00:00', '17:00:00', 1, 'Hair Stylist', 600000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:20:48', '2025-06-13 23:20:48'),
('14fa920c-73c3-4a72-a0ef-951ac41a9028', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'friday', '09:00:00', '17:00:00', 1, 'Hair Stylist', 600000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:20:48', '2025-06-13 23:20:48'),
('166cc6f1-9657-4e89-9cb6-c2250d51d673', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, 'saturday', '10:00:00', '17:00:00', 1, 'Nail Technician', 340000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:22:18', '2025-06-13 23:22:18'),
('19654e6a-cfb2-421f-be8a-365c5c62c566', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, NULL, NULL, NULL, 1, 'Nail Technician', 340000, NULL, 0, '{\"monday\":{\"is_working\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\"},\"tuesday\":{\"is_working\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\"},\"wednesday\":{\"is_working\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\"},\"thursday\":{\"is_working\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\"},\"friday\":{\"is_working\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\"},\"saturday\":{\"is_working\":true,\"start_time\":\"10:00\",\"end_time\":\"17:00\"},\"sunday\":{\"is_working\":false,\"start_time\":null,\"end_time\":null}}', NULL, 1, '2025-06-13 23:22:18', '2025-06-13 23:22:18'),
('2fc61eb4-7d14-4b85-bbfd-9e5189ed89ca', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, 'wednesday', '09:00:00', '17:00:00', 1, 'Nail Technician', 340000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:22:18', '2025-06-13 23:22:18'),
('344e026d-b8dd-43ea-a29f-6548890b1fa0', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'monday', '09:00:00', '17:00:00', 1, 'Hair Stylist', 600000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:20:48', '2025-06-13 23:20:48'),
('3b35eadf-5335-413d-a3dd-ac13f9a0f403', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, 'tuesday', '09:00:00', '17:00:00', 1, 'Nail Technician', 340000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:22:18', '2025-06-13 23:22:18'),
('8adf8005-165b-42c9-9ff7-278f739cd013', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'thursday', '09:00:00', '17:00:00', 1, 'Hair Stylist', 600000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:20:48', '2025-06-13 23:20:48'),
('92e6bfb2-f069-43fe-aa8d-bb6cf6ebb611', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, 'friday', '09:00:00', '17:00:00', 1, 'Nail Technician', 340000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:22:18', '2025-06-13 23:22:18'),
('adecb9bd-1676-4add-991b-e82e97ee3f7e', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, 'thursday', '09:00:00', '17:00:00', 1, 'Nail Technician', 340000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:22:18', '2025-06-13 23:22:18'),
('b442a62e-78c5-4862-b36f-60371b68359d', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'saturday', '09:00:00', '22:00:00', 1, 'Hair Stylist', 600000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:20:48', '2025-06-13 23:20:48'),
('c52a53d8-c942-42a7-813b-268ce10bcabf', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'wednesday', '09:00:00', '17:00:00', 1, 'Hair Stylist', 600000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:20:48', '2025-06-13 23:20:48'),
('e532781b-c012-4c07-a876-38a908680e57', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, 'monday', '09:00:00', '17:00:00', 1, 'Nail Technician', 340000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:22:18', '2025-06-13 23:22:18'),
('f73ccea6-e3dc-42d1-ba22-90b77326e5fb', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'sunday', '10:30:00', '15:00:00', 1, 'Hair Stylist', 600000, NULL, 0, NULL, NULL, 1, '2025-06-13 23:20:48', '2025-06-13 23:20:48');

-- --------------------------------------------------------

--
-- Table structure for table `staff_schedules_backup_2025_06_14_02_09_08`
--

CREATE TABLE `staff_schedules_backup_2025_06_14_02_09_08` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `day_of_week` varchar(20) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `is_working` tinyint(1) DEFAULT 1,
  `role` varchar(100) DEFAULT 'Staff Member',
  `hourly_rate` int(11) DEFAULT 50,
  `bio` text DEFAULT NULL,
  `experience` int(11) DEFAULT 0,
  `schedule` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`schedule`)),
  `specialties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specialties`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_schedules_backup_2025_06_14_02_09_08`
--

INSERT INTO `staff_schedules_backup_2025_06_14_02_09_08` (`id`, `user_id`, `staff_id`, `day_of_week`, `start_time`, `end_time`, `is_working`, `role`, `hourly_rate`, `bio`, `experience`, `schedule`, `specialties`, `is_active`, `created_at`, `updated_at`) VALUES
('11f192a6-4858-47ab-96a3-841f6210d4fd', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'monday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('1a986767-868a-44f5-9e61-32c6ed2b8793', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'wednesday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('200ae44c-7b50-4ba5-b9b9-da6a5e3e768f', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, NULL, NULL, NULL, 1, 'Nail Technician', 50, NULL, 0, '{\"monday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"tuesday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"wednesday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"thursday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"friday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"saturday\":{\"is_working\":true,\"available\":true,\"start_time\":\"08:00\",\"end_time\":\"17:00\",\"start\":\"08:00\",\"end\":\"17:00\"},\"sunday\":{\"is_working\":false,\"available\":false,\"start_time\":\"09:00\",\"end_time\":\"18:00\",\"start\":\"09:00\",\"end\":\"18:00\"}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-13 22:52:06'),
('4e311245-e81b-4d10-84b8-3e6425d54aa4', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'sunday', '07:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('63ddf464-2b52-4ecc-a096-1a57832109da', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, NULL, NULL, NULL, 1, 'Senior Stylist', 1000, NULL, 0, '{\"monday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"tuesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"wednesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"thursday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"friday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"saturday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"22:00:00\"},\"sunday\":{\"is_working\":true,\"start_time\":\"07:00:00\",\"end_time\":\"17:00:00\"}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-13 22:52:42'),
('6a96db06-44bf-4725-8041-861460dd5c4b', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'thursday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('860d5002-d773-456d-95ad-ee1c5cfbc0f6', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'friday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('8acaa3bb-64e0-403e-9a27-82fa37b6de60', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'tuesday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('be9c065a-602d-4462-bdb7-591e42eb49e5', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'saturday', '09:00:00', '22:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42');

-- --------------------------------------------------------

--
-- Table structure for table `staff_schedules_backup_2025_06_14_02_10_03`
--

CREATE TABLE `staff_schedules_backup_2025_06_14_02_10_03` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `day_of_week` varchar(20) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `is_working` tinyint(1) DEFAULT 1,
  `role` varchar(100) DEFAULT 'Staff Member',
  `hourly_rate` int(11) DEFAULT 50,
  `bio` text DEFAULT NULL,
  `experience` int(11) DEFAULT 0,
  `schedule` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`schedule`)),
  `specialties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specialties`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_schedules_backup_2025_06_14_02_10_03`
--

INSERT INTO `staff_schedules_backup_2025_06_14_02_10_03` (`id`, `user_id`, `staff_id`, `day_of_week`, `start_time`, `end_time`, `is_working`, `role`, `hourly_rate`, `bio`, `experience`, `schedule`, `specialties`, `is_active`, `created_at`, `updated_at`) VALUES
('11f192a6-4858-47ab-96a3-841f6210d4fd', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'monday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('1a986767-868a-44f5-9e61-32c6ed2b8793', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'wednesday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('200ae44c-7b50-4ba5-b9b9-da6a5e3e768f', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, NULL, NULL, NULL, 1, 'Nail Technician', 50, NULL, 0, '{\"monday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"tuesday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"wednesday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"thursday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"friday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"saturday\":{\"is_working\":true,\"available\":true,\"start_time\":\"08:00\",\"end_time\":\"17:00\",\"start\":\"08:00\",\"end\":\"17:00\"},\"sunday\":{\"is_working\":false,\"available\":false,\"start_time\":\"09:00\",\"end_time\":\"18:00\",\"start\":\"09:00\",\"end\":\"18:00\"}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-13 22:52:06'),
('4e311245-e81b-4d10-84b8-3e6425d54aa4', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'sunday', '07:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('63ddf464-2b52-4ecc-a096-1a57832109da', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, NULL, NULL, NULL, 1, 'Senior Stylist', 1000, NULL, 0, '{\"monday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"tuesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"wednesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"thursday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"friday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"saturday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"22:00:00\"},\"sunday\":{\"is_working\":true,\"start_time\":\"07:00:00\",\"end_time\":\"17:00:00\"}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-13 22:52:42'),
('6a96db06-44bf-4725-8041-861460dd5c4b', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'thursday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('860d5002-d773-456d-95ad-ee1c5cfbc0f6', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'friday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('8acaa3bb-64e0-403e-9a27-82fa37b6de60', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'tuesday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('be9c065a-602d-4462-bdb7-591e42eb49e5', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'saturday', '09:00:00', '22:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42');

-- --------------------------------------------------------

--
-- Table structure for table `staff_schedules_backup_2025_06_14_02_10_42`
--

CREATE TABLE `staff_schedules_backup_2025_06_14_02_10_42` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `day_of_week` varchar(20) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `is_working` tinyint(1) DEFAULT 1,
  `role` varchar(100) DEFAULT 'Staff Member',
  `hourly_rate` int(11) DEFAULT 50,
  `bio` text DEFAULT NULL,
  `experience` int(11) DEFAULT 0,
  `schedule` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`schedule`)),
  `specialties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specialties`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_schedules_backup_2025_06_14_02_10_42`
--

INSERT INTO `staff_schedules_backup_2025_06_14_02_10_42` (`id`, `user_id`, `staff_id`, `day_of_week`, `start_time`, `end_time`, `is_working`, `role`, `hourly_rate`, `bio`, `experience`, `schedule`, `specialties`, `is_active`, `created_at`, `updated_at`) VALUES
('11f192a6-4858-47ab-96a3-841f6210d4fd', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'monday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('1a986767-868a-44f5-9e61-32c6ed2b8793', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'wednesday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('200ae44c-7b50-4ba5-b9b9-da6a5e3e768f', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, NULL, NULL, NULL, 1, 'Nail Technician', 50, NULL, 0, '{\"monday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"tuesday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"wednesday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"thursday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"friday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"saturday\":{\"is_working\":true,\"available\":true,\"start_time\":\"08:00\",\"end_time\":\"17:00\",\"start\":\"08:00\",\"end\":\"17:00\"},\"sunday\":{\"is_working\":false,\"available\":false,\"start_time\":\"09:00\",\"end_time\":\"18:00\",\"start\":\"09:00\",\"end\":\"18:00\"}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-13 22:52:06'),
('4e311245-e81b-4d10-84b8-3e6425d54aa4', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'sunday', '07:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('63ddf464-2b52-4ecc-a096-1a57832109da', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, NULL, NULL, NULL, 1, 'Senior Stylist', 1000, NULL, 0, '{\"monday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"tuesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"wednesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"thursday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"friday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"saturday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"22:00:00\"},\"sunday\":{\"is_working\":true,\"start_time\":\"07:00:00\",\"end_time\":\"17:00:00\"}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-13 22:52:42'),
('6a96db06-44bf-4725-8041-861460dd5c4b', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'thursday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('860d5002-d773-456d-95ad-ee1c5cfbc0f6', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'friday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('8acaa3bb-64e0-403e-9a27-82fa37b6de60', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'tuesday', '09:00:00', '17:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42'),
('be9c065a-602d-4462-bdb7-591e42eb49e5', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'saturday', '09:00:00', '22:00:00', 1, 'Senior Stylist', 1000, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-13 22:52:42');

-- --------------------------------------------------------

--
-- Table structure for table `staff_specialties`
--

CREATE TABLE `staff_specialties` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `service_id` varchar(36) NOT NULL,
  `proficiency_level` enum('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT') DEFAULT 'INTERMEDIATE',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_specialties`
--

INSERT INTO `staff_specialties` (`id`, `user_id`, `service_id`, `proficiency_level`, `created_at`, `updated_at`) VALUES
('0d52d5b7-5fc7-42b7-8a2c-04559f8fac68', '8c42848c-6123-4438-9f23-8f5c07680371', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'INTERMEDIATE', '2025-06-13 23:21:52', '2025-06-13 23:21:52'),
('3f866ea5-7031-4623-9358-7aa4938f77dc', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'c001781b-954d-4ca6-a48a-66e20b6b1ed7', 'INTERMEDIATE', '2025-06-13 23:20:16', '2025-06-13 23:20:16'),
('5ce998cd-df7c-416a-b786-1eb6861f8438', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'bde7fc78-ba62-4f92-9e2a-81214bb5a247', 'INTERMEDIATE', '2025-06-13 23:20:16', '2025-06-13 23:20:16'),
('84f0f274-1030-4354-8e6d-d579767d3346', '8c42848c-6123-4438-9f23-8f5c07680371', '386ff31e-0274-443f-8807-53202fa0b7fe', 'INTERMEDIATE', '2025-06-13 23:21:52', '2025-06-13 23:21:52'),
('a851f640-b996-41ce-baab-f6f08462044c', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '5ffce5a5-f93e-4fdd-bb66-7c54376f0fef', 'INTERMEDIATE', '2025-06-13 23:20:16', '2025-06-13 23:20:16'),
('b2052e3b-5a16-4caa-8d02-be9e025d9ee2', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '958b58c2-c476-463b-b7f6-12b82fda1930', 'INTERMEDIATE', '2025-06-13 23:20:16', '2025-06-13 23:20:16'),
('d4582735-3357-4cdd-a865-b12be7ccdb07', '8c42848c-6123-4438-9f23-8f5c07680371', '389f948c-141b-4fa3-9ec1-c1887d9ed745', 'INTERMEDIATE', '2025-06-13 23:21:52', '2025-06-13 23:21:52');

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `updated_at`) VALUES
(1, 'points_migration_completed', '2025-06-02 19:19:03', '2025-06-02 16:19:03'),
(2, 'last_reminder_check', '2025-06-04 00:18:29', '2025-06-03 21:18:29'),
(9, 'last_expiration_check', '2025-07-04 00:58:00', '2025-07-03 21:58:00'),
(11, 'payment_enabled', 'true', '2025-06-08 16:34:24'),
(12, 'stripe_enabled', 'true', '2025-06-08 16:34:24'),
(13, 'flutterwave_enabled', 'true', '2025-06-08 16:34:24'),
(14, 'payment_timeout_minutes', '30', '2025-06-08 16:34:24'),
(15, 'max_verification_attempts', '3', '2025-06-08 16:34:24');

-- --------------------------------------------------------

--
-- Stand-in structure for view `upcoming_appointments`
-- (See below for the actual view)
--
CREATE TABLE `upcoming_appointments` (
`id` varchar(36)
,`user_id` varchar(36)
,`staff_id` varchar(36)
,`date` date
,`start_time` time
,`status` enum('PENDING','CONFIRMED','IN_PROGRESS','COMPLETED','CANCELLED','NO_SHOW','EXPIRED')
,`appointment_datetime` varchar(21)
,`minutes_until` bigint(21)
,`customer_name` varchar(255)
,`customer_email` varchar(255)
,`customer_phone` varchar(20)
,`service_name` varchar(255)
,`staff_name` varchar(255)
,`package_name` varchar(255)
);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `role` enum('CUSTOMER','ADMIN','STAFF') DEFAULT 'CUSTOMER',
  `points` int(11) DEFAULT 0,
  `referral_code` varchar(10) DEFAULT NULL,
  `referred_by` varchar(36) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1,
  `reset_otp` varchar(6) DEFAULT NULL,
  `otp_expires_at` timestamp NULL DEFAULT NULL,
  `otp_attempts` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `image`, `password`, `phone`, `date_of_birth`, `role`, `points`, `referral_code`, `referred_by`, `created_at`, `updated_at`, `is_active`, `reset_otp`, `otp_expires_at`, `otp_attempts`) VALUES
('12a6f3a6-7f1d-4bfc-87c9-147153949d9a', 'Black Zu', '<EMAIL>', NULL, NULL, '$2y$12$7bl5eKhdtGHXLBni.IuQq.6gsJnxYwQr9ZhODW8ypfoNO9sMpJtBu', '', NULL, 'CUSTOMER', 100, 'BLA752', '4544f774-891d-4333-85c4-e78c998882cf', '2025-06-14 18:47:18', '2025-06-14 22:04:15', 1, '248692', '2025-06-14 19:07:22', 0),
('1ec02ea0-8b70-44b0-bf07-c59a279dae8d', 'Craq Venture', '<EMAIL>', NULL, NULL, '$2y$12$SI95UabiDCyFg6Jgkv0ji.Z9HmP/.v8j/X.S8qwv3LXWjL3i2qrGK', '0787574355', NULL, 'CUSTOMER', 200, 'CRA550', NULL, '2025-06-02 22:14:32', '2025-06-14 18:43:47', 1, NULL, NULL, 0),
('350e5a4d-da0d-4c42-ab08-302eb513c732', 'John Customer', '<EMAIL>', NULL, NULL, '$2y$10$QRoLGv9BUjKACGqU.jpDkOcAC8KzNfR.rkiA/o30KUfBb0L.4CZea', NULL, NULL, 'CUSTOMER', 6, 'JOH001', NULL, '2025-06-01 00:06:01', '2025-06-02 20:52:05', 1, NULL, NULL, 0),
('3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'chaz vet', '<EMAIL>', NULL, NULL, '$2y$10$N6hEG6AWi4C5mizpmWQ/6eMhGS0EYhAB9jozdvD64BRJvqoUozBt.', '+255622518815', NULL, 'STAFF', 0, NULL, NULL, '2025-06-01 19:12:51', '2025-06-13 23:20:16', 1, NULL, NULL, 0),
('4544f774-891d-4333-85c4-e78c998882cf', 'chare', '<EMAIL>', NULL, NULL, '$2y$12$cQKiOFF7eD6xkxx5gJQtkuYGHpCVkgskHQb3XJNC.maGDqEZpq6Da', '', '2001-04-12', 'CUSTOMER', 606, 'CHA038', NULL, '2025-06-07 22:22:15', '2025-07-07 01:39:27', 1, '254616', '2025-06-14 19:10:09', 0),
('6085b727-0ac8-4d95-b10f-c6e766420e0c', 'Lisa Garcia', '<EMAIL>', NULL, NULL, '$2y$10$sl/.2ndIF2ema243FGTCH.vj2v4/0doORgF50QvcFIL8SuvT9FpqC', NULL, NULL, 'CUSTOMER', 0, 'LIS001', NULL, '2025-06-01 00:06:02', '2025-06-02 20:21:12', 1, NULL, NULL, 0),
('8c42848c-6123-4438-9f23-8f5c07680371', 'Staff Test', '<EMAIL>', NULL, NULL, '$2y$10$839BNTNhUot2ia5JWqFzeeYW6lWxJqg0/.IdolIJm/c563YEoXM7.', '+255622518815', NULL, 'STAFF', 0, 'STF001', NULL, '2025-06-01 00:06:01', '2025-06-13 23:21:52', 1, NULL, NULL, 0),
('bd021aa2-59cd-4423-97c2-a4e7698460a3', 'Ailya Hassan', '<EMAIL>', NULL, 'https://img.freepik.com/free-psd/traveler-desert-hot-sun_23-2150177805.jpg?t=st=1748936874~exp=1748940474~hmac=6b09110ebc259ed8688b881bfd83c6b0b9ddca033205da520ce7fdb5713ef4fc&w=1480', '$2y$12$HiJkrbYbCQ30x3KmiP0.heOWHYaENSnn3hJbfZxRrsossBYClyhku', '0787574355', '2004-06-19', 'CUSTOMER', 0, 'MMB111', NULL, '2025-06-03 02:13:54', '2025-06-05 21:29:24', 1, NULL, NULL, 0),
('e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'Flix Admin', '<EMAIL>', NULL, '/uploads/profiles/profile_e0a2235d-ded8-4775-96b6-9e8a3cfe3346_1751747698.jpg', '$2y$10$YATFTcMpx0zkawHaT1cD5e62A9q7j/pHgsEsgRAuD7YMw03TFjhFa', NULL, NULL, 'ADMIN', 0, 'ADM001', NULL, '2025-06-01 00:06:01', '2025-07-07 01:39:40', 1, NULL, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `wishlists`
--

CREATE TABLE `wishlists` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `item_type` enum('service','package') NOT NULL,
  `item_id` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure for view `upcoming_appointments`
--
DROP TABLE IF EXISTS `upcoming_appointments`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `upcoming_appointments`  AS SELECT `b`.`id` AS `id`, `b`.`user_id` AS `user_id`, `b`.`staff_id` AS `staff_id`, `b`.`date` AS `date`, `b`.`start_time` AS `start_time`, `b`.`status` AS `status`, concat(`b`.`date`,' ',`b`.`start_time`) AS `appointment_datetime`, timestampdiff(MINUTE,current_timestamp(),concat(`b`.`date`,' ',`b`.`start_time`)) AS `minutes_until`, `u`.`name` AS `customer_name`, `u`.`email` AS `customer_email`, `u`.`phone` AS `customer_phone`, `s`.`name` AS `service_name`, `st`.`name` AS `staff_name`, `p`.`name` AS `package_name` FROM ((((`bookings` `b` left join `users` `u` on(`b`.`user_id` = `u`.`id`)) left join `services` `s` on(`b`.`service_id` = `s`.`id`)) left join `users` `st` on(`b`.`staff_id` = `st`.`id` and `st`.`role` = 'STAFF')) left join `packages` `p` on(`b`.`package_id` = `p`.`id`)) WHERE `b`.`status` in ('CONFIRMED','PENDING') AND concat(`b`.`date`,' ',`b`.`start_time`) > current_timestamp() ORDER BY concat(`b`.`date`,' ',`b`.`start_time`) ASC ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_2fa_attempts`
--
ALTER TABLE `admin_2fa_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_2fa_attempts_admin_id` (`admin_id`),
  ADD KEY `idx_admin_2fa_attempts_ip` (`ip_address`),
  ADD KEY `idx_admin_2fa_attempts_time` (`attempted_at`);

--
-- Indexes for table `admin_2fa_backup_codes`
--
ALTER TABLE `admin_2fa_backup_codes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_2fa_backup_admin_id` (`admin_id`),
  ADD KEY `idx_admin_2fa_backup_used` (`is_used`);

--
-- Indexes for table `admin_2fa_email_codes`
--
ALTER TABLE `admin_2fa_email_codes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_2fa_email_admin_id` (`admin_id`),
  ADD KEY `idx_admin_2fa_email_expires` (`expires_at`),
  ADD KEY `idx_admin_2fa_email_code` (`code`);

--
-- Indexes for table `admin_2fa_logs`
--
ALTER TABLE `admin_2fa_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_2fa_logs_admin_id` (`admin_id`),
  ADD KEY `idx_admin_2fa_logs_action` (`action`),
  ADD KEY `idx_admin_2fa_logs_created_at` (`created_at`);

--
-- Indexes for table `admin_2fa_settings`
--
ALTER TABLE `admin_2fa_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admin_id` (`admin_id`),
  ADD KEY `idx_admin_2fa_admin_id` (`admin_id`);

--
-- Indexes for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_logs_admin_id` (`admin_id`),
  ADD KEY `idx_admin_logs_action` (`action`),
  ADD KEY `idx_admin_logs_created_at` (`created_at`);

--
-- Indexes for table `blog`
--
ALTER TABLE `blog`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `blog_posts`
--
ALTER TABLE `blog_posts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `author_id` (`author_id`),
  ADD KEY `idx_blog_posts_slug` (`slug`),
  ADD KEY `idx_blog_posts_status` (`status`),
  ADD KEY `idx_blog_posts_publish_date` (`publish_date`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `package_id` (`package_id`),
  ADD KEY `offer_id` (`offer_id`),
  ADD KEY `idx_bookings_user_id` (`user_id`),
  ADD KEY `idx_bookings_date` (`date`),
  ADD KEY `idx_bookings_status` (`status`),
  ADD KEY `bookings_staff_fk` (`staff_id`),
  ADD KEY `idx_bookings_expiration` (`status`,`date`,`start_time`),
  ADD KEY `idx_bookings_status_date` (`status`,`date`),
  ADD KEY `idx_bookings_reminder_check` (`status`,`date`,`start_time`),
  ADD KEY `service_variation_id` (`service_variation_id`);

--
-- Indexes for table `booking_reminders`
--
ALTER TABLE `booking_reminders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_booking_reminder` (`booking_id`,`reminder_type`),
  ADD KEY `idx_booking_id` (`booking_id`),
  ADD KEY `idx_reminder_type` (`reminder_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_scheduled_time` (`scheduled_time`),
  ADD KEY `idx_pending_reminders` (`status`,`scheduled_time`),
  ADD KEY `idx_booking_reminder_type` (`booking_id`,`reminder_type`);

--
-- Indexes for table `booking_status_log`
--
ALTER TABLE `booking_status_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `booking_id` (`booking_id`),
  ADD KEY `changed_by` (`changed_by`);

--
-- Indexes for table `cms_content`
--
ALTER TABLE `cms_content`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `contact_messages`
--
ALTER TABLE `contact_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_subject` (`subject`);

--
-- Indexes for table `customer_messages`
--
ALTER TABLE `customer_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customer_messages_customer` (`customer_id`),
  ADD KEY `idx_customer_messages_admin` (`admin_id`),
  ADD KEY `idx_customer_messages_status` (`status`),
  ADD KEY `idx_customer_messages_created` (`created_at`);

--
-- Indexes for table `customer_tiers`
--
ALTER TABLE `customer_tiers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `custom_services`
--
ALTER TABLE `custom_services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_custom_services_active` (`is_active`),
  ADD KEY `idx_custom_services_category` (`category`),
  ADD KEY `idx_custom_services_name` (`name`);

--
-- Indexes for table `email_logs`
--
ALTER TABLE `email_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_recipient` (`recipient`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `faqs`
--
ALTER TABLE `faqs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gallery`
--
ALTER TABLE `gallery`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `newsletter`
--
ALTER TABLE `newsletter`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_notifications_user_id` (`user_id`),
  ADD KEY `idx_notifications_read` (`is_read`),
  ADD KEY `idx_notifications_category` (`category`),
  ADD KEY `idx_notifications_type` (`type`),
  ADD KEY `idx_notifications_priority` (`priority`),
  ADD KEY `idx_notifications_created_at` (`created_at`),
  ADD KEY `idx_notifications_expires_at` (`expires_at`);

--
-- Indexes for table `offers`
--
ALTER TABLE `offers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `packages`
--
ALTER TABLE `packages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_packages_type` (`package_type`),
  ADD KEY `idx_packages_manual_services` (`manual_services`(255)),
  ADD KEY `idx_packages_duration` (`package_duration`);

--
-- Indexes for table `package_custom_services`
--
ALTER TABLE `package_custom_services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_package_custom_service` (`package_id`,`custom_service_id`),
  ADD KEY `idx_package_custom_services_package` (`package_id`),
  ADD KEY `idx_package_custom_services_service` (`custom_service_id`);

--
-- Indexes for table `package_services`
--
ALTER TABLE `package_services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_package_service` (`package_id`,`service_id`),
  ADD KEY `service_id` (`service_id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_id` (`booking_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_payments_gateway` (`payment_gateway`),
  ADD KEY `idx_payments_reference` (`payment_reference`),
  ADD KEY `idx_payments_status` (`status`),
  ADD KEY `idx_payments_booking` (`booking_id`),
  ADD KEY `idx_payments_stripe_id` (`stripe_payment_id`),
  ADD KEY `idx_payments_flutterwave_ref` (`flutterwave_tx_ref`),
  ADD KEY `idx_payments_dpo_token` (`dpo_token`);

--
-- Indexes for table `payment_logs`
--
ALTER TABLE `payment_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_payment_logs_payment` (`payment_id`),
  ADD KEY `idx_payment_logs_event` (`event_type`);

--
-- Indexes for table `payment_webhooks`
--
ALTER TABLE `payment_webhooks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `payment_id` (`payment_id`),
  ADD KEY `idx_payment_webhooks_gateway` (`gateway`),
  ADD KEY `idx_payment_webhooks_status` (`status`),
  ADD KEY `idx_payment_webhooks_webhook_id` (`webhook_id`);

--
-- Indexes for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_reward_id` (`reward_id`);

--
-- Indexes for table `reminder_logs`
--
ALTER TABLE `reminder_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_reminder_id` (`reminder_id`),
  ADD KEY `idx_booking_id` (`booking_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `reminder_rate_limit`
--
ALTER TABLE `reminder_rate_limit`
  ADD PRIMARY KEY (`booking_id`,`reminder_type`),
  ADD KEY `idx_last_sent` (`last_sent`);

--
-- Indexes for table `reviews`
--
ALTER TABLE `reviews`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customer_id` (`customer_id`),
  ADD KEY `idx_service_id` (`service_id`),
  ADD KEY `idx_package_id` (`package_id`),
  ADD KEY `idx_booking_id` (`booking_id`),
  ADD KEY `idx_rating` (`rating`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_is_verified` (`is_verified`),
  ADD KEY `idx_is_featured` (`is_featured`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `rewards`
--
ALTER TABLE `rewards`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_services_category` (`category`),
  ADD KEY `idx_services_active` (`is_active`),
  ADD KEY `idx_services_category_id` (`category_id`),
  ADD KEY `idx_services_subcategory_id` (`subcategory_id`);

--
-- Indexes for table `service_categories`
--
ALTER TABLE `service_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_category_name` (`name`);

--
-- Indexes for table `service_subcategories`
--
ALTER TABLE `service_subcategories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_subcategory_per_category` (`name`,`category_id`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_sort_order` (`sort_order`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `service_variations`
--
ALTER TABLE `service_variations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_service_variations_service_id` (`service_id`),
  ADD KEY `idx_service_variations_active` (`is_active`),
  ADD KEY `idx_service_variations_sort` (`sort_order`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `session_token` (`session_token`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_sessions_2fa_pending` (`pending_2fa_admin_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_setting` (`category`,`setting_key`);

--
-- Indexes for table `staff_schedules`
--
ALTER TABLE `staff_schedules`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `staff_specialties`
--
ALTER TABLE `staff_specialties`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_service` (`user_id`,`service_id`),
  ADD KEY `service_id` (`service_id`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `referral_code` (`referral_code`),
  ADD KEY `referred_by` (`referred_by`),
  ADD KEY `idx_users_email` (`email`),
  ADD KEY `idx_users_role` (`role`),
  ADD KEY `idx_users_reset_otp` (`reset_otp`),
  ADD KEY `idx_users_otp_expires` (`otp_expires_at`);

--
-- Indexes for table `wishlists`
--
ALTER TABLE `wishlists`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_wishlist_item` (`user_id`,`item_type`,`item_id`),
  ADD KEY `idx_user_wishlist` (`user_id`),
  ADD KEY `idx_item_type` (`item_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `email_logs`
--
ALTER TABLE `email_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=126;

--
-- AUTO_INCREMENT for table `faqs`
--
ALTER TABLE `faqs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `reminder_logs`
--
ALTER TABLE `reminder_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=153;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_2fa_attempts`
--
ALTER TABLE `admin_2fa_attempts`
  ADD CONSTRAINT `admin_2fa_attempts_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_2fa_backup_codes`
--
ALTER TABLE `admin_2fa_backup_codes`
  ADD CONSTRAINT `admin_2fa_backup_codes_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_2fa_email_codes`
--
ALTER TABLE `admin_2fa_email_codes`
  ADD CONSTRAINT `admin_2fa_email_codes_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_2fa_logs`
--
ALTER TABLE `admin_2fa_logs`
  ADD CONSTRAINT `admin_2fa_logs_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_2fa_settings`
--
ALTER TABLE `admin_2fa_settings`
  ADD CONSTRAINT `admin_2fa_settings_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD CONSTRAINT `admin_logs_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `blog_posts`
--
ALTER TABLE `blog_posts`
  ADD CONSTRAINT `blog_posts_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bookings_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_ibfk_3` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_ibfk_5` FOREIGN KEY (`offer_id`) REFERENCES `offers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_ibfk_6` FOREIGN KEY (`service_variation_id`) REFERENCES `service_variations` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_staff_fk` FOREIGN KEY (`staff_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `booking_status_log`
--
ALTER TABLE `booking_status_log`
  ADD CONSTRAINT `booking_status_log_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_status_log_ibfk_2` FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `customer_messages`
--
ALTER TABLE `customer_messages`
  ADD CONSTRAINT `customer_messages_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_messages_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `package_custom_services`
--
ALTER TABLE `package_custom_services`
  ADD CONSTRAINT `package_custom_services_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `package_custom_services_ibfk_2` FOREIGN KEY (`custom_service_id`) REFERENCES `custom_services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `package_services`
--
ALTER TABLE `package_services`
  ADD CONSTRAINT `package_services_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `package_services_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payment_logs`
--
ALTER TABLE `payment_logs`
  ADD CONSTRAINT `payment_logs_ibfk_1` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payment_webhooks`
--
ALTER TABLE `payment_webhooks`
  ADD CONSTRAINT `payment_webhooks_ibfk_1` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD CONSTRAINT `point_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `services`
--
ALTER TABLE `services`
  ADD CONSTRAINT `fk_services_category` FOREIGN KEY (`category_id`) REFERENCES `service_categories` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_services_subcategory` FOREIGN KEY (`subcategory_id`) REFERENCES `service_subcategories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `service_subcategories`
--
ALTER TABLE `service_subcategories`
  ADD CONSTRAINT `service_subcategories_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `service_categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `service_variations`
--
ALTER TABLE `service_variations`
  ADD CONSTRAINT `service_variations_ibfk_1` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff_schedules`
--
ALTER TABLE `staff_schedules`
  ADD CONSTRAINT `staff_schedules_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff_specialties`
--
ALTER TABLE `staff_specialties`
  ADD CONSTRAINT `staff_specialties_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_specialties_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`referred_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `wishlists`
--
ALTER TABLE `wishlists`
  ADD CONSTRAINT `wishlists_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
