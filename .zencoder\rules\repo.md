---
description: Repository Information Overview
alwaysApply: true
---

# Redolence Salon & Spa Management System

## Summary
A comprehensive PHP-based salon and spa management system with booking, service management, staff scheduling, customer management, and payment processing capabilities. The system includes separate interfaces for customers, staff, and administrators.

## Structure
- **admin/**: Admin panel for system management and reporting
- **api/**: API endpoints for various functionalities
- **assets/**: Static assets (CSS, JS, images)
- **auth/**: Authentication related pages and scripts
- **config/**: Configuration files for database, app settings
- **cron/**: Scheduled tasks for reminders and maintenance
- **customer/**: Customer-facing portal for bookings and profile
- **database/**: Database schema, migrations, and SQL scripts
- **docs/**: Documentation files and implementation guides
- **includes/**: PHP include files for functions and components
- **staff/**: Staff portal for appointments and schedules
- **uploads/**: Uploaded files for services, profiles, and blog

## Language & Runtime
**Language**: PHP
**Version**: Compatible with PHP 7.4+
**Database**: MySQL/MariaDB
**Web Server**: Apache (with mod_rewrite)

## Dependencies
**Main Dependencies**:
- tecnickcom/tcpdf: ^6.9 (PDF generation)
- firebase/php-jwt: ^6.0 (JWT authentication)
- stripe/stripe-php: ^10.0 (Payment processing)

**Development Dependencies**:
- XAMPP/WAMP/LAMP stack for local development

## Build & Installation
```bash
# Clone repository
git clone [repository-url]

# Install dependencies
composer install

# Configure application
cp config/app.php.example config/app.php
cp config/database.php.example config/database.php

# Edit configuration files with appropriate settings
# Import database schema
mysql -u username -p database_name < setup_redolence_database.sql
```

## Authentication System
**Features**:
- Multi-role authentication (Admin, Staff, Customer)
- Two-factor authentication (2FA) for admin accounts
- Password reset functionality
- Session management with security features

## Main Components

### Admin Panel
**Entry Point**: admin/index.php
**Key Features**:
- Service and category management
- Staff scheduling and management
- Booking management and reporting
- Customer management
- Financial reporting

### Customer Portal
**Entry Point**: customer/index.php
**Key Features**:
- Service browsing and booking
- Appointment management
- Profile management
- Payment history

### Staff Portal
**Entry Point**: staff/index.php
**Key Features**:
- Appointment schedule viewing
- Earnings tracking
- Notification management

### Booking System
**Key Files**:
- includes/booking_functions.php
- includes/booking_reminder_functions.php
- customer/book/index.php

### Payment Processing
**Integrations**:
- Stripe payment gateway
- Direct Pay Online (DPO)
- Payment verification and receipt generation

## Database Structure
The system uses a relational database with tables for:
- Users (admin, staff, customers)
- Services and categories
- Bookings and appointments
- Payments and transactions
- Content management (blog, FAQ)

## Cron Jobs
**Scheduled Tasks**:
- Booking reminders (cron/send_reminders.php)
- Expired booking cleanup
- Notification processing