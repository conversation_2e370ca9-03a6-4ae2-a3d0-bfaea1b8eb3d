<?php
session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Get date range filters
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-t'); // Last day of current month

// Get staff data
$staffId = $_SESSION['user_id'];
$profile = getStaffProfile($staffId);
$monthlyStats = getMonthlyStats($staffId);
$performanceMetrics = getPerformanceMetrics($staffId);
$earnings = getStaffEarnings($staffId, $startDate, $endDate);

// Calculate totals for the period
$totalEarnings = array_sum(array_column($earnings, 'commission'));
$totalRevenue = array_sum(array_column($earnings, 'revenue'));
$totalAppointments = array_sum(array_column($earnings, 'completed'));

$pageTitle = "Medical Earnings Dashboard";
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Earnings Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-redolence-navy">Medical Earnings <span class="text-redolence-green">Dashboard</span></h1>
            <p class="mt-2 text-lg text-gray-600">Track your medical consultation commission and professional performance metrics</p>
            <div class="mt-3 flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                Professional Medical Financial Tracking
            </div>
        </div>
        <div class="mt-6 sm:mt-0">
            <div class="text-center p-4 bg-gradient-to-r from-redolence-green/10 to-redolence-green/5 rounded-xl border border-redolence-green/20">
                <p class="text-sm font-medium text-gray-600">Medical Commission Rate</p>
                <p class="text-2xl font-bold text-redolence-green">60%</p>
                <p class="text-xs text-gray-500 mt-1">Professional Rate</p>
            </div>
        </div>
    </div>
</div>

<!-- Medical Date Range Filter -->
<div class="medical-card p-6 mb-8">
    <h3 class="text-xl font-bold text-redolence-navy mb-6">Medical Earnings Period Filter</h3>
    <form method="GET" class="flex flex-col sm:flex-row gap-6">
        <div class="flex-1">
            <label for="start_date" class="block text-sm font-semibold text-redolence-navy mb-3">Treatment Period Start</label>
            <input type="date" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>"
                   class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
        </div>
        <div class="flex-1">
            <label for="end_date" class="block text-sm font-semibold text-redolence-navy mb-3">Treatment Period End</label>
            <input type="date" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>"
                   class="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all">
        </div>
        <div class="flex items-end gap-3">
            <button type="submit" class="medical-btn-primary px-6 py-3">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Filter Earnings
            </button>
            <button type="button" onclick="setCurrentMonth()" class="medical-btn-secondary px-6 py-3">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Current Month
            </button>
        </div>
    </form>
</div>

<!-- Medical Earnings Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Medical Commission -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-2xl flex items-center justify-center">
                <svg class="h-8 w-8 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Medical Commission Earned</dt>
                    <dd class="text-3xl font-bold text-redolence-green"><?= formatCurrency($totalEarnings) ?></dd>
                    <dd class="text-xs text-redolence-green font-medium">Professional Earnings</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Medical Revenue -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-2xl flex items-center justify-center">
                <svg class="h-8 w-8 text-redolence-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Medical Revenue Generated</dt>
                    <dd class="text-3xl font-bold text-redolence-blue"><?= formatCurrency($totalRevenue) ?></dd>
                    <dd class="text-xs text-redolence-blue font-medium">Total Treatment Revenue</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Medical Treatments Completed -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-2xl flex items-center justify-center">
                <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Medical Treatments Completed</dt>
                    <dd class="text-3xl font-bold text-purple-600"><?= $totalAppointments ?></dd>
                    <dd class="text-xs text-purple-600 font-medium">Patient Sessions</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Average Medical Commission -->
    <div class="medical-card p-6">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-gradient-to-br from-orange-500/20 to-orange-500/10 rounded-2xl flex items-center justify-center">
                <svg class="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-medium text-gray-600 truncate">Average Medical Commission</dt>
                    <dd class="text-3xl font-bold text-orange-600"><?= $totalAppointments > 0 ? formatCurrency($totalEarnings / $totalAppointments) : formatCurrency(0) ?></dd>
                    <dd class="text-xs text-orange-600 font-medium">Per Treatment Session</dd>
                </dl>
            </div>
        </div>
    </div>
</div>

<!-- Medical Performance Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Medical Monthly Performance -->
    <div class="medical-card p-8">
        <h3 class="text-2xl font-bold text-redolence-navy mb-6">Monthly Medical Performance</h3>
        <div class="space-y-6">
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                <span class="text-gray-700 font-medium">Total Patient Appointments</span>
                <span class="text-redolence-navy font-bold text-lg"><?= $monthlyStats['totalAppointments'] ?></span>
            </div>
            <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                <span class="text-gray-700 font-medium">Treatments Completed</span>
                <span class="text-green-700 font-bold text-lg"><?= $monthlyStats['completedAppointments'] ?></span>
            </div>
            <div class="flex items-center justify-between p-4 bg-red-50 rounded-xl">
                <span class="text-gray-700 font-medium">Cancelled Appointments</span>
                <span class="text-red-700 font-bold text-lg"><?= $monthlyStats['cancelledAppointments'] ?></span>
            </div>
            <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-xl">
                <span class="text-gray-700 font-medium">Patient No Shows</span>
                <span class="text-yellow-700 font-bold text-lg"><?= $monthlyStats['noShowAppointments'] ?></span>
            </div>
            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-redolence-green/10 to-redolence-green/5 rounded-xl border-t-4 border-redolence-green">
                <span class="text-redolence-navy font-semibold">Medical Completion Rate</span>
                <span class="text-redolence-green font-bold text-xl"><?= $monthlyStats['completionRate'] ?>%</span>
            </div>
            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-redolence-blue/10 to-redolence-blue/5 rounded-xl border-t-4 border-redolence-blue">
                <span class="text-redolence-navy font-semibold">Monthly Medical Commission</span>
                <span class="text-redolence-blue font-bold text-xl"><?= formatCurrency($monthlyStats['commissionEarned']) ?></span>
            </div>
        </div>
    </div>

    <!-- Medical Overall Performance -->
    <div class="medical-card p-8">
        <h3 class="text-2xl font-bold text-redolence-navy mb-6">Overall Medical Performance</h3>
        <div class="space-y-6">
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                <span class="text-gray-700 font-medium">Total Medical Bookings</span>
                <span class="text-redolence-navy font-bold text-lg"><?= $performanceMetrics['totalBookings'] ?></span>
            </div>
            <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                <span class="text-gray-700 font-medium">Completed Medical Treatments</span>
                <span class="text-green-700 font-bold text-lg"><?= $performanceMetrics['completedBookings'] ?></span>
            </div>
            <div class="flex items-center justify-between p-4 bg-blue-50 rounded-xl">
                <span class="text-gray-700 font-medium">Overall Medical Completion Rate</span>
                <span class="text-blue-700 font-bold text-lg"><?= $performanceMetrics['completionRate'] ?>%</span>
            </div>
            <div class="flex items-center justify-between p-4 bg-purple-50 rounded-xl">
                <span class="text-gray-700 font-medium">Average Treatment Value</span>
                <span class="text-purple-700 font-bold text-lg"><?= formatCurrency($performanceMetrics['avgServiceValue']) ?></span>
            </div>
            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-redolence-green/10 to-redolence-green/5 rounded-xl border-t-4 border-redolence-green">
                <span class="text-redolence-navy font-semibold">Total Medical Revenue Generated</span>
                <span class="text-redolence-green font-bold text-xl"><?= formatCurrency($performanceMetrics['totalRevenue']) ?></span>
            </div>
            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-redolence-blue/10 to-redolence-blue/5 rounded-xl border-t-4 border-redolence-blue">
                <span class="text-redolence-navy font-semibold">Monthly Medical Growth</span>
                <span class="font-bold text-xl <?= $performanceMetrics['monthlyGrowth'] >= 0 ? 'text-green-600' : 'text-red-600' ?>">
                    <?= $performanceMetrics['monthlyGrowth'] >= 0 ? '+' : '' ?><?= number_format($performanceMetrics['monthlyGrowth'], 1) ?>%
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Medical Daily Earnings Breakdown -->
<div class="medical-card p-8">
    <h3 class="text-2xl font-bold text-redolence-navy mb-6">Daily Medical Earnings Breakdown</h3>

    <?php if (empty($earnings)): ?>
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <h4 class="text-2xl font-bold text-redolence-navy mb-3">No Medical Earnings Data</h4>
            <p class="text-gray-600">No completed medical treatments found for the selected period</p>
        </div>
    <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10">
                        <th class="px-6 py-4 text-left text-sm font-bold text-redolence-navy uppercase tracking-wider">Treatment Date</th>
                        <th class="px-6 py-4 text-left text-sm font-bold text-redolence-navy uppercase tracking-wider">Patient Appointments</th>
                        <th class="px-6 py-4 text-left text-sm font-bold text-redolence-navy uppercase tracking-wider">Treatments Completed</th>
                        <th class="px-6 py-4 text-left text-sm font-bold text-redolence-navy uppercase tracking-wider">Medical Revenue</th>
                        <th class="px-6 py-4 text-left text-sm font-bold text-redolence-navy uppercase tracking-wider">Medical Commission</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php foreach ($earnings as $earning): ?>
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-redolence-navy">
                                <div class="font-semibold"><?= date('M j, Y', strtotime($earning['date'])) ?></div>
                                <div class="text-xs text-gray-500"><?= date('l', strtotime($earning['date'])) ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-redolence-navy font-medium">
                                <?= $earning['appointments'] ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-redolence-navy">
                                <div class="font-semibold"><?= $earning['completed'] ?></div>
                                <?php if ($earning['appointments'] > 0): ?>
                                    <div class="text-xs text-redolence-green font-medium">
                                        <?= round(($earning['completed'] / $earning['appointments']) * 100, 1) ?>% completion rate
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-redolence-blue">
                                <?= formatCurrency($earning['revenue']) ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-redolence-green">
                                <?= formatCurrency($earning['commission']) ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-redolence-navy">Medical Period Total</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-redolence-navy">
                            <?= array_sum(array_column($earnings, 'appointments')) ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-redolence-navy">
                            <?= $totalAppointments ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-redolence-blue">
                            <?= formatCurrency($totalRevenue) ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-redolence-green">
                            <?= formatCurrency($totalEarnings) ?>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    <?php endif; ?>
</div>
    <script>
        function setCurrentMonth() {
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

            document.getElementById('start_date').value = firstDay.toISOString().split('T')[0];
            document.getElementById('end_date').value = lastDay.toISOString().split('T')[0];
        }
    </script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
