<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME . ' Admin' : APP_NAME . ' - Medical Admin Panel' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Medical admin panel for Redolence Medi Aesthetics - Advanced medical beauty management system' ?>">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= getBasePath() ?>/includes/redolence_logo.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'redolence-green': '#49a75c',
                        'redolence-blue': '#5894d2',
                        'redolence-gold': '#f4d03f',
                        'redolence-navy': '#1a2332',
                        'redolence-gray': '#f8fafc',
                        primary: {
                            50: '#f0f9f3',
                            100: '#dcf2e3',
                            200: '#bce5cb',
                            300: '#8dd2a8',
                            400: '#5ab67e',
                            500: '#49a75c',
                            600: '#3a8549',
                            700: '#316a3c',
                            800: '#2b5533',
                            900: '#25462c',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        medical: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#5894d2',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'pulse-medical': 'pulseMedical 2s ease-in-out infinite',
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                        pulseMedical: {
                            '0%, 100%': { boxShadow: '0 0 20px rgba(73, 167, 92, 0.3)' },
                            '50%': { boxShadow: '0 0 30px rgba(88, 148, 210, 0.5)' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                    },
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --background: #ffffff;
            --foreground: #1a2332;
            --primary-green: #49a75c;
            --primary-blue: #5894d2;
            --accent-gold: #f4d03f;
            --deep-navy: #1a2332;
            --soft-gray: #f8fafc;
            --medical-white: #ffffff;
            --shadow-primary: rgba(73, 167, 92, 0.15);
            --shadow-blue: rgba(88, 148, 210, 0.15);
            --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
            --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--soft-gray);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-blue);
        }

        /* Selection */
        ::selection {
            background: var(--primary-green);
            color: var(--medical-white);
        }

        /* Medical glass effect for headers and cards */
        .medical-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(73, 167, 92, 0.1);
        }

        /* Medical hover effects */
        .medical-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .medical-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow-primary);
        }

        /* Medical admin specific styles */
        .admin-page {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f0f9f3 100%);
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(to right, #f7fafc, #edf2f7);
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 0.75rem 1rem;
        }

        .admin-sidebar {
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(15px);
            border-right: 2px solid rgba(73, 167, 92, 0.1);
        }

        /* Medical navigation styles */
        .nav-item-active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 15px var(--shadow-primary);
        }

        .nav-item-inactive {
            color: var(--deep-navy);
            border: 1px solid transparent;
        }

        .nav-item-inactive:hover {
            background: var(--gradient-soft);
            color: var(--primary-green);
            border-color: rgba(73, 167, 92, 0.2);
        }

        /* Medical notification styles */
        .notification-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            animation: pulse-medical 2s ease-in-out infinite;
        }

        /* Comprehensive Medical Button System */
        .medical-btn-primary {
            background: linear-gradient(135deg, #49a75c, #2d6a3e);
            color: white;
            border: none;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
            color: white;
            text-decoration: none;
        }

        .medical-btn-secondary {
            background: rgba(255, 255, 255, 0.95);
            color: #49a75c;
            border: 2px solid #49a75c;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-secondary:hover {
            background: #49a75c;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
            text-decoration: none;
        }

        .medical-btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border: none;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Medical Card System */
        .medical-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            position: relative;
            overflow: hidden;
        }

        .medical-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 3px;
            background: var(--gradient-primary);
            transition: left 0.6s ease;
        }

        .medical-card:hover::before {
            left: 100%;
        }

        .medical-card:hover {
            transform: translateY(-2px);
            border-color: rgba(73, 167, 92, 0.2);
            box-shadow: 0 15px 35px var(--shadow-primary);
        }

        .medical-filter-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 20px;
        }

        .medical-content-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
            border: 1px solid rgba(73, 167, 92, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .medical-content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .medical-content-card:hover::before {
            transform: scaleX(1);
        }

        .medical-content-card:hover {
            transform: translateY(-3px);
            border-color: rgba(73, 167, 92, 0.2);
            box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
        }

        /* Medical Form System */
        .medical-form-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            color: var(--deep-navy);
            font-weight: 500;
        }

        .medical-form-input:focus {
            outline: none;
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
            background: white;
        }

        .medical-form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--primary-green);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Medical Message System */
        .medical-message-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
            border: 2px solid rgba(16, 185, 129, 0.2);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            color: #065f46;
            font-weight: 600;
        }

        .medical-message-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
            border: 2px solid rgba(239, 68, 68, 0.2);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            color: #7f1d1d;
            font-weight: 600;
        }

        /* Clean Modern Design System */
        .clean-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .clean-sidebar {
            background: rgba(255, 255, 255, 0.98);
            border-right: 1px solid rgba(0, 0, 0, 0.06);
        }

        .clean-card {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            transition: all 0.15s ease;
        }

        .clean-card:hover {
            border-color: rgba(73, 167, 92, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .clean-btn {
            background: #49a75c;
            color: white;
            border: none;
            padding: 0.625rem 1.25rem;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.15s ease;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .clean-btn:hover {
            background: #3d8a4d;
            color: white;
            text-decoration: none;
        }

        .clean-btn-ghost {
            background: transparent;
            color: #6b7280;
            border: 1px solid rgba(0, 0, 0, 0.08);
            padding: 0.625rem 1.25rem;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.15s ease;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .clean-btn-ghost:hover {
            background: #f9fafb;
            color: #374151;
            text-decoration: none;
        }

        .clean-nav-item {
            color: #6b7280;
            border-radius: 6px;
            padding: 0.75rem 1rem;
            transition: all 0.15s ease;
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .clean-nav-item:hover {
            background: rgba(73, 167, 92, 0.06);
            color: #49a75c;
            text-decoration: none;
        }

        .clean-nav-item.active {
            background: #49a75c;
            color: white;
        }

        .clean-stat-card {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            padding: 1.25rem;
            transition: all 0.15s ease;
        }

        .clean-stat-card:hover {
            border-color: rgba(73, 167, 92, 0.12);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .clean-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: currentColor;
        }

        .clean-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #49a75c;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.75rem;
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen admin-page">
    <!-- Clean Professional Header -->
    <header class="admin-header fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <!-- Left Section: Logo and Menu Toggle -->
            <div class="flex items-center space-x-4">
                <a href="/" class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-redolence-blue rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <span class="text-xl font-semibold text-gray-800 hidden md:inline">Redolence Medical</span>
                </a>
                <button id="mobile-menu-toggle" class="lg:hidden text-gray-600 hover:text-gray-900 p-2 rounded-full hover:bg-gray-200/30" aria-label="Toggle Menu">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M2.5 6.5A1.5 1.5 0 014 5h12a1.5 1.5 0 010 3H4a1.5 1.5 0 01-1.5-1.5zm0 5A1.5 1.5 0 014 10h12a1.5 1.5 0 010 3H4a1.5 1.5 0 01-1.5-1.5zm0-3H7a1 1 0 011-1h3v2h-4V7z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
            <!-- Right Section: User and Notifications -->
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <button id="notificationButton" onclick="toggleNotifications()" class="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-200/30 relative" aria-label="Notifications">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14v2a1 1 0 001 1h10a1 1 0 001-1v-2a1 1 0 00.707-1.707L16 10.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                        </svg>
                        <span id="notificationCounter" class="absolute top-1 right-1 w-5 h-5 rounded-full bg-red-500 text-white text-xs font-medium flex items-center justify-center hidden">0</span>
                    </button>
                    <!-- Notification Dropdown (unchanged backend logic) -->
                    <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white shadow-lg rounded-2xl z-50 max-h-96 overflow-hidden border border-gray-200/50">
                        <div class="px-4 py-3 border-b border-gray-200/50">
                            <div class="flex items-center justify-between">
                                <h3 class="text-base font-medium text-gray-800">Notifications</h3>
                                <div class="flex items-center space-x-2">
                                    <button onclick="markAllAsRead()" class="text-sm text-gray-500 hover:text-gray-700">Mark all read</button>
                                    <button onclick="openNotificationsPage()" class="text-sm text-redolence-blue hover:text-redolence-blue/80">View all</button>
                                </div>
                            </div>
                        </div>
                        <div class="px-4 py-2 border-b border-redolence-blue/10">
                            <div class="flex space-x-2 text-sm overflow-x-auto">
                                <button onclick="filterNotifications('all')" class="notification-tab active px-3 py-1.5 rounded-lg bg-redolence-blue text-white font-medium min-w-fit">All <span id="count-all">0</span></button>
                                <button onclick="filterNotifications('BOOKING')" class="notification-tab px-3 py-1.5 rounded-lg text-gray-600 hover:text-redolence-blue transition-colors min-w-fit">Appointments <span id="count-BOOKING">0</span></button>
                                <button onclick="filterNotifications('CUSTOMER')" class="notification-tab px-3 py-1.5 rounded-lg text-gray-600 hover:text-redolence-blue transition-colors min-w-fit">Patients <span id="count-CUSTOMER">0</span></button>
                                <button onclick="filterNotifications('SYSTEM')" class="notification-tab px-3 py-1.5 rounded-lg text-gray-600 hover:text-redolence-blue transition-colors min-w-fit">System <span id="count-SYSTEM">0</span></button>
                            </div>
                        </div>
                        <div id="notificationsList" class="max-h-64 overflow-y-auto">
                            <div class="p-4 text-center text-gray-500">
                                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-redolence-blue mx-auto mb-2"></div>
                                Loading medical notifications...
                            </div>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <button id="user-menu-toggle" class="flex items-center space-x-3 p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-200/30" aria-label="User Menu">
                        <div class="w-9 h-9 bg-redolence-blue rounded-full flex items-center justify-center text-white font-medium text-base">
                            <?= strtoupper(substr(getCurrentUser()['name'], 0, 2)) ?>
                        </div>
                        <span class="hidden md:inline-block text-sm font-medium text-gray-800"><?= htmlspecialchars(getCurrentUser()['name']) ?></span>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <!-- User Menu Dropdown (unchanged backend logic) -->
                    <div id="user-menu" class="hidden absolute right-0 mt-2 w-52 bg-white shadow-lg rounded-2xl z-50 border border-gray-200/50 overflow-hidden">
                        <?php $basePath = getBasePath(); ?>
                        <div class="px-4 py-3 border-b border-gray-200/50">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-redolence-blue rounded-full flex items-center justify-center text-white text-sm font-medium">
                                    <?= strtoupper(substr(getCurrentUser()['name'], 0, 2)) ?>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars(getCurrentUser()['name']) ?></p>
                                    <p class="text-xs text-gray-500">Administrator</p>
                                </div>
                            </div>
                        </div>
                        <div class="py-2">
                            <a href="<?= $basePath ?>/admin/profile" class="block px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                <svg class="w-5 h-5 mr-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9c0-1.1.9-2 2-2h10a2 2 0 012 2v1a1 1 0 01-2 0v-1H5v1a1 1 0 11-2 0v-1z" clip-rule="evenodd"/>
                                </svg>
                                Profile
                            </a>
                            <a href="<?= $basePath ?>/admin/settings" class="block px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                <svg class="w-5 h-5 mr-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-1.6-2.72-3.17-3.06a17.2 17.2 0 00-2.26-.11c-.57 0-1.14.04-1.7.11a3.52 3.52 0 00-3.1 2.52 17.48 17.48 0 00-.16 2.26c0 .58.05 1.15.16 1.71.37 1.58 1.59 2.74 3.17 3.07a17.36 17.36 0 002.26.11 17.7 17.7 0 001.7-.11 3.52 3.52 0 003.1-2.52c.11-.56.16-1.13.16-1.71 0-.58-.05-1.15-.16-1.71A3.48 3.48 0 0011.49 3.17zM6.24 5.66a2 2 0 012.7-1.88 15.5 15.5 0 011.82.1 2 2 0 011.88 2.7 15.37 15.37 0 01-.1 1.82 2 2 0 01-2.7 1.88 15.5 15.5 0 01-1.82-.1 2 2 0 01-1.88-2.7 15.37 15.37 0 01.1-1.82zM10 12.83a14.21 14.21 0 01-1.82.11 14.21 14.21 0 01-1.71-.11 3.52 3.52 0 00-3.1 2.52 17.48 17.48 0 00-.16 2.26c0 .58.05 1.15.16 1.71.37 1.58 1.59 2.74 3.17 3.07a17.36 17.36 0 002.26.11c.58 0 1.14-.04 1.7-.11 1.58-.33 2.8-1.49 3.17-3.06 1.02-4.43.46-9.05-1.94-12.85a13.88 13.88 0 00-1.73-2.6 15.7 15.7 0 00-2.08-.26c-.58 0-1.15.05-1.71.16-1.58.37-2.74 1.59-3.07 3.17a17.36 17.36 0 00-.11 2.26c0 .57.04 1.14.11 1.7a3.52 3.52 0 003.06 3.1 17.2 17.2 0 002.26.16 17.7 17.7 0 001.71-.16 3.52 3.52 0 003.1-3.06c.07-.56.11-1.13.11-1.7 0-.58-.04-1.15-.11-1.71a3.52 3.52 0 00-3.06-3.1 17.2 17.2 0 00-2.26-.11z" clip-rule="evenodd"/>
                                </svg>
                                Settings
                            </a>
                            <div class="border-t border-gray-200/50 my-1.5"></div>
                            <a href="<?= $basePath ?>/" class="block px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                <svg class="w-5 h-5 mr-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 3H6a2 2 0 00-2 2v14a2 2 0 002 2h8a2 2 0 002-2V9l-5-5zm3 11H7a1 1 0 010-2h6a1 1 0 010 2zm0-3H7a1 1 0 010-2h6a1 1 0 010 2zM9 7a1 1 0 011-1h3v2h-4V7z" clip-rule="evenodd"/>
                                </svg>
                                View Website
                            </a>
                            <a href="<?= $basePath ?>/auth/logout.php" class="block px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 hover:text-red-800 flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 10l1.293 1.293a1 1 0 01-1.414 1.414l-2-2a1 1 0 010-1.414l2-2a1 1 0 011.414 0zM16 10a6 6 0 11-12 0 6 6 0 0112 0z" clip-rule="evenodd"/>
                                </svg>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-black opacity-50"></div>
    </div>

    <!-- Mobile Medical Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-64 admin-sidebar lg:hidden">
        <div class="flex items-center justify-between p-4 border-b border-redolence-green/10">
            <h2 class="text-lg font-semibold text-redolence-green">Medical Navigation</h2>
            <button id="mobile-sidebar-close" class="p-2 rounded-md text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="mt-4">
            <?php include __DIR__ . '/admin_sidebar_nav.php'; ?>
        </nav>
    </div>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.remove('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.remove('hidden');
        });

        document.getElementById('mobile-sidebar-close').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        document.getElementById('mobile-sidebar-overlay').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        // User menu toggle
        document.getElementById('user-menu-toggle').addEventListener('click', function() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('hidden');
        });

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userMenu = document.getElementById('user-menu');
            
            if (!userMenuToggle.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>