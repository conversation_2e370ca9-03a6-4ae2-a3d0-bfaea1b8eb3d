<?php
/**
 * Room Management Functions
 * Redolence Medi Aesthetics - Room Management System
 */

require_once __DIR__ . '/../config/database.php';

/**
 * Get all rooms with optional filters
 */
function getAllRooms($filters = []) {
    global $database;
    
    $whereConditions = [];
    $params = [];
    
    if (!empty($filters['status'])) {
        $whereConditions[] = "status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['type'])) {
        $whereConditions[] = "type = ?";
        $params[] = $filters['type'];
    }
    
    if (!empty($filters['search'])) {
        $whereConditions[] = "(name LIKE ? OR description LIKE ?)";
        $searchTerm = '%' . $filters['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    $sql = "SELECT * FROM rooms $whereClause ORDER BY type, name";
    
    return $database->fetchAll($sql, $params);
}

/**
 * Get room by ID
 */
function getRoomById($roomId) {
    global $database;
    
    return $database->fetch("SELECT * FROM rooms WHERE id = ?", [$roomId]);
}

/**
 * Create new room
 */
function createRoom($data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name']) || empty($data['type'])) {
            return ['success' => false, 'error' => 'Room name and type are required'];
        }
        
        // Check if room name already exists
        $existing = $database->fetch("SELECT id FROM rooms WHERE name = ?", [$data['name']]);
        if ($existing) {
            return ['success' => false, 'error' => 'Room name already exists'];
        }
        
        $roomId = generateUUID();
        
        $sql = "INSERT INTO rooms (id, name, type, capacity, description, status) VALUES (?, ?, ?, ?, ?, ?)";
        $params = [
            $roomId,
            $data['name'],
            $data['type'],
            $data['capacity'] ?? 1,
            $data['description'] ?? '',
            $data['status'] ?? 'AVAILABLE'
        ];
        
        $database->execute($sql, $params);
        
        return ['success' => true, 'room_id' => $roomId];
        
    } catch (Exception $e) {
        error_log("Error creating room: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create room'];
    }
}

/**
 * Update room
 */
function updateRoom($roomId, $data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name']) || empty($data['type'])) {
            return ['success' => false, 'error' => 'Room name and type are required'];
        }
        
        // Check if room exists
        $room = getRoomById($roomId);
        if (!$room) {
            return ['success' => false, 'error' => 'Room not found'];
        }
        
        // Check if room name already exists (excluding current room)
        $existing = $database->fetch("SELECT id FROM rooms WHERE name = ? AND id != ?", [$data['name'], $roomId]);
        if ($existing) {
            return ['success' => false, 'error' => 'Room name already exists'];
        }
        
        $sql = "UPDATE rooms SET name = ?, type = ?, capacity = ?, description = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $params = [
            $data['name'],
            $data['type'],
            $data['capacity'] ?? 1,
            $data['description'] ?? '',
            $data['status'] ?? 'AVAILABLE',
            $roomId
        ];
        
        $database->execute($sql, $params);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Error updating room: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update room'];
    }
}

/**
 * Delete room (only if no active or future bookings)
 */
function deleteRoom($roomId) {
    global $database;
    
    try {
        // Check if room exists
        $room = getRoomById($roomId);
        if (!$room) {
            return ['success' => false, 'error' => 'Room not found'];
        }
        
        // Check for active or future bookings
        $activeBookings = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings 
             WHERE room_id = ? 
             AND (status IN ('CONFIRMED', 'IN_PROGRESS') OR (date >= CURDATE() AND status = 'PENDING'))",
            [$roomId]
        );
        
        if ($activeBookings['count'] > 0) {
            return [
                'success' => false, 
                'error' => 'Cannot delete room with active or future bookings',
                'booking_count' => $activeBookings['count']
            ];
        }
        
        // Delete the room
        $database->execute("DELETE FROM rooms WHERE id = ?", [$roomId]);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Error deleting room: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to delete room'];
    }
}

/**
 * Check room availability for a specific date and time
 */
function checkRoomAvailability($roomId, $date, $startTime, $endTime, $excludeBookingId = null) {
    global $database;
    
    try {
        // First check if room exists and is available
        $room = getRoomById($roomId);
        if (!$room || $room['status'] !== 'AVAILABLE') {
            return false;
        }
        
        // Check for overlapping bookings (including PENDING with room assignments)
        $sql = "SELECT COUNT(*) as count FROM bookings
                WHERE room_id = ?
                AND date = ?
                AND status IN ('PENDING', 'CONFIRMED', 'IN_PROGRESS')
                AND (start_time < ? AND end_time > ?)";

        $params = [$roomId, $date, $endTime, $startTime];

        if ($excludeBookingId) {
            $sql .= " AND id != ?";
            $params[] = $excludeBookingId;
        }

        $result = $database->fetch($sql, $params);

        // Debug logging
        error_log("Room availability check - Room: $roomId, Date: $date, Time: $startTime-$endTime, Conflicts: {$result['count']}, Exclude: " . ($excludeBookingId ?? 'none'));

        return $result['count'] == 0;
        
    } catch (Exception $e) {
        error_log("Error checking room availability: " . $e->getMessage());
        return false;
    }
}

/**
 * Get available rooms for a specific date and time
 */
function getAvailableRooms($date, $startTime, $endTime, $roomType = null, $excludeBookingId = null) {
    global $database;
    
    try {
        $sql = "SELECT r.* FROM rooms r 
                WHERE r.status = 'AVAILABLE'";
        $params = [];
        
        if ($roomType) {
            $sql .= " AND r.type = ?";
            $params[] = $roomType;
        }
        
        $sql .= " AND r.id NOT IN (
                    SELECT DISTINCT b.room_id FROM bookings b
                    WHERE b.room_id IS NOT NULL
                    AND b.date = ?
                    AND b.status IN ('PENDING', 'CONFIRMED', 'IN_PROGRESS')
                    AND (b.start_time < ? AND b.end_time > ?)";
        
        $params[] = $date;
        $params[] = $endTime;
        $params[] = $startTime;
        
        if ($excludeBookingId) {
            $sql .= " AND b.id != ?";
            $params[] = $excludeBookingId;
        }
        
        $sql .= ") ORDER BY r.type, r.name";
        
        return $database->fetchAll($sql, $params);
        
    } catch (Exception $e) {
        error_log("Error getting available rooms: " . $e->getMessage());
        return [];
    }
}

/**
 * Assign room to booking
 */
function assignRoomToBooking($bookingId, $roomId) {
    global $database;

    try {
        // Start transaction for atomic operation
        $database->beginTransaction();

        // Get booking details
        $booking = $database->fetch("SELECT * FROM bookings WHERE id = ?", [$bookingId]);
        if (!$booking) {
            $database->rollback();
            return ['success' => false, 'error' => 'Booking not found'];
        }

        // Get room details
        $room = getRoomById($roomId);
        if (!$room) {
            $database->rollback();
            return ['success' => false, 'error' => 'Room not found'];
        }

        if ($room['status'] !== 'AVAILABLE') {
            $database->rollback();
            return ['success' => false, 'error' => 'Room is currently ' . strtolower($room['status'])];
        }

        // Double-check for conflicts with a more detailed query
        $conflictCheck = $database->fetch("
            SELECT b.id, b.status, u.name as customer_name
            FROM bookings b
            LEFT JOIN users u ON b.user_id = u.id
            WHERE b.room_id = ?
            AND b.date = ?
            AND b.status IN ('PENDING', 'CONFIRMED', 'IN_PROGRESS')
            AND (b.start_time < ? AND b.end_time > ?)
            AND b.id != ?
            LIMIT 1
        ", [$roomId, $booking['date'], $booking['end_time'], $booking['start_time'], $bookingId]);

        if ($conflictCheck) {
            $database->rollback();
            $conflictTime = date('g:i A', strtotime($booking['start_time'])) . ' - ' . date('g:i A', strtotime($booking['end_time']));
            return [
                'success' => false,
                'error' => "Room '{$room['name']}' is already assigned to {$conflictCheck['customer_name']} on " . date('M j, Y', strtotime($booking['date'])) . " at {$conflictTime}. Status: {$conflictCheck['status']}"
            ];
        }

        // Assign room to booking
        $database->execute("UPDATE bookings SET room_id = ?, updated_at = NOW() WHERE id = ?", [$roomId, $bookingId]);

        $database->commit();

        return ['success' => true, 'message' => "Room '{$room['name']}' assigned successfully"];

    } catch (Exception $e) {
        if ($database->getConnection()->inTransaction()) {
            $database->rollback();
        }
        error_log("Error assigning room to booking: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to assign room: ' . $e->getMessage()];
    }
}

/**
 * Unassign room from booking
 */
function unassignRoomFromBooking($bookingId) {
    global $database;

    try {
        // Start transaction for atomic operation
        $database->beginTransaction();

        // Get booking details
        $booking = $database->fetch("SELECT * FROM bookings WHERE id = ?", [$bookingId]);
        if (!$booking) {
            $database->rollback();
            return ['success' => false, 'error' => 'Booking not found'];
        }

        if (!$booking['room_id']) {
            $database->rollback();
            return ['success' => false, 'error' => 'No room is currently assigned to this booking'];
        }

        // Get room details for response message
        $room = getRoomById($booking['room_id']);
        $roomName = $room ? $room['name'] : 'Unknown Room';

        // Check if booking is confirmed - warn but allow unassignment
        $warningMessage = '';
        if ($booking['status'] === 'CONFIRMED') {
            $warningMessage = ' Warning: This booking was already confirmed. Customer may need to be notified.';
        }

        // Unassign room from booking
        $database->execute("UPDATE bookings SET room_id = NULL, updated_at = NOW() WHERE id = ?", [$bookingId]);

        $database->commit();

        return [
            'success' => true,
            'message' => "Room '{$roomName}' unassigned successfully.{$warningMessage}"
        ];

    } catch (Exception $e) {
        if ($database->getConnection()->inTransaction()) {
            $database->rollback();
        }
        error_log("Error unassigning room from booking: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to unassign room: ' . $e->getMessage()];
    }
}

/**
 * Validate room assignment before booking confirmation
 */
function validateRoomAssignment($bookingId) {
    global $database;

    try {
        $booking = $database->fetch("SELECT * FROM bookings WHERE id = ?", [$bookingId]);
        if (!$booking) {
            return ['success' => false, 'error' => 'Booking not found'];
        }

        if (!$booking['room_id']) {
            return ['success' => false, 'error' => 'Room must be assigned before confirmation'];
        }

        // Get room details
        $room = getRoomById($booking['room_id']);
        if (!$room) {
            return ['success' => false, 'error' => 'Assigned room no longer exists'];
        }

        if ($room['status'] !== 'AVAILABLE') {
            return ['success' => false, 'error' => "Assigned room is currently {$room['status']}. Please assign a different room."];
        }

        // Re-check availability at confirmation time with detailed conflict info
        $conflictCheck = $database->fetch("
            SELECT b.id, b.status, u.name as customer_name, b.start_time, b.end_time
            FROM bookings b
            LEFT JOIN users u ON b.user_id = u.id
            WHERE b.room_id = ?
            AND b.date = ?
            AND b.status IN ('PENDING', 'CONFIRMED', 'IN_PROGRESS')
            AND (b.start_time < ? AND b.end_time > ?)
            AND b.id != ?
            LIMIT 1
        ", [$booking['room_id'], $booking['date'], $booking['end_time'], $booking['start_time'], $bookingId]);

        if ($conflictCheck) {
            $conflictTime = date('g:i A', strtotime($conflictCheck['start_time'])) . ' - ' . date('g:i A', strtotime($conflictCheck['end_time']));
            return [
                'success' => false,
                'error' => "Room '{$room['name']}' is already assigned to {$conflictCheck['customer_name']} at {$conflictTime}. Please assign a different room."
            ];
        }

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Error validating room assignment: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to validate room assignment: ' . $e->getMessage()];
    }
}

/**
 * Get room types
 */
function getRoomTypes() {
    return [
        'TREATMENT' => 'Treatment Room',
        'CONSULTATION' => 'Consultation Room',
        'PROCEDURE' => 'Procedure Room',
        'RECOVERY' => 'Recovery Room',
        'VIP' => 'VIP Suite'
    ];
}

/**
 * Get room statuses
 */
function getRoomStatuses() {
    return [
        'AVAILABLE' => 'Available',
        'UNAVAILABLE' => 'Unavailable',
        'MAINTENANCE' => 'Under Maintenance'
    ];
}

/**
 * Get room usage statistics
 */
function getRoomUsageStats($roomId, $startDate = null, $endDate = null) {
    global $database;
    
    try {
        $sql = "SELECT 
                    COUNT(*) as total_bookings,
                    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_bookings,
                    COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_bookings,
                    AVG(TIMESTAMPDIFF(MINUTE, start_time, end_time)) as avg_duration_minutes
                FROM bookings 
                WHERE room_id = ?";
        
        $params = [$roomId];
        
        if ($startDate) {
            $sql .= " AND date >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $sql .= " AND date <= ?";
            $params[] = $endDate;
        }
        
        return $database->fetch($sql, $params);
        
    } catch (Exception $e) {
        error_log("Error getting room usage stats: " . $e->getMessage());
        return null;
    }
}
