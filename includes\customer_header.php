<?php
// Include customer panel functions
require_once __DIR__ . '/customer_panel_functions.php';

// Get customer data for navigation
if (!isset($dashboardData)) {
    $customerId = $_SESSION['user_id'];
    $customerProfile = getCustomerProfile($customerId);
    $customerPoints = getCustomerPointsData($customerId);
} else {
    $customerProfile = $dashboardData['profile'];
    $customerPoints = $dashboardData['pointsData'];
}

// Set current page for navigation highlighting
$currentPath = $_SERVER['REQUEST_URI'];
?>

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - Customer Portal' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Customer portal for Redolence Medi Aesthetics - Advanced medical aesthetics and beauty treatments' ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= getBasePath() ?>/includes/redolence_logo.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9f4',
                            100: '#dcf4e6',
                            200: '#bce8d1',
                            300: '#8dd5b3',
                            400: '#5cb85c',
                            500: '#49a75c',
                            600: '#3d8b50',
                            700: '#336f42',
                            800: '#2d5a37',
                            900: '#264a2f',
                        },
                        secondary: {
                            50: '#f0f7ff',
                            100: '#e0efff',
                            200: '#b9dfff',
                            300: '#7cc8ff',
                            400: '#36b0ff',
                            500: '#5894d2',
                            600: '#4a7bc4',
                            700: '#3e65a3',
                            800: '#355485',
                            900: '#2d456d',
                        },
                        'redolence-green': '#49a75c',
                        'redolence-blue': '#5894d2',
                        'redolence-white': '#ffffff',
                        'green-light': '#5cb85c',
                        'green-dark': '#3d8b50',
                        'blue-light': '#7cc8ff',
                        'blue-dark': '#3e65a3',
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <style>
        :root {
            --background: #ffffff;
            --foreground: #000000;
            --primary-green: #49a75c;
            --primary-blue: #5894d2;
            --green-light: #5cb85c;
            --green-dark: #3d8b50;
            --blue-light: #7cc8ff;
            --blue-dark: #3e65a3;
            --gradient-primary: linear-gradient(135deg, #49a75c, #5894d2);
            --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
            --shadow-primary: rgba(73, 167, 92, 0.3);
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        /* Remove default list styling */
        ul, ol {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Modern Header Styles */
        .modern-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            z-index: 60; /* Higher than sidebar to prevent interference */
        }

        .header-logo {
            background: linear-gradient(135deg, #49a75c, #3d8b4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 1.5rem;
        }



        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #49a75c, #3d8b4e);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(73, 167, 92, 0.3);
        }

        .user-avatar::before {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(45deg, #49a75c, #2563eb, #49a75c);
            border-radius: 50%;
            z-index: -1;
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .dropdown-menu {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(226, 232, 240, 0.5);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            padding: 0.5rem;
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #374151;
            text-decoration: none;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
            color: #49a75c;
            transform: translateX(2px);
        }

        .mobile-menu-btn {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(226, 232, 240, 0.5);
            border-radius: 8px;
            padding: 0.5rem;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            background: rgba(73, 167, 92, 0.1);
            border-color: rgba(73, 167, 92, 0.3);
            transform: translateY(-1px);
        }

        .mobile-sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(226, 232, 240, 0.5);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gradient-primary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--green-dark), var(--blue-dark));
        }

        /* Enhanced Selection */
        ::selection {
            background: var(--gradient-primary);
            color: white;
        }

        /* Modern Glass Effect */
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(73, 167, 92, 0.1);
        }

        /* Enhanced Hover Effects */
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow-primary);
        }

        /* Medical Professional Button Styles */
        .btn-medical-primary {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-medical-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow-primary);
        }

        /* Notification Badge */
        .notification-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            position: absolute;
            top: -8px;
            right: -8px;
            min-width: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen customer-page">
    <!-- Modern Customer Header -->
    <header class="modern-header fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Mobile Menu -->
                <div class="flex items-center gap-4">
                    <button id="mobile-menu-toggle" class="lg:hidden mobile-menu-btn">
                        <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    <div>
                        <h1 class="header-logo">Redolence</h1>
                        <p class="text-sm text-gray-500 font-medium">Customer Portal</p>
                    </div>
                </div>

                <!-- Header Actions -->
                <div class="flex items-center gap-4">
                    <!-- User Profile Dropdown -->
                    <div class="relative">
                        <button id="user-menu-toggle" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-all duration-300">
                            <div class="user-avatar">
                                <?php if ($customerProfile['avatar']): ?>
                                    <img src="<?= htmlspecialchars($customerProfile['avatar']) ?>" alt="Profile" class="w-full h-full object-cover rounded-full" />
                                <?php else: ?>
                                    <span class="text-white font-semibold text-sm">
                                        <?= strtoupper(substr($customerProfile['name'], 0, 2)) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-semibold text-gray-900"><?= htmlspecialchars(explode(' ', $customerProfile['name'])[0]) ?></p>
                                <p class="text-xs text-gray-500">Customer</p>
                            </div>
                            <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-56 dropdown-menu z-50">
                            <?php $basePath = getBasePath(); ?>
                            <a href="<?= $basePath ?>/customer/profile" class="dropdown-item">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profile Settings
                            </a>
                            <a href="<?= $basePath ?>/customer/rewards" class="dropdown-item">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                                </svg>
                                Points & Rewards
                            </a>
                            <div class="border-t border-gray-200 my-2"></div>
                            <a href="<?= $basePath ?>/" class="dropdown-item">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                View Website
                            </a>
                            <a href="<?= $basePath ?>/auth/logout.php" class="dropdown-item text-red-600 hover:bg-red-50">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-black opacity-50"></div>
    </div>

    <!-- Mobile Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-80 mobile-sidebar lg:hidden">
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
                <h2 class="text-lg font-bold text-gray-900">Navigation</h2>
                <p class="text-sm text-gray-500">Customer Portal</p>
            </div>
            <button id="mobile-sidebar-close" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        

        
        <nav class="p-6">
            <?php include __DIR__ . '/customer_sidebar_nav.php'; ?>
        </nav>
    </div>

    <div class="min-h-screen pt-20">
        <!-- Fixed Sidebar -->
        <div class="hidden lg:block">
            <?php include __DIR__ . '/customer_sidebar.php'; ?>
        </div>

        <div class="py-6">
            <!-- Main content with flexible left margin to account for fixed sidebar -->
            <main class="lg:ml-64 xl:ml-72 2xl:ml-80 transition-all duration-300">
                <div class="px-4 sm:px-6 lg:px-8 max-w-none">
                    <!-- Mobile sidebar placeholder removed for better UX -->

<script>
// Enhanced header interactions
document.addEventListener('DOMContentLoaded', function() {
    // User menu toggle
    const userMenuToggle = document.getElementById('user-menu-toggle');
    const userMenu = document.getElementById('user-menu');
    
    if (userMenuToggle && userMenu) {
        userMenuToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!userMenuToggle.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.classList.add('hidden');
            }
        });
    }
    
    // Mobile menu toggle
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarClose = document.getElementById('mobile-sidebar-close');
    
    function openMobileSidebar() {
        mobileSidebar.classList.remove('hidden');
        mobileSidebarOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        // Animate in
        setTimeout(() => {
            mobileSidebar.style.transform = 'translateX(0)';
        }, 10);
    }
    
    function closeMobileSidebar() {
        mobileSidebar.style.transform = 'translateX(-100%)';
        document.body.style.overflow = 'auto';
        
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
            mobileSidebarOverlay.classList.add('hidden');
        }, 300);
    }
    
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', openMobileSidebar);
    }
    
    if (mobileSidebarClose) {
        mobileSidebarClose.addEventListener('click', closeMobileSidebar);
    }
    
    if (mobileSidebarOverlay) {
        mobileSidebarOverlay.addEventListener('click', closeMobileSidebar);
    }
    
    // Initialize mobile sidebar position
    if (mobileSidebar) {
        mobileSidebar.style.transform = 'translateX(-100%)';
        mobileSidebar.style.transition = 'transform 0.3s ease';
    }
    
    // Escape key to close mobile menu
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (!mobileSidebar.classList.contains('hidden')) {
                closeMobileSidebar();
            }
            if (!userMenu.classList.contains('hidden')) {
                userMenu.classList.add('hidden');
            }
        }
    });
    
    // Header scroll effect (optional)
    let lastScrollY = window.scrollY;
    const header = document.querySelector('.modern-header');
    
    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;
        
        if (currentScrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.12)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
        }
        
        lastScrollY = currentScrollY;
    }, { passive: true });
});
</script>