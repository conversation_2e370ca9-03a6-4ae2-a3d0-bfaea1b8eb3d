<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReport.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php');
}

// Get entry ID
$entryId = $_GET['id'] ?? '';
if (!$entryId) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Initialize handlers
$progressiveReport = new ProgressiveReport();
$progressiveReportEntry = new ProgressiveReportEntry();

// Get entry details
$entry = $progressiveReportEntry->getById($entryId);
if (!$entry) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Get report details
$report = $progressiveReport->getById($entry['report_id']);
if (!$report) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports');
    exit;
}

// Check if staff can edit this entry (only creator can edit)
$staffId = $_SESSION['user_id'];
if ($entry['created_by'] !== $staffId) {
    header('Location: ' . getBasePath() . '/staff/progressive-reports/view.php?id=' . $entry['report_id']);
    exit;
}

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $treatment = trim($_POST['treatment'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    $entryDate = $_POST['entry_date'] ?? '';
    
    // Validation
    if (empty($treatment)) {
        $errors[] = 'Medical treatment description is required.';
    }
    
    if (empty($notes)) {
        $errors[] = 'Medical progress notes are required.';
    }
    
    if (empty($entryDate)) {
        $errors[] = 'Treatment date is required.';
    } elseif (!strtotime($entryDate)) {
        $errors[] = 'Please enter a valid treatment date.';
    }
    
    // Update entry if no errors
    if (empty($errors)) {
        $updateData = [
            'treatment' => $treatment,
            'notes' => $notes,
            'entry_date' => $entryDate,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($progressiveReportEntry->update($entryId, $updateData)) {
            $success = true;
            // Refresh entry data
            $entry = $progressiveReportEntry->getById($entryId);
        } else {
            $errors[] = 'Failed to update medical progress entry. Please try again.';
        }
    }
}

$pageTitle = "Edit Medical Progress Entry - " . $report['client_name'];
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Medical Progress Entry Edit Back Navigation -->
<div class="mb-6">
    <a href="<?= getBasePath() ?>/staff/progressive-reports/view.php?id=<?= $entry['report_id'] ?>" 
       class="inline-flex items-center text-sm font-medium text-redolence-green hover:text-redolence-blue transition-colors focus:outline-none focus:ring-2 focus:ring-redolence-green focus:ring-offset-2 rounded-lg px-3 py-2">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        Back to Medical Progress Report
    </a>
</div>

<!-- Medical Progress Entry Edit Header -->
<div class="medical-card mb-8 p-8">
    <div class="flex items-center">
        <div class="flex-shrink-0 mr-6">
            <div class="w-16 h-16 bg-gradient-to-r from-redolence-green to-redolence-blue rounded-2xl flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
            </div>
        </div>
        <div>
            <h1 class="text-3xl font-bold text-redolence-navy">Edit Medical Progress Entry</h1>
            <p class="mt-2 text-lg text-gray-600">Patient: <span class="text-redolence-green font-semibold"><?= htmlspecialchars($report['client_name']) ?></span></p>
            <div class="mt-3 flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                HIPAA Compliant Medical Record Editing
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($success): ?>
    <div class="medical-card mb-8 p-6 bg-green-50 border-l-4 border-green-400">
        <div class="flex items-center">
            <svg class="w-6 h-6 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-green-800 font-medium">Medical progress entry updated successfully!</p>
        </div>
    </div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
    <div class="medical-card mb-8 p-6 bg-red-50 border-l-4 border-red-400">
        <div class="flex items-start">
            <svg class="w-6 h-6 text-red-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <h3 class="text-red-800 font-medium mb-2">Please correct the following errors:</h3>
                <ul class="text-red-700 list-disc list-inside space-y-1">
                    <?php foreach ($errors as $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Medical Progress Entry Edit Form -->
<div class="medical-card p-8">
    <form method="POST" class="space-y-8">
        <!-- Medical Treatment Description -->
        <div>
            <label for="treatment" class="block text-sm font-bold text-redolence-navy mb-3">
                Medical Treatment Description *
            </label>
            <input type="text" 
                   id="treatment" 
                   name="treatment" 
                   value="<?= htmlspecialchars($entry['treatment']) ?>"
                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                   placeholder="e.g., Botox injection, Dermal filler treatment, Laser therapy..."
                   required>
            <p class="mt-2 text-sm text-gray-600">Describe the medical treatment or procedure performed.</p>
        </div>

        <!-- Treatment Date -->
        <div>
            <label for="entry_date" class="block text-sm font-bold text-redolence-navy mb-3">
                Treatment Date *
            </label>
            <input type="date" 
                   id="entry_date" 
                   name="entry_date" 
                   value="<?= htmlspecialchars($entry['entry_date']) ?>"
                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all"
                   required>
            <p class="mt-2 text-sm text-gray-600">Date when the medical treatment was performed.</p>
        </div>

        <!-- Medical Progress Notes -->
        <div>
            <label for="notes" class="block text-sm font-bold text-redolence-navy mb-3">
                Medical Progress Notes *
            </label>
            <textarea id="notes" 
                      name="notes" 
                      rows="8"
                      class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl text-redolence-navy focus:outline-none focus:ring-2 focus:ring-redolence-green focus:border-redolence-green transition-all resize-none"
                      placeholder="Document patient progress, treatment outcomes, observations, side effects, patient response, and any follow-up recommendations..."
                      required><?= htmlspecialchars($entry['notes']) ?></textarea>
            <p class="mt-2 text-sm text-gray-600">Detailed medical notes about patient progress, treatment response, and clinical observations.</p>
        </div>

        <!-- HIPAA Compliance Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <div class="flex items-start">
                <svg class="w-6 h-6 text-blue-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                <div>
                    <h4 class="text-blue-800 font-semibold mb-2">HIPAA Compliance Notice</h4>
                    <p class="text-blue-700 text-sm">This medical progress entry contains protected health information (PHI). All information must be accurate, relevant to patient care, and handled in accordance with HIPAA privacy regulations. Only authorized medical personnel should access this information.</p>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
            <button type="submit" class="medical-btn-primary px-8 py-4 flex-1 sm:flex-none">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Update Medical Entry
            </button>
            <a href="<?= getBasePath() ?>/staff/progressive-reports/view.php?id=<?= $entry['report_id'] ?>" 
               class="medical-btn-secondary px-8 py-4 text-center flex-1 sm:flex-none">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Cancel Changes
            </a>
        </div>
    </form>
</div>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
