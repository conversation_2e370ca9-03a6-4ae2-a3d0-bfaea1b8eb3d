<?php
/**
 * Login Page - Redesigned
 * Redolence Medi Aesthetics
 */

require_once __DIR__ . '/../config/app.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'ADMIN') {
        redirect('/admin');
    } elseif ($user['role'] === 'STAFF') {
        redirect('/staff');
    } else {
        redirect('/customer');
    }
}

$error = '';
$success = '';

// Check for password reset success message
if (isset($_SESSION['password_reset_success'])) {
    $success = $_SESSION['password_reset_success'];
    unset($_SESSION['password_reset_success']);
}

// Get redirect parameter
$redirectUrl = $_GET['redirect'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $redirectUrl = sanitize($_POST['redirect'] ?? '');

    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } else {
        $result = $auth->login($email, $password);

        if ($result['success']) {
            $user = $result['user'];

            if (isset($result['requires_2fa']) && $result['requires_2fa']) {
                redirect('/admin/auth/verify-2fa.php');
            }

            if (!empty($redirectUrl) && $user['role'] === 'CUSTOMER') {
                $allowedPaths = ['/customer/', '/services.php', '/booking-policy.php'];
                $isValidRedirect = false;
                $decodedRedirectUrl = urldecode($redirectUrl);

                foreach ($allowedPaths as $allowedPath) {
                    if (strpos($decodedRedirectUrl, $allowedPath) !== false) {
                        $isValidRedirect = true;
                        break;
                    }
                }
                redirect($isValidRedirect ? $decodedRedirectUrl : '/customer');
            } else {
                if ($user['role'] === 'ADMIN') {
                    redirect('/admin');
                } elseif ($user['role'] === 'STAFF') {
                    redirect('/staff');
                } else {
                    redirect('/customer');
                }
            }
        } else {
            $error = $result['error'];
        }
    }
}

$pageTitle = "Login - Redolence Medi Aesthetics";
$pageDescription = "Access your Redolence account to manage appointments and view your treatment history.";

include __DIR__ . '/../includes/header.php';
?>

<main class="auth-page-container bg-gradient-to-br from-gray-50 to-redolence-green/10 min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="bg-white rounded-3xl shadow-2xl p-8 lg:p-10 border border-gray-100">
            <div class="text-center">
                <a href="<?= getBasePath() ?>/" class="inline-block mb-6">
                    <img class="mx-auto h-12 w-auto" src="<?= getBasePath() ?>/assets/images/logo-green.png" alt="Redolence Logo">
                </a>
                <h2 class="text-3xl font-extrabold text-gray-900">
                    Welcome Back
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    Sign in to continue your journey with us.
                </p>
            </div>

            <?php if ($error): ?>
                <div class="mt-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-lg" role="alert">
                    <p><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="mt-6 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-lg" role="alert">
                    <p><?= htmlspecialchars($success) ?></p>
                </div>
            <?php endif; ?>

            <form class="mt-8 space-y-6" method="POST" autocomplete="off" novalidate>
                <input type="hidden" name="redirect" value="<?= htmlspecialchars($redirectUrl) ?>">
                <div class="rounded-md shadow-sm -space-y-px">
                    <div class="mb-4">
                        <label for="email" class="sr-only">Email address</label>
                        <input id="email" name="email" type="email" autocomplete="email" required
                               class="appearance-none rounded-lg relative block w-full px-4 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-redolence-green focus:border-redolence-green focus:z-10 sm:text-sm"
                               placeholder="Email address" value="<?= isset($email) ? htmlspecialchars($email) : '' ?>">
                    </div>
                    <div class="relative">
                        <label for="password" class="sr-only">Password</label>
                        <input id="password" name="password" type="password" autocomplete="current-password" required
                               class="appearance-none rounded-lg relative block w-full px-4 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-redolence-green focus:border-redolence-green focus:z-10 sm:text-sm"
                               placeholder="Password">
                        <button type="button" onclick="togglePasswordVisibility()" class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5">
                            <i id="password-eye" class="fas fa-eye text-gray-500"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-sm">
                        <a href="<?= getBasePath() ?>/auth/forgot-password.php" class="font-medium text-redolence-green hover:text-redolence-dark-green">
                            Forgot your password?
                        </a>
                    </div>
                </div>

                <div>
                    <button type="submit"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-redolence-green hover:bg-redolence-dark-green focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-redolence-green transition-colors duration-300">
                        Sign In
                    </button>
                </div>
            </form>

            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    Don't have an account?
                    <a href="<?= getBasePath() ?>/auth/register.php<?= !empty($redirectUrl) ? '?redirect=' . urlencode($redirectUrl) : '' ?>"
                       class="font-medium text-redolence-green hover:text-redolence-dark-green">
                        Sign up here
                    </a>
                </p>
            </div>
        </div>
    </div>
</main>

<script>
    function togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const eyeIcon = document.getElementById('password-eye');
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeIcon.classList.remove('fa-eye');
            eyeIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            eyeIcon.classList.remove('fa-eye-slash');
            eyeIcon.classList.add('fa-eye');
        }
    }
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>