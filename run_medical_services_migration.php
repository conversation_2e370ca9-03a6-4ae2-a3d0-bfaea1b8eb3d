<?php
/**
 * Medical Services Enhancement Migration Script
 * Run this script to apply the medical services enhancements to your database
 */

require_once __DIR__ . '/config/app.php';

// Check if user is admin
if (!isset($auth) || !$auth->hasRole('ADMIN')) {
    die("Error: Admin access required to run database migrations.");
}

echo "<h1>Medical Services Enhancement Migration</h1>";
echo "<p>This script will enhance your services table for medical treatments.</p>";

try {
    // Read the migration SQL file
    $migrationFile = __DIR__ . '/database/simple_medical_services_enhancement.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    // Remove the USE database statement since we're already connected
    $sql = preg_replace('/USE\s+\w+;\s*/', '', $sql);
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "<h2>Executing Migration...</h2>";
    echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $database->query($statement);
            echo "<p style='color: green;'>✓ Executed: " . substr($statement, 0, 100) . "...</p>";
            $successCount++;
        } catch (Exception $e) {
            // Check if it's a "column already exists" error (which is OK)
            if (strpos($e->getMessage(), 'Duplicate column name') !== false || 
                strpos($e->getMessage(), 'already exists') !== false) {
                echo "<p style='color: orange;'>⚠ Skipped (already exists): " . substr($statement, 0, 100) . "...</p>";
            } else {
                echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
                echo "<p style='color: red;'>Statement: " . substr($statement, 0, 200) . "...</p>";
                $errorCount++;
            }
        }
    }
    
    echo "</div>";
    
    echo "<h2>Migration Summary</h2>";
    echo "<p><strong>Successful operations:</strong> $successCount</p>";
    echo "<p><strong>Errors:</strong> $errorCount</p>";
    
    if ($errorCount === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>✅ Migration Completed Successfully!</h3>";
        echo "<p>Your services table has been enhanced with medical treatment features:</p>";
        echo "<ul>";
        echo "<li>✓ Added session_frequency field</li>";
        echo "<li>✓ Added technology_used field</li>";
        echo "<li>✓ Added treatment badges (featured, popular, new_treatment)</li>";
        echo "<li>✓ Added sort_order field</li>";
        echo "<li>✓ Made price and duration optional</li>";
        echo "<li>✓ Added sample medical treatments</li>";
        echo "</ul>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ol>";
        echo "<li>Visit <a href='" . getBasePath() . "/admin/services'>Admin Services</a> to see the new interface</li>";
        echo "<li>Review the sample treatments that were added</li>";
        echo "<li>Start adding your own medical treatments with rich descriptions</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>⚠ Migration Completed with Errors</h3>";
        echo "<p>Some operations failed. Please review the errors above and contact support if needed.</p>";
        echo "</div>";
    }
    
    // Test the new structure
    echo "<h2>Testing New Structure</h2>";
    echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    
    try {
        $testQuery = "SELECT COUNT(*) as count, 
                             SUM(CASE WHEN featured = 1 THEN 1 ELSE 0 END) as featured_count,
                             SUM(CASE WHEN popular = 1 THEN 1 ELSE 0 END) as popular_count,
                             SUM(CASE WHEN new_treatment = 1 THEN 1 ELSE 0 END) as new_count
                      FROM services";
        $result = $database->fetch($testQuery);
        
        echo "<p><strong>Services Statistics:</strong></p>";
        echo "<ul>";
        echo "<li>Total Services: " . $result['count'] . "</li>";
        echo "<li>Featured Treatments: " . $result['featured_count'] . "</li>";
        echo "<li>Popular Treatments: " . $result['popular_count'] . "</li>";
        echo "<li>New Treatments: " . $result['new_count'] . "</li>";
        echo "</ul>";
        
        // Show sample of enhanced services
        $sampleServices = $database->fetchAll("SELECT name, technology_used, session_frequency, featured, popular, new_treatment FROM services WHERE technology_used IS NOT NULL LIMIT 3");
        
        if (!empty($sampleServices)) {
            echo "<p><strong>Sample Enhanced Treatments:</strong></p>";
            foreach ($sampleServices as $service) {
                echo "<div style='background: white; padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #49a75c;'>";
                echo "<strong>" . htmlspecialchars($service['name']) . "</strong><br>";
                if ($service['technology_used']) echo "Technology: " . htmlspecialchars($service['technology_used']) . "<br>";
                if ($service['session_frequency']) echo "Frequency: " . htmlspecialchars($service['session_frequency']) . "<br>";
                $badges = [];
                if ($service['featured']) $badges[] = "⭐ Featured";
                if ($service['popular']) $badges[] = "🔥 Popular";
                if ($service['new_treatment']) $badges[] = "✨ New";
                if (!empty($badges)) echo "Badges: " . implode(", ", $badges);
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error testing structure: " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ Migration Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='" . getBasePath() . "/admin/services'>← Back to Services Admin</a></p>";
echo "<p><a href='" . getBasePath() . "/admin'>← Back to Admin Dashboard</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #49a75c;
}
code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
</style>
